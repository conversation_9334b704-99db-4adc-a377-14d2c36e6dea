package com.facishare.open.feishu.sync.service.impl;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.feishu.syncapi.model.connect.BaseConnectParams;
import com.facishare.open.feishu.syncapi.model.connect.FeishuAppConnectParams;
import com.facishare.open.feishu.sync.manager.*;
import com.facishare.open.feishu.sync.mongo.document.OaConnectorOutDepartmentInfoDoc;
import com.facishare.open.feishu.sync.mongo.document.OaConnectorOutUserInfoDoc;
import com.facishare.open.feishu.sync.mongo.document.OaConnectorSyncEventDataDoc;
import com.facishare.open.feishu.sync.threadpool.ThreadPoolHelper;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.entity.DepartmentBindEntity;
import com.facishare.open.feishu.syncapi.entity.EmployeeBindEntity;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.enums.BindStatusEnum;
import com.facishare.open.feishu.syncapi.enums.BindTypeEnum;
import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.feishu.syncapi.model.ContactScopeModel;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.result.data.ContactScopeData;
import com.facishare.open.feishu.syncapi.result.data.DepartmentData;
import com.facishare.open.feishu.syncapi.result.data.UserData;
import com.facishare.open.feishu.syncapi.service.*;
import com.facishare.open.feishu.syncapi.arg.QueryOaConnectorSyncEventDataArg;
import com.facishare.open.order.contacts.proxy.api.arg.FsDeptArg;
import com.facishare.open.order.contacts.proxy.api.arg.FsEmpArg;
import com.facishare.open.order.contacts.proxy.api.consts.GlobalValue;
import com.facishare.open.order.contacts.proxy.api.enums.FsEmployeeRoleCodeEnum;
import com.facishare.open.order.contacts.proxy.api.service.FsContactsServiceProxy;
import com.facishare.open.order.contacts.proxy.api.service.FsDepartmentServiceProxy;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.result.DeleteResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service("contactsService")
public class ContactsServiceImpl implements ContactsService {
    @Resource
    private FsContactsServiceProxy fsContactsServiceProxy;
    @Resource
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;
    @Resource
    private FsDepartmentServiceProxy fsDepartmentServiceProxy;
    @Resource
    private FeishuContactsService feishuContactsService;
    @Resource
    private FeishuDepartmentService feishuDepartmentService;
    @Resource
    private FeishuUserService feishuUserService;
    @Resource
    private FeishuAppService feishuAppService;
    @Resource
    private EnterpriseBindManager enterpriseBindManager;
    @Resource
    private DepartmentBindManager departmentBindManager;
    @Resource
    private EmployeeBindManager employeeBindManager;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private OaConnectorSyncEventDataManager oaConnectorSyncEventDataManager;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private OaConnectorOutUserInfoManager oaConnectorOutUserInfoManager;
    @Resource
    private OaConnectorOutDepartmentInfoManager oaConnectorOutDepartmentInfoManager;
    @Resource
    private EmployeeBindService employeeBindService;
    @Resource
    private EnterpriseBindService enterpriseBindService;

    private static final List<String> refreshContactScopeDataEventTypes = Lists.newArrayList("contact.department.created_v3","contact.department.updated_v3","contact.scope.updated_v3","contact.user.created_v3","contact.user.updated_v3");
    private final String GET_CONTACT_SCOPE_CACHE_KEY = "get_contact_scope_cache_key_";

    private List<DepartmentData.Department> getAllDepartmentList(String appId, String outEa, List<String> outDepIdList) {
        List<DepartmentData.Department> departmentList = new ArrayList<>();
        if(CollectionUtils.isEmpty(outDepIdList)) return departmentList;

        //拉取所有部门信息
        for (String depId : outDepIdList) {
            Result<DepartmentData.Department> deptInfo = feishuDepartmentService.getDeptInfo(appId, outEa, depId);
            if (deptInfo.isSuccess()) {
                departmentList.add(deptInfo.getData());
                List<DepartmentData.Department> childDept = getAllChildDepartmentList(appId, outEa, depId);
                if (CollectionUtils.isNotEmpty(childDept)) {
                    departmentList.addAll(childDept);
                }
            }
        }

        LogUtils.info("ContactsServiceImpl.getAllDepartmentList,departmentList.size={}",
                departmentList.size());

        return departmentList;
    }

    private List<DepartmentData.Department> getAllDepartmentList2(String appId, String outEa, List<DepartmentData.Department> departmentList) {
        List<DepartmentData.Department> totalDepartmentList = new ArrayList<>();

        for (DepartmentData.Department department : departmentList) {
            totalDepartmentList.add(department);

            List<DepartmentData.Department> childDepartmentList = getAllChildDepartmentList(appId, outEa, department.getOpenDepartmentId());
            if (CollectionUtils.isNotEmpty(childDepartmentList)) {
                totalDepartmentList.addAll(childDepartmentList);
            }
        }

        LogUtils.info("ContactsServiceImpl.getAllDepartmentList,totalDepartmentList.size={},totalDepartmentList={}",
                totalDepartmentList.size(), totalDepartmentList);

        return totalDepartmentList;
    }

    private List<DepartmentData.Department> getAllChildDepartmentList(String appId, String outEa, String depId) {
        List<DepartmentData.Department> departmentList = new ArrayList<>();

        Result<List<DepartmentData.Department>> result = feishuDepartmentService.getChildDept(appId, outEa, depId, true);
        if (result.isSuccess() && CollectionUtils.isNotEmpty(result.getData())) {
            departmentList.addAll(result.getData());
        }

        return departmentList;
    }

    @Override
    public Result<Void> initContactsAsync(String fsEa) {
        Executors.newScheduledThreadPool(1).schedule(()->{
            initContacts(fsEa);
        },3 * 1000L, TimeUnit.MILLISECONDS);
        return Result.newSuccess();
    }

    private void initContacts(String fsEa) {
        Integer ei = eieaConverter.enterpriseAccountToId(fsEa);

        EnterpriseBindEntity entity = enterpriseBindManager.getEntity(fsEa);
        String appId=ObjectUtils.isNotEmpty(entity.getAppId())?entity.getAppId():ConfigCenter.feishuCrmAppId;

        Result<ContactScopeData> result = feishuContactsService.getContactScopeData(appId,
                entity.getOutEa());
        LogUtils.info("ContactsServiceImpl.initContactsAsync,get contact scope dat,result={}", result);
        if (result.isSuccess() == false) {
            return;
        }

        //拉取所有部门包括子部门信息
        List<DepartmentData.Department> departmentList = getAllDepartmentList(appId, entity.getOutEa(), result.getData().getDepartmentIds());

        initDepList(ei, fsEa, entity, departmentList);

        initUserList(ei, fsEa, entity, result.getData().getUserIds(), departmentList);

        updateAdminMainDep(appId,ei,fsEa,entity.getOutEa());
    }

    @Override
    public Result<ContactScopeModel> getContactScopeData(String appId, String outEa) {
        Result<ContactScopeData> result = feishuContactsService.getContactScopeData(appId, outEa);
        LogUtils.info("ContactsServiceImpl.initContactsAsync,get contact scope dat,result={}", result);
        if (result.isSuccess() == false) {
            return new Result<>(result.getCode(),result.getMsg(),null);
        }

        //拉取所有部门包括子部门信息
        List<DepartmentData.Department> departmentList = getAllDepartmentList(appId, outEa, result.getData().getDepartmentIds());

        List<UserData.User> userList = batchGetDepartmentUserList(appId, outEa, departmentList);
        //userIds为空直接跳过
        if(CollectionUtils.isNotEmpty(result.getData().getUserIds())) {
            userList.addAll(batchGetUserList(appId,outEa,result.getData().getUserIds()));
        }

        ContactScopeModel model = new ContactScopeModel();
        model.setUserList(userList);
        model.setDepartmentList(departmentList);

        return Result.newSuccess(model);
    }

    @Override
    public Result<Void> addUserList(String appId, String outEa, List<UserData.User> userList) {
        List<EnterpriseBindEntity> enterpriseBindList = enterpriseBindManager.getEnterpriseBindList(outEa);
        if(CollectionUtils.isEmpty(enterpriseBindList)) {
            return Result.newError(ResultCodeEnum.OUT_EA_NOT_BIND);
        }
        EnterpriseBindEntity enterpriseBindEntity = enterpriseBindList.get(0);

        String fsEa = enterpriseBindEntity.getFsEa();
        int ei = eieaConverter.enterpriseAccountToId(fsEa);

        for (UserData.User user : userList) {
            EmployeeBindEntity employeeBindEntity = employeeBindManager.getEntity(outEa, user.getOpenId(), null);
            if (employeeBindEntity == null) {
                //员工不存在，新增员工并更新员工绑定表
                addUser(appId,ei, fsEa, outEa, user);
            } else {
                //员工存在，更新纷享员工状态并更新员工绑定状态
                com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsEmployeeServiceProxy.toggle(ei + "",
                        employeeBindEntity.getFsUserId(),
                        true,
                        Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
                        FsEmployeeRoleCodeEnum.SALES.getRoleCode());
                LogUtils.info("ContactsServiceImpl.addUserList,result={}", result);
                employeeBindManager.batchUpdateBindStatus(fsEa, Lists.newArrayList(employeeBindEntity.getFsUserId()), BindStatusEnum.normal,outEa);
            }
        }

        return Result.newSuccess();
    }

    @Override
    public Result<Void> removeUserList(String appId, String outEa, List<UserData.User> userList) {
        List<EnterpriseBindEntity> enterpriseBindList = enterpriseBindManager.getEnterpriseBindList(outEa);
        if(CollectionUtils.isEmpty(enterpriseBindList)) {
            Result.newError(ResultCodeEnum.OUT_EA_NOT_BIND);
        }
        EnterpriseBindEntity enterpriseBindEntity = enterpriseBindList.get(0);

        String fsEa = enterpriseBindEntity.getFsEa();
        int ei = eieaConverter.enterpriseAccountToId(fsEa);

        for (UserData.User user : userList) {
            EmployeeBindEntity employeeBindEntity = employeeBindManager.getEntity(outEa, user.getOpenId(), null);
            //1000号员工不被停用
            if (employeeBindEntity == null || employeeBindEntity.getFsUserId().equals("1000")) {
                continue;
            }

            //1.停用纷享员工
            com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsEmployeeServiceProxy.toggle(ei + "",
                    employeeBindEntity.getFsUserId(),
                    false,
                    null,null);
            LogUtils.info("ContactsServiceImpl.removeUserList,result={}", result);
            //2.更新员工绑定状态为停用
            employeeBindManager.batchUpdateBindStatus(fsEa, Lists.newArrayList(employeeBindEntity.getFsUserId()), BindStatusEnum.stop,outEa);
        }

        return Result.newSuccess();
    }

    @Override
    public Result<Void> addDepList(String appId, String outEa, List<DepartmentData.Department> departmentList) {
        List<EnterpriseBindEntity> enterpriseBindList = enterpriseBindManager.getEnterpriseBindList(outEa);
        EnterpriseBindEntity enterpriseBindEntity = enterpriseBindList.get(0);

        String fsEa = enterpriseBindEntity.getFsEa();
        int ei = eieaConverter.enterpriseAccountToId(fsEa);

        for (DepartmentData.Department department : departmentList) {
            DepartmentBindEntity departmentBindEntity = departmentBindManager.getEntityByOutEa(outEa, department.getOpenDepartmentId());
            List<DepartmentData.Department> allDepList = getAllDepartmentList2(appId, outEa, Lists.newArrayList(department));
            List<UserData.User> departmentUserList = batchGetDepartmentUserList(appId, outEa, allDepList);

            if (departmentBindEntity == null) {
                //1. 创建纷享部门包括子部门并更新部门绑定表
                batchAddDepartment(ei, fsEa, outEa, allDepList);
                //2. 创建纷享员工并更新员工绑定表
                addUserList(appId, outEa, departmentUserList);
            } else {
                //1. 批量启用当前部门及子部门纷享员工
                batchResumeFsEmployee(ei, appId, fsEa, outEa, department);

                //2. 启用当前纷享部门及子部门
                batchResumeFsDepartment(ei, appId, fsEa, outEa, department);

                //3. 更新部门下所有纷享员工的部门
                updateUserMainDep(ei,fsEa,outEa,departmentUserList);
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> removeDepList(String appId, String outEa, List<DepartmentData.Department> departmentList) {
        List<EnterpriseBindEntity> enterpriseBindList = enterpriseBindManager.getEnterpriseBindList(outEa);
        EnterpriseBindEntity enterpriseBindEntity = enterpriseBindList.get(0);

        String fsEa = enterpriseBindEntity.getFsEa();
        int ei = eieaConverter.enterpriseAccountToId(fsEa);

        for (DepartmentData.Department department : departmentList) {
            DepartmentBindEntity departmentBindEntity = departmentBindManager.getEntityByOutEa(outEa, department.getOpenDepartmentId());
            if (departmentBindEntity == null) {
                continue;
            }

            //1. 批量停用当前部门及子部门纷享员工并更新绑定表为已停用
            batchStopFsEmployee(ei, fsEa, departmentBindEntity.getFsDepId(),outEa);

            //2. 批量停用当前纷享部门及子部门并更新部门绑定表为已停用
            batchStopFsDepartment(ei, fsEa, departmentBindEntity.getFsDepId(),outEa);
        }

        return Result.newSuccess();
    }

    private void batchStopFsEmployee(int ei, String fsEa, String fsDepId, String outEa) {
//        com.facishare.open.order.contacts.proxy.api.result.Result<List<ObjectData>> listResult = fsEmployeeServiceProxy.list(ei,fsDepId);
//        LogUtils.info("ContactsServiceImpl.batchStopEmployee,listResult={}", listResult);
//        if(listResult.isSuccess()==false) return;
//
//        List<String> fsUserIdList = listResult.getData().stream().map(ObjectData::getId).collect(Collectors.toList());
//
//        //1.批量停用当前部门纷享员工
//        com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsEmployeeServiceProxy.bulkStop(ei + "",
//                fsUserIdList);
//        LogUtils.info("ContactsServiceImpl.batchStopEmployee,result={}", result);
        com.facishare.open.order.contacts.proxy.api.result.Result<List<String>> batchStopFsEmp = fsContactsServiceProxy.batchStopFsEmp(ei, fsEa, fsDepId,null);
        //2.批量更新员工绑定状态为停用
        if(batchStopFsEmp.isSuccess()) {
            employeeBindManager.batchUpdateBindStatus(fsEa, batchStopFsEmp.getData(), BindStatusEnum.stop,outEa);
        }
    }

    private void batchStopFsDepartment(int ei, String fsEa, String fsDepId, String outEa) {
//        com.facishare.open.order.contacts.proxy.api.result.Result<List<ObjectData>> listResult = fsDepartmentServiceProxy.list(ei, fsDepId);
//        LogUtils.info("ContactsServiceImpl.batchStopFsDepartment,listResult={}", listResult);
//        if(listResult.isSuccess()==false) return;
//
//        List<String> fsDepIdList = listResult.getData().stream().map(ObjectData::getId).collect(Collectors.toList());
//        //从子部门到根部门依次排序
//        Collections.reverse(fsDepIdList);
//
//        for(String depId : fsDepIdList) {
//            //1.先停用最小的子部门，再依次停用父部门
//            com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsDepartmentServiceProxy.toggle(ei + "",
//                    depId,false);
//            LogUtils.info("ContactsServiceImpl.batchStopFsDepartment,result={}", result);
//        }

        com.facishare.open.order.contacts.proxy.api.result.Result<List<String>> batchStopFsDep = fsContactsServiceProxy.batchStopFsDep(ei, fsEa, fsDepId);
        //2.批量更新部门绑定状态为停用
        if(batchStopFsDep.isSuccess()) {
            departmentBindManager.batchUpdateBindStatus(fsEa, batchStopFsDep.getData(), BindStatusEnum.stop, outEa);
        }
    }

    private void batchResumeFsEmployee(int ei, String appId, String fsEa, String outEa, DepartmentData.Department department) {
        //获取当前部门的所有子部门
        List<DepartmentData.Department> departmentList = getAllDepartmentList2(appId, outEa, Lists.newArrayList(department));
        List<String> fsUserIdList = getFsUserIdList(appId, fsEa, outEa, departmentList);

        //1.批量启用当前部门纷享员工
        com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsEmployeeServiceProxy.bulkResume(ei + "",
                fsUserIdList,
                Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
                FsEmployeeRoleCodeEnum.SALES.getRoleCode());
        LogUtils.info("ContactsServiceImpl.batchResumeFsEmployee,result={}", result);
        //2.批量更新员工绑定状态为正常
        employeeBindManager.batchUpdateBindStatus(fsEa, fsUserIdList, BindStatusEnum.normal,outEa);
    }

    private void batchResumeFsDepartment(int ei, String appId, String fsEa, String outEa, DepartmentData.Department department) {
        List<String> fsDepIdList = getFsDepIdList(appId, outEa, Lists.newArrayList(department));

//        //1.批量启用当前部门及子部门
//        for(String depId : fsDepIdList) {
//            com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsDepartmentServiceProxy.toggle(ei + "",
//                    depId,true);
//            LogUtils.info("ContactsServiceImpl.toggle,enable department,result={}", result);
//        }

        com.facishare.open.order.contacts.proxy.api.result.Result<List<String>> batchResumeFsDep = fsContactsServiceProxy.batchResumeFsDep(ei, fsEa, fsDepIdList);

        //2.批量更新部门绑定状态为正常
        if(batchResumeFsDep.isSuccess()) {
            departmentBindManager.batchUpdateBindStatus(fsEa, batchResumeFsDep.getData(), BindStatusEnum.normal,outEa);
        }
    }

//    @Override
//    public Result<Void> resetAdminRole(String appId, String outEa) {
//        Result<GetAppAdminUserListData> result = feishuAppService.getAdminUserList(appId, outEa);
//        if(result.isSuccess()==false) {
//            return new Result(result.getCode(),result.getMsg(),null);
//        }
//
//        List<EnterpriseBindEntity> enterpriseBindList = enterpriseBindManager.getEnterpriseBindList(outEa);
//        EnterpriseBindEntity enterpriseBindEntity = enterpriseBindList.get(0);
//
//        String fsEa = enterpriseBindEntity.getFsEa();
//        int ei = eieaConverter.enterpriseAccountToId(fsEa);
//
//        List<String> openUserIdList = result.getData().getUserList().stream()
//                .map(UserData.UserId::getOpenId)
//                .collect(Collectors.toList());
//        List<EmployeeBindEntity> entityList = employeeBindManager.getEntityList(outEa, openUserIdList, fsEa);
//        List<String> fsUserIdList = entityList.stream()
//                .map(EmployeeBindEntity::getFsUserId)
//                .collect(Collectors.toList());
//        LogUtils.info("ContactsServiceImpl.resetAdminRole,fsUserIdList={}",fsUserIdList);
//        if(CollectionUtils.isEmpty(fsUserIdList)) {
//            return Result.newError(ResultCodeEnum.APP_ADMIN_NOT_BIND);
//        }
//
//        int failedCount = 0;
//        List<String> successList = new ArrayList<>();
//        for(String empId : fsUserIdList) {
//            com.facishare.open.order.contacts.proxy.api.result.Result<Void> addManagerRole = fsEmployeeServiceProxy.addManagerRole(ei,
//                    Lists.newArrayList(Integer.valueOf(empId)));
//            LogUtils.info("ContactsServiceImpl.resetAdminRole,empId={},addManagerRole={}",empId,addManagerRole);
//            if(addManagerRole.isSuccess()) {
//                successList.add(empId);
//            } else {
//                failedCount++;
//            }
//        }
//        //批量给系统管理员添加CRM管理员和销售员角色
//        if(CollectionUtils.isNotEmpty(successList)) {
//            com.facishare.open.order.contacts.proxy.api.result.Result<Void> batchAddUserRole = fsEmployeeServiceProxy.batchAddUserRole(ei,
//                    successList,
//                    Lists.newArrayList(FsEmployeeRoleCodeEnum.CRM_MANAGER.getRoleCode(), FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
//                    FsEmployeeRoleCodeEnum.CRM_MANAGER.getRoleCode());
//            LogUtils.info("ContactsServiceImpl.resetAdminRole,successList={},batchAddUserRole={}",successList,batchAddUserRole);
//        }
//        //如果管理员赋权全部失败，刚重启所有管理员并添加系统管理员权限
//        if(failedCount==fsUserIdList.size()) {
//            com.facishare.open.order.contacts.proxy.api.result.Result<Void> bulkResume = fsEmployeeServiceProxy.bulkResume(ei + "",
//                    fsUserIdList,
//                    Lists.newArrayList(FsEmployeeRoleCodeEnum.CRM_MANAGER.getRoleCode(),FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
//                    FsEmployeeRoleCodeEnum.CRM_MANAGER.getRoleCode());
//            LogUtils.info("ContactsServiceImpl.resetAdminRole,fsUserIdList={},bulkResume={}",fsUserIdList,bulkResume);
//            employeeBindManager.batchUpdateBindStatus(fsEa, fsUserIdList, BindStatusEnum.normal);
//
//            List<Integer> fsUserIdList2 = fsUserIdList.stream()
//                    .map(item->Integer.valueOf(item))
//                    .collect(Collectors.toList());
//
//            com.facishare.open.order.contacts.proxy.api.result.Result<Void> addManagerRole = fsEmployeeServiceProxy.addManagerRole(ei,
//                    fsUserIdList2);
//            LogUtils.info("ContactsServiceImpl.resetAdminRole,fsUserIdList2={},addManagerRole={}",fsUserIdList2,addManagerRole);
//        }
//
//        return Result.newSuccess();
//    }

    /**
     * 初始化纷享用户
     *
     * @param entity
     * @param userIdList
     * @param departmentList
     */
    private List<UserData.User> initUserList(Integer ei,
                                             String fsEa,
                                             EnterpriseBindEntity entity,
                                             List<String> userIdList,
                                             List<DepartmentData.Department> departmentList) {
        String appId = ObjectUtils.isNotEmpty(entity.getAppId())?entity.getAppId():ConfigCenter.feishuCrmAppId;

        List<UserData.User> totalUserList = new ArrayList<>();
        //1.拉取应用可见范围飞书用户信息
        if (CollectionUtils.isNotEmpty(userIdList)) {
            totalUserList.addAll(batchGetUserList(appId, entity.getOutEa(), userIdList));
        }

        //2.拉取应用可见范围内部门用户信息
        List<UserData.User> userList = batchGetDepartmentUserList(appId, entity.getOutEa(), departmentList);
        if (CollectionUtils.isNotEmpty(userList)) {
            totalUserList.addAll(userList);
        }

        //3.创建CRM员工并入库
        batchAddUser(appId,ei, fsEa, entity.getOutEa(), totalUserList);

        //4.查询当前企业根部门下所有员工并更新负责人信息
        com.facishare.open.order.contacts.proxy.api.result.Result<List<ObjectData>> listResult = fsEmployeeServiceProxy.listAll(ei,
                GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"");
        if (listResult.isSuccess() && CollectionUtils.isNotEmpty(listResult.getData())) {
            for (ObjectData objectData : listResult.getData()) {
                EmployeeBindEntity employeeBindEntity = employeeBindManager.getEntityByFsUserId(fsEa, objectData.getId(),entity.getOutEa());
                LogUtils.info("ContactsServiceImpl.initUserList,employeeBindEntity={}", employeeBindEntity);
                if (employeeBindEntity == null) continue;

                EmployeeBindEntity leaderBinderEntity = null;
                if (StringUtils.isNotEmpty(employeeBindEntity.getOutLeaderUserId())) {
                    leaderBinderEntity = employeeBindManager.getEntity(employeeBindEntity.getOutEa(),
                            employeeBindEntity.getOutLeaderUserId(), fsEa);
                    LogUtils.info("ContactsServiceImpl.initUserList,leaderBinderEntity={}", leaderBinderEntity);
                }
                if (leaderBinderEntity == null) continue;

                FsEmpArg arg = FsEmpArg.builder()
                        .ei(ei + "")
                        .id(objectData.getId())
                        .leader(Lists.newArrayList(leaderBinderEntity.getFsUserId()))
                        .build();
                //更新纷享员工负责人
                com.facishare.open.order.contacts.proxy.api.result.Result<Void> updateResult = fsEmployeeServiceProxy.update(arg,
                        null,
                        null);
                LogUtils.info("ContactsServiceImpl.initUserList,update employee leader,updateResult={}", updateResult);
                if (updateResult.isSuccess() == false) {
                    LogUtils.info("ContactsServiceImpl.initUserList,update employee leader failed,arg={},updateResult={}",
                            arg, updateResult);
                }
            }
        }
        return totalUserList;
    }

    /**
     * 批量新增纷享员工并插入中间表
     *
     * @param ei
     * @param fsEa
     * @param outEa
     * @param userList
     */
    private void batchAddUser(String appId,Integer ei,
                              String fsEa,
                              String outEa,
                              List<UserData.User> userList) {
        for (UserData.User user : userList) {
            addUser(appId,ei, fsEa, outEa, user);
        }
    }

    private void addUser(String appId,Integer ei,
                         String fsEa,
                         String outEa,
                         UserData.User user) {
        //先判断数据库里是否已有该用户的绑定关系
        EmployeeBindEntity employeeBindManagerEntity = employeeBindManager.getEntity(outEa, user.getOpenId(), fsEa);
        if(ObjectUtils.isNotEmpty(employeeBindManagerEntity)) {
            LogUtils.info("ContactsServiceImpl.addUser,select employee mapping,fsEa={},outEa={},user", fsEa, outEa, user);
            return;
        }
        String sex = null;
        if (user.getGender() == 1) {
            sex = "M";
        } else if (user.getGender() == 2) {
            sex = "F";
        }

        if(CollectionUtils.isEmpty(user.getDepartment_ids())) {
            List<UserData.User> userList = batchGetUserList(appId,
                    outEa,
                    Lists.newArrayList(user.getOpenId()));
            user.setDepartment_ids(userList.get(0).getDepartment_ids());
        }

        List<String> fsDepIdList = getFsDepIdList(outEa, user.getDepartment_ids());
        List<String> mainDepIdList = CollectionUtils.isEmpty(fsDepIdList) ?
                Lists.newArrayList(GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"") : fsDepIdList;

        FsEmpArg arg = FsEmpArg.builder()
                .ei(ei + "")
                .name(StringUtils.isEmpty(user.getNickname()) ? user.getName() : user.getNickname())
                .fullName(user.getName())
                .sex(sex)
                .phone(user.getMobile()) //商店应用获取不到值，永远为空
                .mainDepartment(mainDepIdList)
                .viceDepartments(new ArrayList<>())
                .status("0")
                .isActive(true)
                .build();
        LogUtils.info("ContactsServiceImpl.addUser,create,arg={}", arg);
        com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> result = fsEmployeeServiceProxy.create(arg,
                Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
                FsEmployeeRoleCodeEnum.SALES.getRoleCode());
        LogUtils.info("ContactsServiceImpl.addUser,create,result={}", result);
        if (result.isSuccess()) {
            EmployeeBindEntity employeeBindEntity = EmployeeBindEntity.builder()
                    .channel(ChannelEnum.feishu)
                    .fsEa(fsEa)
                    .fsUserId(result.getData().getId())
                    .outEa(outEa)
                    .outUserId(user.getOpenId())
                    .outLeaderUserId(user.getLeader_user_id())
                    .bindStatus(BindStatusEnum.normal)
                    .bindType(BindTypeEnum.auto)
                    .build();
            int count = employeeBindManager.insert(employeeBindEntity);
            LogUtils.info("ContactsServiceImpl.addUser,insert employee mapping,count={}", count);
        } else {
            LogUtils.info("ContactsServiceImpl.addUser,create fs user failed", result);
        }
    }

    private List<UserData.User> batchGetUserList(String appId, String outEa, List<String> userList) {
        List<UserData.User> totalUserList = new ArrayList<>();
        for (String userId : userList) {
            Result<UserData.User> userInfo = feishuUserService.getUserInfo(appId, outEa, userId);
            if (userInfo.isSuccess()) {
                totalUserList.add(userInfo.getData());
            }
        }
        return totalUserList;
    }

    private List<UserData.User> getDepartmentUserList(String appId, String outEa, DepartmentData.Department department) {
        List<UserData.User> userList = new ArrayList<>();
        Result<List<UserData.User>> userListResult = feishuUserService.getUserList(appId, outEa, department.getOpenDepartmentId());
        if (userListResult.isSuccess() && CollectionUtils.isNotEmpty(userListResult.getData())) {
            userList.addAll(userListResult.getData());
        }
        return userList;
    }

    private List<UserData.User> batchGetDepartmentUserList(String appId, String outEa, List<DepartmentData.Department> departmentList) {
        List<UserData.User> totalUserList = new ArrayList<>();
        for (DepartmentData.Department department : departmentList) {
            List<UserData.User> userList = getDepartmentUserList(appId, outEa, department);
            if (CollectionUtils.isNotEmpty(userList)) {
                totalUserList.addAll(userList);
            }
        }
        return totalUserList;
    }

    /**
     * 初始化纷享部门
     *
     * @param ei
     * @param fsEa
     * @param entity
     * @param departmentList
     */
    private void initDepList(Integer ei,
                             String fsEa,
                             EnterpriseBindEntity entity,
                             List<DepartmentData.Department> departmentList) {
        batchAddDepartment(ei, fsEa, entity.getOutEa(), departmentList);
    }

    private void batchAddDepartment(Integer ei,
                                    String fsEa,
                                    String outEa,
                                    List<DepartmentData.Department> departmentList) {
        for (DepartmentData.Department department : departmentList) {
            addDepartment(ei, fsEa, outEa, department);
        }
    }

    private void addDepartment(Integer ei,
                               String fsEa,
                               String outEa,
                               DepartmentData.Department department) {
        //1.查询父部门绑定信息
        DepartmentBindEntity parentDepEntity = departmentBindManager.getEntityByFsEa(fsEa, department.getParentDepartmentId());
        LogUtils.info("ContactsServiceImpl.initDepList,parentDepEntity={}", parentDepEntity);
        List<String> parentIdList = null;
        String fsLeaderUserId = null;
        if (parentDepEntity != null) {
            parentIdList = Lists.newArrayList(parentDepEntity.getFsDepId());
        } else {
            parentIdList = Lists.newArrayList(GlobalValue.ALL_COMPANY_DEPARTMENT_ID + "");
        }
        //2.查询飞书部门Leader对应的纷享userId
        if (StringUtils.isNotEmpty(department.getLeaderUserId())) {
            EmployeeBindEntity leaderBindEntity = employeeBindManager.getEntity(outEa, department.getLeaderUserId(), fsEa);
            if (leaderBindEntity != null) {
                fsLeaderUserId = leaderBindEntity.getFsUserId();
            }
        }

        FsDeptArg arg = FsDeptArg.builder()
                .ei(ei + "")
                .name(department.getName())
                .code(department.getOpenDepartmentId())
                .status("0")
                .parentId(parentIdList)
                .managerId(StringUtils.isNotEmpty(fsLeaderUserId) ? Lists.newArrayList(fsLeaderUserId) : null)
                .build();
        LogUtils.info("ContactsServiceImpl.initDepList,createArg={}", arg);
        //3.创建纷享部门
        com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> createDepResult = fsDepartmentServiceProxy.create(arg);
        LogUtils.info("ContactsServiceImpl.initDepList,createDepResult={}", createDepResult);
        if (createDepResult.isSuccess() == false) {
            LogUtils.info("ContactsServiceImpl.initDepList,create dep failed,break,createArg={}", arg);
            return;
        }
        DepartmentBindEntity departmentBindEntity = DepartmentBindEntity.builder()
                .channel(ChannelEnum.feishu)
                .fsEa(fsEa)
                .fsDepId(createDepResult.getData().getId())
                .fsLeaderUserId(fsLeaderUserId)
                .outEa(outEa)
                .outDepId(department.getOpenDepartmentId())
                .outLeaderUserId(department.getLeaderUserId())
                .bindType(BindTypeEnum.auto)
                .bindStatus(BindStatusEnum.normal)
                .depCode(department.getOpenDepartmentId())
                .build();
        //4.插入部门绑定表数据
        int count = departmentBindManager.insert(departmentBindEntity);
        LogUtils.info("ContactsServiceImpl.initDepList,insert department mapping,count={}", count);
    }

    /**
     * 更新纷享用户主属部门信息
     * @param ei
     * @param fsEa
     * @param outEa
     * @param totalUserList
     */
    private void updateUserMainDep(Integer ei,
                                   String fsEa,
                                   String outEa,
                                   List<UserData.User> totalUserList) {
        for(UserData.User user : totalUserList) {
            EmployeeBindEntity employeeBindEntity = employeeBindManager.getEntity(outEa, user.getOpenId(), fsEa);
            LogUtils.info("ContactsServiceImpl.updateUserMainDep,employeeBindEntity={}",employeeBindEntity);
            if(employeeBindEntity==null) {
                continue;
            }
            List<String> fsDepList = getFsDepIdList(outEa,user.getDepartment_ids());
            if(CollectionUtils.isEmpty(fsDepList)) {
                continue;
            }

            FsEmpArg arg = FsEmpArg.builder()
                    .ei(ei+"")
                    .id(employeeBindEntity.getFsUserId())
                    .mainDepartment(Lists.newArrayList(fsDepList))
                    .build();
            //更新纷享员工主属部门
            com.facishare.open.order.contacts.proxy.api.result.Result<Void> updateResult = fsEmployeeServiceProxy.update(arg,
                    null,
                    null);
            LogUtils.info("ContactsServiceImpl.updateUserMainDep,update employee main department,updateResult={}",updateResult);
            if(updateResult.isSuccess()==false) {
                LogUtils.info("ContactsServiceImpl.initUserList,update employee main department failed,arg={},updateResult={}",
                        arg,updateResult);
            }
        }
    }

    /**
     * 更新管理员主属部门
     * @param ei
     * @param fsEa
     * @param outEa
     */
    private void updateAdminMainDep(String appId,Integer ei,
                                    String fsEa,
                                    String outEa) {
        EmployeeBindEntity entity = employeeBindManager.getEntityByFsUserId(fsEa, "1000",outEa);
        if(entity==null) return;

        Result<UserData.User> result = feishuUserService.getUserInfo(appId, outEa, entity.getOutUserId());
        if(result.isSuccess()) {
            updateUserMainDep(ei,fsEa,outEa,Lists.newArrayList(result.getData()));
        }
    }

    private List<String> getFsUserIdList(String appId, String fsEa, String outEa, List<DepartmentData.Department> departmentList) {
        List<UserData.User> userList = batchGetDepartmentUserList(appId, outEa, departmentList);

        List<String> userIdList = new ArrayList<>();
        for (UserData.User user : userList) {
            EmployeeBindEntity employeeBindEntity = employeeBindManager.getEntity(outEa, user.getOpenId(), fsEa);
            if (employeeBindEntity == null) {
                continue;
            }
            userIdList.add(employeeBindEntity.getFsUserId());
        }

        return userIdList;
    }

    private List<String> getFsDepIdList(String outEa, List<String> outDepIdList) {
        List<String> fsDepList = new ArrayList<>();
        for (String depId : outDepIdList) {
            //飞书根部门对应纷享根部门
            if (StringUtils.equalsIgnoreCase(depId, "0")) {
                fsDepList.add(GlobalValue.ALL_COMPANY_DEPARTMENT_ID + "");
                continue;
            }
            DepartmentBindEntity entityByOutEa = departmentBindManager.getEntityByOutEa(outEa, depId);
            if (entityByOutEa == null) {
                continue;
            }
            fsDepList.add(entityByOutEa.getFsDepId());
        }
        LogUtils.info("ContactsServiceImpl.getFsDepIdList,fsDepList={}", fsDepList);

        return fsDepList;
    }

    private List<String> getFsDepIdList(String appId, String outEa, List<DepartmentData.Department> departmentDataList) {
        List<DepartmentData.Department> allDepList = getAllDepartmentList2(appId, outEa, departmentDataList);

        List<String> fsDepIdList = new ArrayList<>();
        for (DepartmentData.Department department : allDepList) {
            DepartmentBindEntity entity = departmentBindManager.getEntity(outEa, department.getOpenDepartmentId());
            if (entity == null) {
                continue;
            }
            fsDepIdList.add(entity.getFsDepId());
        }

        return fsDepIdList;
    }

    @Override
    public Result<Void> resumeEmployee(String outEa, String outUserId) {
        List<EnterpriseBindEntity> enterpriseBindList = enterpriseBindManager.getEnterpriseBindList(outEa);
        LogUtils.info("ContactsServiceImpl.resumeEmployee,enterpriseBindList={}", enterpriseBindList);
        if(CollectionUtils.isEmpty(enterpriseBindList))
            return Result.newError(ResultCodeEnum.OUT_EA_NOT_BIND);

        int count = employeeBindManager.updateBindStatusByFeiShu(outEa, outUserId, BindStatusEnum.normal);
        LogUtils.info("ContactsServiceImpl.resumeEmployee,count={}", count);
        if(enterpriseBindList.size()==1 && enterpriseBindList.get(0).getBindType()==BindTypeEnum.auto) {
            if(count>0) {
                EmployeeBindEntity employeeBindEntity = employeeBindManager.getEntity(outEa,outUserId,enterpriseBindList.get(0).getFsEa());
                LogUtils.info("ContactsServiceImpl.resumeEmployee,employeeBindEntity={}", employeeBindEntity);
                int ei = eieaConverter.enterpriseAccountToId(enterpriseBindList.get(0).getFsEa());
                List<String> roleCodeList = Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode());
                String mainRoleCode = FsEmployeeRoleCodeEnum.SALES.getRoleCode();
                if(!StringUtils.equalsIgnoreCase(employeeBindEntity.getFsUserId(),"1000")) {
                    com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsEmployeeServiceProxy.bulkResume(ei + "",
                            Lists.newArrayList(employeeBindEntity.getFsUserId()),
                            roleCodeList,
                            mainRoleCode);
                    LogUtils.info("ContactsServiceImpl.resumeEmployee,result={}", result);
                }
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> stopEmployee(String outEa, String outUserId) {
        List<EnterpriseBindEntity> enterpriseBindList = enterpriseBindManager.getEnterpriseBindList(outEa);
        LogUtils.info("ContactsServiceImpl.stopEmployee,enterpriseBindList={}", enterpriseBindList);
        if(CollectionUtils.isEmpty(enterpriseBindList))
            return Result.newError(ResultCodeEnum.OUT_EA_NOT_BIND);

        int count = employeeBindManager.updateBindStatusByFeiShu(outEa, outUserId, BindStatusEnum.stop);
        LogUtils.info("ContactsServiceImpl.stopEmployee,count={}", count);
        if(enterpriseBindList.size()==1 && enterpriseBindList.get(0).getBindType()==BindTypeEnum.auto) {
            if(count>0) {
                EmployeeBindEntity employeeBindEntity = employeeBindManager.getEntity(outEa,outUserId,enterpriseBindList.get(0).getFsEa());
                LogUtils.info("ContactsServiceImpl.stopEmployee,employeeBindEntity={}", employeeBindEntity);
                int ei = eieaConverter.enterpriseAccountToId(enterpriseBindList.get(0).getFsEa());
                if(!StringUtils.equalsIgnoreCase(employeeBindEntity.getFsUserId(),"1000")) {
                    //只有非CRM管理员，才允许停用
                    com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsEmployeeServiceProxy.bulkStop(ei + "",
                            Lists.newArrayList(employeeBindEntity.getFsUserId()));
                    LogUtils.info("ContactsServiceImpl.stopEmployee,result={}", result);
                }
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<Void> autoGetContactData() {
        int pageNum = 0;
        int size;
        QueryOaConnectorSyncEventDataArg arg = new QueryOaConnectorSyncEventDataArg();
        arg.setChannel(ChannelEnum.feishu);
//        arg.setAppId(ConfigCenter.feishuCrmAppId);
        arg.setEventType(refreshContactScopeDataEventTypes);
        arg.setStatus(0);
        arg.setPageSize(1000);
        //分页循环，防止单次拉取数据太大导致oom
        Map<String, Set<String>> eaLists= Maps.newHashMap();
        do {
            arg.setPageNum(pageNum ++);
            List<OaConnectorSyncEventDataDoc> syncEventDataDocs = oaConnectorSyncEventDataManager.pageByQuerySyncEventDataArg(arg);

            if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(syncEventDataDocs)) {
                for (OaConnectorSyncEventDataDoc syncEventDataDoc : syncEventDataDocs) {
                    eaLists.computeIfAbsent(syncEventDataDoc.getOutEa(),k -> Sets.newHashSet()).add(syncEventDataDoc.getAppId());
                }
            }
            size = syncEventDataDocs.size();
        } while(size == 1000);
        LogUtils.info("ContactsServiceImpl.autoGetContactData,outEas={}", eaLists.size());
        if(eaLists.isEmpty()) {
            return Result.newSuccess();
        }
        //删除数据，再丢线程池
        for(String outEa : eaLists.keySet()) {
            QueryOaConnectorSyncEventDataArg delArg = new QueryOaConnectorSyncEventDataArg();
            delArg.setChannel(ChannelEnum.feishu);
//            delArg.setAppId(ConfigCenter.feishuCrmAppId);
            delArg.setEventType(refreshContactScopeDataEventTypes);
            delArg.setStatus(0);
            delArg.setOutEa(outEa);
            DeleteResult result = oaConnectorSyncEventDataManager.deleteTableDataByDelArg(delArg);
            LogUtils.info("ContactsServiceImpl.autoGetContactData,result={}", result);
            for (String appId : eaLists.get(outEa)) {
                ThreadPoolHelper.saveOrUpdateContactDataThreadPool.submit(() -> saveOrUpdateContactData(appId, outEa));
            }
        }
        return Result.newSuccess();
    }

    @Override
    public Result<ContactScopeModel> saveOrUpdateContactData(String appId, String outEa) {
        LogUtils.info("ContactsServiceImpl.saveOrUpdateContactData,appId={},outEa={}", appId, outEa);
        //分布式锁
        RLock rLock = redissonClient.getLock(GET_CONTACT_SCOPE_CACHE_KEY + appId + outEa);
        Result<ContactScopeModel> contactScopeModelResult = null;
        try {
            boolean isGetLock = rLock.tryLock(60 * 5, 60 * 5, TimeUnit.SECONDS);
            LogUtils.info("ContactsServiceImpl.saveOrUpdateContactData,Asynchronous threading,isGetLock={}", isGetLock);
            if(isGetLock) {
                try {
                    contactScopeModelResult = getContactScopeData(appId,
                            outEa);
                    if(!contactScopeModelResult.isSuccess()) {
                        return contactScopeModelResult;
                    }
                    ContactScopeModel contactScopeModel = contactScopeModelResult.getData();
                    if(ObjectUtils.isNotEmpty(contactScopeModel.getUserList())) {
                        saveOrUpdateOutUserInfo(outEa, contactScopeModel, Boolean.TRUE);
                    }

                    if(ObjectUtils.isNotEmpty(contactScopeModel.getDepartmentList())) {
                        saveOrUpdateOutDepartmentInfo(outEa, contactScopeModel, Boolean.TRUE);
                    }
                } finally {
                    rLock.unlock();
                }
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return ObjectUtils.isNotEmpty(contactScopeModelResult) ? contactScopeModelResult : Result.newSuccess(new ContactScopeModel());
    }

    @Override
    public Result<Void> refreshContactScopeDataCacheAsync(String eventType, String appId, String outEa, String eventData) {
        LogUtils.info("ContactsServiceImpl.refreshContactScopeDataCacheAsync,outEa={},eventData={}", outEa, eventData);
        OaConnectorSyncEventDataDoc doc = new OaConnectorSyncEventDataDoc();
        doc.setId(ObjectId.get());
        doc.setAppId(appId);
        doc.setOutEa(outEa);
        doc.setStatus(0);
        doc.setEventType(eventType);
        doc.setChannel(ChannelEnum.feishu);
        doc.setEvent(eventData);
        doc.setCreateTime(System.currentTimeMillis());
        doc.setUpdateTime(System.currentTimeMillis());
        BulkWriteResult bulkWriteResult = oaConnectorSyncEventDataManager.batchReplace(Lists.newArrayList(doc));
        LogUtils.info("ContactsServiceImpl.refreshContactScopeDataCacheAsync,bulkWriteResult={}", bulkWriteResult);
        return Result.newSuccess();
    }

    private void saveOrUpdateOutUserInfo(String outEa, ContactScopeModel contactScopeModel, Boolean isDeleteNotInCollectionDocs) {
        Gson gson = new Gson();
        List<OaConnectorOutUserInfoDoc> docs = new LinkedList<>();
        for(UserData.User user : contactScopeModel.getUserList()) {
            OaConnectorOutUserInfoDoc doc = new OaConnectorOutUserInfoDoc();
            doc.setChannel(ChannelEnum.feishu);
            doc.setOutEa(outEa);
            doc.setOutUserId(user.getOpenId());
            doc.setOutUserInfo(gson.toJson(user));
            doc.setCreateTime(System.currentTimeMillis());
            doc.setUpdateTime(System.currentTimeMillis());
            docs.add(doc);
        }
        BulkWriteResult bulkWriteResult = oaConnectorOutUserInfoManager.batchReplace(docs);
        LogUtils.info("ContactsServiceImpl.saveOrUpdateOutUserInfo,user,bulkWriteResult={}", bulkWriteResult);
        if(isDeleteNotInCollectionDocs) {
            DeleteResult deleteResult = oaConnectorOutUserInfoManager.deleteNotInCollectionDocs(ChannelEnum.feishu, outEa, docs);
            LogUtils.info("ContactsServiceImpl.saveOrUpdateOutUserInfo,user,deleteResult={}", deleteResult);
        }

        ThreadPoolHelper.autoBindThreadPool.submit(()->{
            //通过outEa查找对应的fsEa
            List<EnterpriseBindEntity> enterpriseList = enterpriseBindManager.getEnterpriseBindList(outEa);
            LogUtils.info("ContactsServiceImpl.saveOrUpdateOutUserInfo,enterpriseList={}", enterpriseList);
            if(CollectionUtils.isEmpty(enterpriseList) || ObjectUtils.isEmpty(contactScopeModel)) {
                return;
            }
            //是否灰度
            for(EnterpriseBindEntity entity: enterpriseList) {
                if(ConfigCenter.AUTO_BIND_ACCOUNT_BY_NAME_EA.contains(entity.getFsEa())) {
                    employeeBindService.autoBindEmpBySameName(outEa, entity.getFsEa(), contactScopeModel);
                } else if(entity.getAutBind() == 2) {
                    FeishuAppConnectParams connectParam = entity.getChannel().getConnectParam(entity.getConnectParams());
                    //底层做了判空
                    String autoField=connectParam.getAutoField();
                    employeeBindService.autoBindEmpByFeishuEmployeeNumber(outEa, entity.getFsEa(), contactScopeModel,autoField);
                }
            }
        });
    }

    private void saveOrUpdateOutDepartmentInfo(String outEa, ContactScopeModel contactScopeModel, Boolean isDeleteNotInCollectionDocs) {
        Gson gson = new Gson();
        List<OaConnectorOutDepartmentInfoDoc> docs = new LinkedList<>();
        for(DepartmentData.Department department : contactScopeModel.getDepartmentList()) {
            OaConnectorOutDepartmentInfoDoc doc = new OaConnectorOutDepartmentInfoDoc();
            doc.setChannel(ChannelEnum.feishu);
            doc.setOutEa(outEa);
            doc.setOutDepartmentId(department.getOpenDepartmentId());
            doc.setOutDepartmentInfo(gson.toJson(department));
            doc.setCreateTime(System.currentTimeMillis());
            doc.setUpdateTime(System.currentTimeMillis());
            docs.add(doc);
        }
        BulkWriteResult bulkWriteResult = oaConnectorOutDepartmentInfoManager.batchReplace(docs);
        LogUtils.info("ContactsServiceImpl.saveOrUpdateOutDepartmentInfo,department,bulkWriteResult={}", bulkWriteResult);
        if(isDeleteNotInCollectionDocs) {
            DeleteResult deleteResult = oaConnectorOutDepartmentInfoManager.deleteNotInCollectionDocs(ChannelEnum.feishu, outEa, docs);
            LogUtils.info("ContactsServiceImpl.saveOrUpdateOutDepartmentInfo,department,deleteResult={}", deleteResult);
        }
    }

    @Override
    public Result<Void> saveOrUpdateContactUser(String eventType, String appId, String outEa, UserData.User user, UserData.User oldUser, String eventData) {
        Long count = oaConnectorOutUserInfoManager.countDocuments(ChannelEnum.feishu, outEa);
        if(count == null || count == 0) {
            refreshContactScopeDataCacheAsync(eventType, appId, outEa, eventData);
            return Result.newSuccess();
        }
        if(ObjectUtils.isNotEmpty(oldUser) && CollectionUtils.isNotEmpty(oldUser.getDepartment_ids())) {
            List<OaConnectorOutDepartmentInfoDoc> departmentInfoDocs = oaConnectorOutDepartmentInfoManager.queryDepartmentInfosByIds(ChannelEnum.feishu, outEa, user.getDepartment_ids());
            LogUtils.info("ContactsServiceImpl.saveOrUpdateContactUser,departmentInfoDocs.size={}", departmentInfoDocs.size());
            if(CollectionUtils.isEmpty(departmentInfoDocs)) {
                deleteContactUser(appId, outEa, user);
                return Result.newSuccess();
            }
        }
        ContactScopeModel contactScopeModel = new ContactScopeModel();
        contactScopeModel.setUserList(Lists.newArrayList(user));
        saveOrUpdateOutUserInfo(outEa, contactScopeModel, Boolean.FALSE);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> deleteContactUser(String appId, String outEa, UserData.User user) {
        DeleteResult deleteResult = oaConnectorOutUserInfoManager.deleteUserInfoByUserId(ChannelEnum.feishu, outEa, user.getOpenId());
        LogUtils.info("ContactsServiceImpl.deleteContactUser,deleteResult={}", deleteResult);
        return null;
    }

    @Override
    public Result<Void> deleteContactDepartment(String appId, String outEa, DepartmentData.Department department) {
        DeleteResult deleteResult = oaConnectorOutDepartmentInfoManager.deleteDepartmentInfoByUserId(ChannelEnum.feishu, outEa, department.getOpenDepartmentId());
        LogUtils.info("ContactsServiceImpl.deleteContactDepartment,deleteResult={}", deleteResult);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> saveOrUpdateContactDepartment(String eventType, String appId, String outEa, DepartmentData.Department department, DepartmentData.Department oldDepartment, String eventData) {
        Long count = oaConnectorOutDepartmentInfoManager.countDocuments(ChannelEnum.feishu, outEa);
        if(count == null || count == 0) {
            refreshContactScopeDataCacheAsync(eventType, appId, outEa, eventData);
            return Result.newSuccess();
        }
        if(ObjectUtils.isNotEmpty(oldDepartment) && StringUtils.isNotEmpty(oldDepartment.getParentDepartmentId())) {
            refreshContactScopeDataCacheAsync(eventType, appId, outEa, eventData);
            return Result.newSuccess();
        }
        ContactScopeModel contactScopeModel = new ContactScopeModel();
        contactScopeModel.setDepartmentList(Lists.newArrayList(department));
        saveOrUpdateOutDepartmentInfo(outEa, contactScopeModel, Boolean.FALSE);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> addUser(String appId, String outEa, UserData.User user) {
        List<EnterpriseBindEntity> enterpriseBindList = enterpriseBindManager.getEnterpriseBindList(outEa);
        if(CollectionUtils.isEmpty(enterpriseBindList)) {
            return Result.newError(ResultCodeEnum.OUT_EA_NOT_BIND);
        }
        EnterpriseBindEntity enterpriseBindEntity = enterpriseBindList.get(0);

        String fsEa = enterpriseBindEntity.getFsEa();
        int ei = eieaConverter.enterpriseAccountToId(fsEa);
        EmployeeBindEntity employeeBindEntity = employeeBindManager.getEntity(outEa, user.getOpenId(), null);
        if (employeeBindEntity == null) {
            //员工不存在，新增员工并更新员工绑定表
            String sex = null;
            if (user.getGender() == 1) {
                sex = "M";
            } else if (user.getGender() == 2) {
                sex = "F";
            }

            if(CollectionUtils.isEmpty(user.getDepartment_ids())) {
                List<UserData.User> userList = batchGetUserList(ConfigCenter.feishuCrmAppId,
                        outEa,
                        Lists.newArrayList(user.getOpenId()));
                user.setDepartment_ids(userList.get(0).getDepartment_ids());
            }

            List<String> fsDepIdList = getFsDepIdList(outEa, user.getDepartment_ids());
            List<String> mainDepIdList = CollectionUtils.isEmpty(fsDepIdList) ?
                    Lists.newArrayList(GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"") : fsDepIdList;

            FsEmpArg arg = FsEmpArg.builder()
                    .ei(ei + "")
                    .name(StringUtils.isEmpty(user.getNickname()) ? user.getName() : user.getNickname())
                    .fullName(user.getName())
                    .sex(sex)
                    .phone(user.getMobile()) //商店应用获取不到值，永远为空
                    .mainDepartment(mainDepIdList)
                    .status("0")
                    .isActive(true)
                    .build();
            com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> result = fsEmployeeServiceProxy.create(arg,
                    Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
                    FsEmployeeRoleCodeEnum.SALES.getRoleCode());
            if (result.isSuccess()) {
                EmployeeBindEntity addEmployeeBindEntity = EmployeeBindEntity.builder()
                        .channel(ChannelEnum.feishu)
                        .fsEa(fsEa)
                        .fsUserId(result.getData().getId())
                        .outEa(outEa)
                        .outUserId(user.getOpenId())
                        .outLeaderUserId(user.getLeader_user_id())
                        .bindStatus(BindStatusEnum.normal)
                        .bindType(BindTypeEnum.auto)
                        .build();
                int count = employeeBindManager.insert(addEmployeeBindEntity);
                LogUtils.info("ContactsServiceImpl.addUser,insert employee mapping,count={}", count);
            } else {
                return Result.newError(ResultCodeEnum.getCodeByOutCode(String.valueOf(result.getCode()), ResultCodeEnum.CRM_USER_ACCOUNT_CREATE_ERROR.getCode()), result.getMsg());
            }
        } else {
            //员工存在，更新纷享员工状态并更新员工绑定状态
            com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsEmployeeServiceProxy.toggle(ei + "",
                    employeeBindEntity.getFsUserId(),
                    true,
                    Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
                    FsEmployeeRoleCodeEnum.SALES.getRoleCode());
            LogUtils.info("ContactsServiceImpl.addUserList,result={}", result);
            employeeBindManager.batchUpdateBindStatus(fsEa, Lists.newArrayList(employeeBindEntity.getFsUserId()), BindStatusEnum.normal,outEa);
        }

        return Result.newSuccess();
    }
}
