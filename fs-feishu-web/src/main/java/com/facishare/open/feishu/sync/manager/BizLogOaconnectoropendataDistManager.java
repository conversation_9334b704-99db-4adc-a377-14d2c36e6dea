package com.facishare.open.feishu.sync.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.facishare.open.feishu.sync.ckMapper.BizLogOaconnectoropendataDistMapper;
import com.facishare.open.feishu.syncapi.ckEntity.BizLogOaconnectoropendataDist;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class BizLogOaconnectoropendataDistManager {
    @Resource
    private BizLogOaconnectoropendataDistMapper bizLogOaconnectoropendataDistMapper;

    public List<BizLogOaconnectoropendataDist> findEnterpriseCreateError(String channelId, String dataTypeId, String corpId) {
        LambdaQueryWrapper<BizLogOaconnectoropendataDist> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizLogOaconnectoropendataDist::getChannelId, channelId);
        wrapper.eq(BizLogOaconnectoropendataDist::getDataTypeId, dataTypeId);
        wrapper.eq(BizLogOaconnectoropendataDist::getCorpId, corpId);

        return bizLogOaconnectoropendataDistMapper.selectList(wrapper);
    }
}
