package com.facishare.open.feishu.sync.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.facishare.open.feishu.syncapi.arg.CreateTodoPushArg;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.entity.*;
import com.facishare.open.feishu.syncapi.enums.*;
import com.facishare.open.feishu.syncapi.model.ExternalTodoMsgModel;
import com.facishare.open.feishu.syncapi.model.externalApprovals.ExternalApprovalsTaskDetail;
import com.facishare.open.feishu.syncapi.model.externalApprovals.ExternalApprovalsTaskUpdateDetail;
import com.facishare.open.feishu.syncapi.model.externalApprovals.ExternalInstancesDetail;
import com.facishare.open.feishu.syncapi.result.ExternalApprovalTaskResult;
import com.facishare.open.feishu.syncapi.result.ExternalInstancesDetailResult;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.feishu.syncapi.service.ExternalApprovalsService;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DealTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DeleteTodoArg;
import com.google.gson.Gson;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class ExternalTodoManager {
    @Resource
    private ExternalTodoTaskManager externalTodoTaskManager;
    @Resource
    private EnterpriseEditionService enterpriseEditionService;
    @Resource
    private ExternalApprovalsService externalApprovalsService;
    @Resource
    private ExternalTodoInstanceManager externalTodoInstanceManager;
    @Autowired
    private EmployeeBindManager employeeBindManager;

    public Result<Void> createExternalApprovalTodo(CreateTodoPushArg createTodoPushArg,
                                                   ExternalApprovalsTemplateEntity approvalsTemplateEntity,
                                                   List<EmployeeBindEntity> employeeBindEntities) {
        String msgType = createTodoPushArg.getMsgType();
        String upstreamEa = createTodoPushArg.getUpstreamEa();
        EnterpriseBindEntity enterpriseBindEntity = createTodoPushArg.getEnterpriseBindEntity();
        String fsEa = enterpriseBindEntity.getFsEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String appId = ObjectUtils.isEmpty(enterpriseBindEntity.getAppId()) ? ConfigCenter.feishuCrmAppId : enterpriseBindEntity.getAppId();
        CreateTodoArg createTodoArg = createTodoPushArg.getCreateTodoArg();

        ExternalTodoInstanceEntity externalTodoInstanceEntity = externalTodoInstanceManager.queryEntity(fsEa, outEa, createTodoArg.getSourceId());
        LogUtils.info("ExternalTodoManager.createExternalApprovalTodo,externalTodoInstanceEntity={}", externalTodoInstanceEntity);
        String curTime = System.currentTimeMillis() + "";
        ExternalInstancesDetail externalInstancesDetail;
        ExternalInstancesDetail.Links links;
        if(ObjectUtils.isEmpty(externalTodoInstanceEntity)) {
            externalInstancesDetail = new ExternalInstancesDetail();
            List<ExternalInstancesDetail.TextResource> texts = new LinkedList<>();
            List<ExternalInstancesDetail.Form> forms = new LinkedList<>();

            String todoUrl = feishuApprovalUrlGenerator(createTodoPushArg);
            if (StringUtils.isEmpty(todoUrl)) {
                //暂无跳转链接，返回，打印日志，不发送：https://wiki.firstshare.cn/pages/viewpage.action?pageId=214734110
                LogUtils.info("ExternalTodoManager.createExternalApprovalTodo,param is null,createTodoPushArg={}.", createTodoPushArg);
                if (ObjectUtils.isEmpty(externalTodoInstanceEntity)) {
                    ExternalTodoInstanceEntity insertEntity = ExternalTodoInstanceEntity.builder()
                            .channel(ChannelEnum.feishu)
                            .fsEa(fsEa)
                            .outEa(enterpriseBindEntity.getOutEa())
                            .sourceId(createTodoArg.getSourceId())
                            .taskId(createTodoArg.getExtraDataMap().get("taskId"))
                            .instanceId(outEa + appId + createTodoArg.getSourceId())
                            .fsUserId(String.valueOf(createTodoArg.getSenderId()))
                            .workflowInstanceId(createTodoArg.getExtraDataMap().get("workflowInstanceId"))
                            .objectApiName(createTodoArg.getExtraDataMap().get("objectApiName"))
                            .objectId(createTodoArg.getExtraDataMap().get("objectId"))
                            .todoDetail(JSON.toJSONString(new ExternalInstancesDetail()))
                            .status(ExternalTodoStatusEnum.NOT_PUT.getStatus())
                            .build();
                    Integer count = externalTodoInstanceManager.insert(insertEntity);
                    LogUtils.info("ExternalTodoManager.createExternalApprovalTodo,insert,count={}", count);
                }
                return Result.newSuccess();
            }

            byte[] todoUrlBytes = todoUrl.getBytes();
            String param = new String(Base64.encodeBase64(todoUrlBytes));
            String param2 = "";
            if(msgType.equals(OutOaMsgEventTypeEnum.interconnectMsg.name())) {
                //身份透传
                ExternalTodoMsgModel externalTodoMsgModel = new ExternalTodoMsgModel();
                externalTodoMsgModel.setFsEa(fsEa);
                externalTodoMsgModel.setOutEa(outEa);
                externalTodoMsgModel.setTaskId(createTodoArg.getExtraDataMap().get("taskId"));
                externalTodoMsgModel.setMsgType(OutOaMsgEventTypeEnum.interconnectMsg.name());
                externalTodoMsgModel.setUpstreamEa(upstreamEa);
                externalTodoMsgModel.setFsAppId(createTodoArg.getAppId());
                param2 = new String(Base64.encodeBase64(new Gson().toJson(externalTodoMsgModel).getBytes()));
            }

            String authUrl = ConfigCenter.FEISHU_AUTHEN_URL
                    .replace("{app_id}", appId)
                    .replace("{state}", appId)
                    .replace("{redirect_uri}", URLEncoder.encode(ConfigCenter.CRM_AUTH_URL + "?param=" + param + "&param2=" + param2 + "&fsEa=" + fsEa));


            ExternalInstancesDetail.TextResource titleText = new ExternalInstancesDetail.TextResource();
            titleText.setKey("@i18n@1");
            titleText.setValue(createTodoArg.getTitle());

            if (CollectionUtils.isNotEmpty(createTodoArg.getForm())) {
                int j = 2;
                for (int i = 0; i < createTodoArg.getForm().size(); i++) {
                    ExternalInstancesDetail.Form form = new ExternalInstancesDetail.Form();
                    form.setName("@i18n@" + j);
                    form.setValue("@i18n@" + (j + 1));
                    forms.add(form);

                    ExternalInstancesDetail.TextResource formText1 = new ExternalInstancesDetail.TextResource();
                    formText1.setKey("@i18n@" + j);
                    formText1.setValue(createTodoArg.getForm().get(i).getKey());
                    texts.add(formText1);

                    ExternalInstancesDetail.TextResource formText2 = new ExternalInstancesDetail.TextResource();
                    formText2.setKey("@i18n@" + (j + 1));
                    formText2.setValue(createTodoArg.getForm().get(i).getValue());
                    texts.add(formText2);
                    j += 2;
                }
            } else {
                titleText.setValue(createTodoArg.getTitle() + "：" + createTodoArg.getContent());
            }
            texts.add(titleText);

            externalInstancesDetail.setApprovalCode(approvalsTemplateEntity.getApprovalCode());
            externalInstancesDetail.setStatus(ApprovalStatusEnum.PENDING.name());
            externalInstancesDetail.setInstanceId(outEa + appId + createTodoArg.getSourceId());
            externalInstancesDetail.setTitle("@i18n@1");
            externalInstancesDetail.setForm(forms);
            links = new ExternalInstancesDetail.Links();
            links.setPcLink(authUrl);
            links.setMobileLink(authUrl);
            externalInstancesDetail.setLinks(links);
            externalInstancesDetail.setStartTime(curTime);
            externalInstancesDetail.setEndTime("0");
            externalInstancesDetail.setUpdateTime(curTime);
            externalInstancesDetail.setDisplayMethod(ApprovalDisplayMethodEnum.NORMAL.name());
            externalInstancesDetail.setUpdateMode(ApprovalUpdateModeEnum.REPLACE.name());
            List<ExternalInstancesDetail.I18NResource> i18nResources = new LinkedList<>();
            ExternalInstancesDetail.I18NResource i18NResource = new ExternalInstancesDetail.I18NResource();
            i18NResource.setLocale(ApprovalLocaleEnum.ZH_CN.getCode());
            i18NResource.setIsDefault(Boolean.TRUE);
            i18NResource.setTexts(texts);
            i18nResources.add(i18NResource);
            externalInstancesDetail.setI18nResources(i18nResources);
        } else {
            externalInstancesDetail = JSON.parseObject(externalTodoInstanceEntity.getTodoDetail(), new TypeReference<ExternalInstancesDetail>(){});
            externalInstancesDetail.setUpdateTime(curTime);
            externalInstancesDetail.setEndTime("0");
            externalInstancesDetail.setStatus(ApprovalStatusEnum.PENDING.name());
            links = externalInstancesDetail.getLinks();
        }

        List<ExternalInstancesDetail.Task> tasks = new LinkedList<>();
        if(CollectionUtils.isNotEmpty(externalInstancesDetail.getTaskList())) {
            tasks.addAll(externalInstancesDetail.getTaskList());
        }

        Map<String, ExternalInstancesDetail.Task> taskMap = CollectionUtils.isEmpty(externalInstancesDetail.getTaskList()) ? new HashMap<>() : externalInstancesDetail.getTaskList().stream()
                .collect(Collectors.toMap(ExternalInstancesDetail.Task::getOpenId, Function.identity(), (existing, replacement) -> existing));
        for(EmployeeBindEntity employeeBindEntity : employeeBindEntities) {
            //未避免重复新增人员事件，这里做去重处理，不用取最新结果
            //有可能当前同一个待办，一个人处理完成了，下个节点还是这个人，也需要重新推送
            if (taskMap.containsKey(employeeBindEntity.getOutUserId())) {
                //状态不是为待审批的需要重新推送
                if (!taskMap.get(employeeBindEntity.getOutUserId()).getStatus().equals(ApprovalTaskStatusEnum.PENDING.name())) {
                    tasks.remove(taskMap.get(employeeBindEntity.getOutUserId()));
                } else {
                    //待审批的不需要更新
                    continue;
                }
            }

            ExternalInstancesDetail.Task task = new ExternalInstancesDetail.Task();
            task.setTaskId(createTodoArg.getSourceId() + employeeBindEntity.getOutUserId());
            task.setOpenId(employeeBindEntity.getOutUserId());
            //json序列化的时候，有相同的对象，被引用了，再次直接使用的话，只会显示引用地址
            //:{
            //                "$ref":"$.links"
            //            },
            ExternalInstancesDetail.Links employeeLinks = new ExternalInstancesDetail.Links();
            employeeLinks.setPcLink(links.getPcLink());
            employeeLinks.setMobileLink(links.getMobileLink());
            task.setLinks(employeeLinks);
            task.setStatus(ApprovalTaskStatusEnum.PENDING.name());
            task.setCreateTime(curTime);
            task.setEndTime("0");
            task.setUpdateTime(curTime);
            task.setDisplayMethod(ApprovalDisplayMethodEnum.NORMAL.name());
            tasks.add(task);
        }
        externalInstancesDetail.setTaskList(tasks);
        Result<ExternalInstancesDetailResult> instancesDetailResultResult = externalApprovalsService.syncExternalApprovals(enterpriseBindEntity.getOutEa(), appId, externalInstancesDetail);
        LogUtils.info("ExternalTodoManager.createExternalApprovalTodo,instancesDetailResultResult={}", instancesDetailResultResult);
        if(instancesDetailResultResult.isSuccess()) {
            //保存入库
            if(ObjectUtils.isEmpty(externalTodoInstanceEntity)) {
                ExternalTodoInstanceEntity insertEntity = ExternalTodoInstanceEntity.builder()
                        .channel(ChannelEnum.feishu)
                        .fsEa(fsEa)
                        .outEa(enterpriseBindEntity.getOutEa())
                        .sourceId(createTodoArg.getSourceId())
                        .taskId(createTodoArg.getExtraDataMap().get("taskId"))
                        .instanceId(outEa + appId + createTodoArg.getSourceId())
                        .fsUserId(String.valueOf(createTodoArg.getSenderId()))
                        .workflowInstanceId(createTodoArg.getExtraDataMap().get("workflowInstanceId"))
                        .objectApiName(createTodoArg.getExtraDataMap().get("objectApiName"))
                        .objectId(createTodoArg.getExtraDataMap().get("objectId"))
                        .todoDetail(JSON.toJSONString(instancesDetailResultResult.getData().getData()))
                        .status(ExternalTodoStatusEnum.PENDING.getStatus())
                        .build();
                Integer count = externalTodoInstanceManager.insert(insertEntity);
                LogUtils.info("ExternalTodoManager.createExternalApprovalTodo,insert,count={}", count);
            } else {
                externalTodoInstanceEntity.setTodoDetail(JSON.toJSONString(instancesDetailResultResult.getData().getData()));
                externalTodoInstanceEntity.setStatus(ExternalTodoStatusEnum.PENDING.getStatus());
                Integer count = externalTodoInstanceManager.update(externalTodoInstanceEntity);
                LogUtils.info("ExternalTodoManager.createExternalApprovalTodo,update,count={}", count);
            }
        } else {
            //新增失败
            return Result.newError(ResultCodeEnum.CREATE_APPROVAL_TODO_ERROR.getCode(), instancesDetailResultResult.getMsg());
        }
        return Result.newSuccess();
    }

    public Result<Void> createExternalTodoTask(CreateTodoPushArg createTodoPushArg,
                                                   List<EmployeeBindEntity> employeeBindEntities) {
        String msgType = createTodoPushArg.getMsgType();
        String upstreamEa = createTodoPushArg.getUpstreamEa();
        EnterpriseBindEntity enterpriseBindEntity = createTodoPushArg.getEnterpriseBindEntity();
        String fsEa = enterpriseBindEntity.getFsEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String appId = ObjectUtils.isEmpty(enterpriseBindEntity.getAppId()) ? ConfigCenter.feishuCrmAppId : enterpriseBindEntity.getAppId();
        CreateTodoArg createTodoArg = createTodoPushArg.getCreateTodoArg();
        String sourceId = createTodoArg.getSourceId();

        //已经同步过了，但是会有这么一个问题，就是之前删除了或者是拒绝了的卡片，现在重新推送了待办，这个需要重新推送一个新的待办，因为这个也是不同的外部id，所以会有这种情况，一个sourceId下，同一个fsUserId会有多条待办记录，这些待办详情都是一样的，除了状态
        //创建待办
        ExternalApprovalsTaskDetail externalApprovalsTaskDetail = new ExternalApprovalsTaskDetail();
        List<ExternalApprovalsTaskDetail.I18nResource> i18nResources = new LinkedList<>();
        ExternalApprovalsTaskDetail.I18nResource i18nResource = new ExternalApprovalsTaskDetail.I18nResource();
        i18nResource.setLocale(ApprovalLocaleEnum.ZH_CN.getCode());
        i18nResource.setDefault(Boolean.TRUE);
        Map<String, String> texts = new HashMap<>();

        externalApprovalsTaskDetail.setTemplateId("1008");
        externalApprovalsTaskDetail.setApprovalName("@i18n@1");
        texts.put("@i18n@1", createTodoArg.getContent());
        externalApprovalsTaskDetail.setNote("@i18n@2");
        texts.put("@i18n@2", "纷享销客crm"); //ignorei18n

        ExternalApprovalsTaskDetail.Content content = new ExternalApprovalsTaskDetail.Content();
        List<ExternalApprovalsTaskDetail.Summary> summaries = new LinkedList<>();
        int j = 3;
        if (CollectionUtils.isNotEmpty(createTodoArg.getForm())) {
            for (int i = 0; i < createTodoArg.getForm().size(); i++) {
                ExternalApprovalsTaskDetail.Summary summary = new ExternalApprovalsTaskDetail.Summary();
                summary.setSummary("@i18n@" + j);
                summaries.add(summary);
                texts.put("@i18n@" + j, createTodoArg.getForm().get(i).getKey() + "：" + createTodoArg.getForm().get(i).getValue());
                j += 1;
                //code='60001', msg='too much Summaries error', traceMsg='null'
                if(summaries.size() >= 5) {
                    break;
                }
            }
        } else {
            ExternalApprovalsTaskDetail.Summary summary = new ExternalApprovalsTaskDetail.Summary();
            summary.setSummary("@i18n@" + j);
            summaries.add(summary);
            texts.put("@i18n@" + j, createTodoArg.getContent());
            j += 1;
        }
        content.setSummaries(summaries);

        List<ExternalApprovalsTaskDetail.Action> actions = new LinkedList<>();
        ExternalApprovalsTaskDetail.Action action = new ExternalApprovalsTaskDetail.Action();
        action.setActionName("DETAIL");
        String todoUrl = feishuApprovalUrlGenerator(createTodoPushArg);
        if (StringUtils.isEmpty(todoUrl)) {
            //暂无跳转链接，返回，打印日志，不发送：https://wiki.firstshare.cn/pages/viewpage.action?pageId=214734110
            LogUtils.info("ExternalTodoManager.createTodo,param is null,createTodoArg={}.", createTodoArg);
            List<ExternalTodoTaskEntity> externalTodoTaskEntities = externalTodoTaskManager.queryEntities(fsEa, outEa, sourceId, null, null);
            if (CollectionUtils.isEmpty(externalTodoTaskEntities)) {
                for (EmployeeBindEntity employeeBindEntity : employeeBindEntities) {
                    ExternalTodoTaskEntity externalTodoTaskEntity = new ExternalTodoTaskEntity();
                    externalTodoTaskEntity.setChannel(ChannelEnum.feishu);
                    externalTodoTaskEntity.setFsEa(fsEa);
                    externalTodoTaskEntity.setOutEa(outEa);
                    externalTodoTaskEntity.setSourceId(sourceId);
                    externalTodoTaskEntity.setTaskId(sourceId);
                    externalTodoTaskEntity.setOutUserId(employeeBindEntity.getOutUserId());
                    externalTodoTaskEntity.setOutOwnerId(externalApprovalsTaskDetail.getTitleUserId());
                    externalTodoTaskEntity.setStatus(ExternalTodoStatusEnum.NOT_PUT.getStatus());
                    Integer insert = externalTodoTaskManager.insert(externalTodoTaskEntity);
                    LogUtils.info("ExternalTodoManager.GetEnterpriseDataArg,insert={}", insert);
                }
            }
            return Result.newSuccess();
        }

        byte[] todoUrlBytes = todoUrl.getBytes();
        String param = new String(Base64.encodeBase64(todoUrlBytes));
        String param2 = "";

        ExternalTodoMsgModel externalTodoMsgModel = new ExternalTodoMsgModel();
        externalTodoMsgModel.setFsEa(fsEa);
        externalTodoMsgModel.setOutEa(outEa);
        externalTodoMsgModel.setTaskId(createTodoArg.getExtraDataMap().get("taskId"));
        externalTodoMsgModel.setFsAppId(createTodoArg.getAppId());
        externalTodoMsgModel.setMsgType(msgType);
        externalTodoMsgModel.setUpstreamEa(upstreamEa);
        if(msgType.equals(OutOaMsgEventTypeEnum.interconnectMsg.name())) {
            param2 = new String(Base64.encodeBase64(new Gson().toJson(externalTodoMsgModel).getBytes()));
        }

        String authUrl = ConfigCenter.FEISHU_AUTHEN_URL
                .replace("{app_id}", appId)
                .replace("{state}", appId)
                .replace("{redirect_uri}", URLEncoder.encode(ConfigCenter.CRM_AUTH_URL + "?param=" + param + "&param2=" + param2 + "&fsEa=" + fsEa));

        action.setUrl(authUrl);
        action.setAndroidUrl(authUrl);
        action.setIosUrl(authUrl);
        action.setPcUrl(ConfigCenter.FEISHU_WEB_URL
                .replace("{url}", URLEncoder.encode(authUrl))
                .replace("{mode}", "sidebar-semi"));
        actions.add(action);
        externalApprovalsTaskDetail.setActions(actions);

        List<ExternalApprovalsTaskDetail.ActionConfig> actionConfigs = new LinkedList<>();
        ExternalApprovalsTaskDetail.ActionConfig approveConfig = new ExternalApprovalsTaskDetail.ActionConfig();
        approveConfig.setActionType("APPROVE");
        approveConfig.setNeedReason(Boolean.TRUE);
        approveConfig.setReasonRequired(Boolean.FALSE);
        approveConfig.setNeedAttachment(Boolean.FALSE);
        actionConfigs.add(approveConfig);
        ExternalApprovalsTaskDetail.ActionConfig rejectConfig = new ExternalApprovalsTaskDetail.ActionConfig();
        rejectConfig.setActionType("REJECT");
        rejectConfig.setNeedReason(Boolean.TRUE);
        rejectConfig.setReasonRequired(Boolean.FALSE);
        rejectConfig.setNeedAttachment(Boolean.FALSE);
        actionConfigs.add(rejectConfig);
        externalApprovalsTaskDetail.setActionConfigs(actionConfigs);

        ExternalApprovalsTaskDetail.ActionCallback actionCallback = new ExternalApprovalsTaskDetail.ActionCallback();
        actionCallback.setActionCallbackUrl(ConfigCenter.crm_domain + ConfigCenter.APPROVAL_ACTION_CALLBACK_URL);
        actionCallback.setActionCallbackKey(ConfigCenter.APPROVAL_ACTION_CALLBACK_KEY);
        actionCallback.setActionCallbackToken(ConfigCenter.APPROVAL_ACTION_CALLBACK_TOKEN);
        externalApprovalsTaskDetail.setActionCallback(actionCallback);

        //互联待办不用标注谁发起的，因为通常是上游的人推送过去的
        if(msgType.equals(OutOaMsgEventTypeEnum.commonMsg.name())) {
            //兼容多企业的情况
            String applicantId = createTodoArg.getExtraDataMap().get("applicantId");
            //通过applicantId 找到外部outUserId
            EmployeeBindEntity entityByFsUserId = employeeBindManager.getEntityByFsUserId(fsEa, applicantId, outEa);
            if(ObjectUtils.isNotEmpty(entityByFsUserId)) {
                externalApprovalsTaskDetail.setTitleUserId(entityByFsUserId.getOutUserId());
                externalApprovalsTaskDetail.setTitleUserIdType("open_id");

                content.setUserId(entityByFsUserId.getOutUserId());
                content.setUserIdType("open_id");
            }
        }

        externalApprovalsTaskDetail.setContent(content);

        i18nResource.setTexts(texts);
        i18nResources.add(i18nResource);
        externalApprovalsTaskDetail.setI18nResources(i18nResources);

        List<ExternalTodoTaskEntity> externalTodoTaskEntities = externalTodoTaskManager.queryEntities(fsEa, outEa, sourceId, null, 0);
        LogUtils.info("ExternalTodoManager.GetEnterpriseDataArg,externalTodoTaskEntities={}",externalTodoTaskEntities);
        List<String> externalTodoTaskUserIds = externalTodoTaskEntities.stream().map(ExternalTodoTaskEntity::getOutUserId).collect(Collectors.toList());
        StringBuilder msg = new StringBuilder();
        for(EmployeeBindEntity entity : employeeBindEntities) {
            //存在未办的，不用重复推送
            if(externalTodoTaskUserIds.contains(entity.getOutUserId())) {
                continue;
            }

            externalApprovalsTaskDetail.setOpenId(entity.getOutUserId());
            externalApprovalsTaskDetail.setUuid(String.valueOf(UUID.randomUUID()));

            //操作上下文，回调的时候会把该参数回传
            externalTodoMsgModel.setFsUserId(entity.getFsUserId());

            actionCallback.setActionContext(new Gson().toJson(externalTodoMsgModel));
            externalApprovalsTaskDetail.setActionCallback(actionCallback);

            Result<ExternalApprovalTaskResult> externalApprovalTaskResultResult = externalApprovalsService.sendExternalApprovalTask(outEa, appId, externalApprovalsTaskDetail);
            LogUtils.info("ExternalTodoManager.GetEnterpriseDataArg,externalApprovalTaskResultResult={}",externalApprovalTaskResultResult);
            if(externalApprovalTaskResultResult.isSuccess()) {
                //保存入库
                ExternalTodoTaskEntity externalTodoTaskEntity = new ExternalTodoTaskEntity();
                externalTodoTaskEntity.setChannel(ChannelEnum.feishu);
                externalTodoTaskEntity.setFsEa(fsEa);
                externalTodoTaskEntity.setOutEa(outEa);
                externalTodoTaskEntity.setSourceId(sourceId);
                externalTodoTaskEntity.setTaskId(externalApprovalTaskResultResult.getData().getMessageId());
                externalTodoTaskEntity.setOutUserId(entity.getOutUserId());
                externalTodoTaskEntity.setOutOwnerId(externalApprovalsTaskDetail.getTitleUserId());
                externalTodoTaskEntity.setStatus(ExternalTodoStatusEnum.PENDING.getStatus());
                Integer insert = externalTodoTaskManager.insert(externalTodoTaskEntity);
                LogUtils.info("ExternalTodoManager.GetEnterpriseDataArg,insert={}",insert);
            } else {
                msg.append(entity.getOutUserId()).append(":").append(externalApprovalTaskResultResult.getMsg()).append("\n");
            }
        }

        if(ObjectUtils.isNotEmpty(msg)) {
            return Result.newError(ResultCodeEnum.CREATE_APPROVAL_TODO_ERROR.getCode(), msg.toString());
        }
        return Result.newSuccess();
    }

    public Result<Void> dealExternalApprovalTodo(DealTodoArg arg,
                                                 List<EmployeeBindEntity> employeeBindEntities,
                                                 EnterpriseBindEntity enterpriseBindEntity) {
        //查库
        String appId=ObjectUtils.isNotEmpty(enterpriseBindEntity.getAppId())?enterpriseBindEntity.getAppId():ConfigCenter.feishuCrmAppId;
        ExternalTodoInstanceEntity externalTodoInstanceEntity = externalTodoInstanceManager.queryEntity(arg.getEa(), enterpriseBindEntity.getOutEa(), arg.getSourceId());

        //顺序消费会有绑定记录，除了失败
        if(ObjectUtils.isEmpty(externalTodoInstanceEntity)) {
            LogUtils.info("ExternalTodoManager.dealExternalApprovals,externalTodoInstanceEntity is null,arg={}", arg);
            return Result.newError(ResultCodeEnum.DEAL_APPROVAL_TODO_ERROR);
        }

        if(externalTodoInstanceEntity.getStatus() != 0) {
            return Result.newSuccess();
        }

        //组装数据
        String curTime = System.currentTimeMillis() + "";
        ExternalInstancesDetail externalInstancesDetail = JSON.parseObject(externalTodoInstanceEntity.getTodoDetail(), new TypeReference<ExternalInstancesDetail>(){});
        externalInstancesDetail.setUpdateTime(curTime);

        //判断是否需要完结次待办
        boolean isDone = Boolean.TRUE;

        Map<String, String> employeeMap = employeeBindEntities.stream()
                .collect(Collectors.toMap(EmployeeBindEntity::getOutUserId, EmployeeBindEntity::getFsUserId, (v1, v2) -> v1));
        for(ExternalInstancesDetail.Task task : externalInstancesDetail.getTaskList()) {
            if(employeeMap.containsKey(task.getOpenId())) {
                //需要设置完成
                task.setStatus(ApprovalTaskStatusEnum.APPROVED.name());
                task.setEndTime(curTime);
                task.setUpdateTime(curTime);
            } else {
                if(isDone && task.getStatus().equals(ApprovalTaskStatusEnum.PENDING.name())) {
                    isDone = Boolean.FALSE;
                }
            }
        }

        LogUtils.info("ExternalTodoManager.dealExternalApprovals,isDone={}", isDone);
        if(isDone) {
            externalInstancesDetail.setUpdateTime(curTime);
            externalInstancesDetail.setEndTime(curTime);
            externalInstancesDetail.setStatus(ApprovalStatusEnum.APPROVED.name());
        }

        Result<ExternalInstancesDetailResult> instancesDetailResultResult = externalApprovalsService.syncExternalApprovals(enterpriseBindEntity.getOutEa(), appId, externalInstancesDetail);
        LogUtils.info("ExternalTodoManager.dealExternalApprovals,instancesDetailResultResult={}", instancesDetailResultResult);
        if(instancesDetailResultResult.isSuccess()) {
            //保存入库
            externalTodoInstanceEntity.setTodoDetail(JSON.toJSONString(instancesDetailResultResult.getData().getData()));
            if(isDone) {
                externalTodoInstanceEntity.setStatus(ExternalTodoStatusEnum.APPROVED.getStatus());
            }
            Integer count = externalTodoInstanceManager.update(externalTodoInstanceEntity);
            LogUtils.info("ExternalTodoManager.dealExternalApprovals,update,count={}", count);
        } else {
            return Result.newError(ResultCodeEnum.DEAL_APPROVAL_TODO_ERROR.getCode(), instancesDetailResultResult.getMsg());
        }

        return Result.newSuccess();
    }

    public Result<Void> dealExternalApprovalTodoTask(DealTodoArg arg,
                                                 List<EmployeeBindEntity> employeeBindEntities,
                                                 EnterpriseBindEntity enterpriseBindEntity) {
        String fsEa = arg.getEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String sourceId = arg.getSourceId();
        String appId=ObjectUtils.isNotEmpty(enterpriseBindEntity.getAppId())?enterpriseBindEntity.getAppId():ConfigCenter.feishuCrmAppId;
        //根据sourceId查找bot审批
        List<ExternalTodoTaskEntity> externalTodoTaskEntities = externalTodoTaskManager.queryEntities(fsEa, outEa, sourceId, null, null);
        if(CollectionUtils.isEmpty(externalTodoTaskEntities)) {
            return Result.newError(ResultCodeEnum.DEAL_APPROVAL_TODO_ERROR);
        }
        StringBuilder msg = new StringBuilder();
        List<ExternalTodoTaskEntity> filteredExternalTodoTaskEntities = new LinkedList<>();

        for(EmployeeBindEntity employeeBindEntity : employeeBindEntities) {
            boolean isEmpRetry = Boolean.TRUE;
            for(ExternalTodoTaskEntity externalTodoTaskEntity : externalTodoTaskEntities) {
                if(employeeBindEntity.getOutUserId().equals(externalTodoTaskEntity.getOutUserId())) {
                    isEmpRetry = Boolean.FALSE;
                    if(externalTodoTaskEntity.getStatus() != 0) {
                        //已处理过
                        continue;
                    }
                    filteredExternalTodoTaskEntities.add(externalTodoTaskEntity);
                }
            }

            if(isEmpRetry) {
                msg.append(employeeBindEntity.getOutUserId()).append(":").append(ResultCodeEnum.CREATE_APPROVAL_TODO_ERROR.getMsg()).append("\n");
            }
        }

        if(CollectionUtils.isEmpty(filteredExternalTodoTaskEntities)) {
            return Result.newSuccess();
        }

        for(ExternalTodoTaskEntity externalTodoTaskEntity : filteredExternalTodoTaskEntities) {
            ExternalApprovalsTaskUpdateDetail externalApprovalsTaskUpdateDetail = new ExternalApprovalsTaskUpdateDetail();
            externalApprovalsTaskUpdateDetail.setMessageId(externalTodoTaskEntity.getTaskId());
            externalApprovalsTaskUpdateDetail.setStatus(ApprovalBotTaskStatusEnum.PROCESSED.name());
            Result<ExternalApprovalTaskResult> externalApprovalTaskResultResult = externalApprovalsService.updateExternalApprovalTask(outEa, appId, externalApprovalsTaskUpdateDetail);
            if(externalApprovalTaskResultResult.isSuccess()) {
                externalTodoTaskEntity.setStatus(ExternalTodoStatusEnum.APPROVED.getStatus());
                Integer update = externalTodoTaskManager.update(externalTodoTaskEntity);
                LogUtils.info("ExternalTodoManager.GetEnterpriseDataArg,update={}",update);
            } else {
                msg.append(externalTodoTaskEntity.getOutUserId()).append(":").append(externalApprovalTaskResultResult.getMsg()).append("\n");
            }
        }

        if(ObjectUtils.isNotEmpty(msg)) {
            return Result.newError(ResultCodeEnum.CREATE_APPROVAL_TODO_ERROR.getCode(), msg.toString());
        }
        return Result.newSuccess();
    }

    public Result<Void> deleteExternalApprovals(DeleteTodoArg var1,
                                                     List<EmployeeBindEntity> employeeBindEntities,
                                                     EnterpriseBindEntity enterpriseBindEntity) {

        //查库
        String appId=ObjectUtils.isNotEmpty(enterpriseBindEntity.getAppId())?enterpriseBindEntity.getAppId():ConfigCenter.feishuCrmAppId;
        ExternalTodoInstanceEntity externalTodoInstanceEntity = externalTodoInstanceManager.queryEntity(var1.getEa(), enterpriseBindEntity.getOutEa(), var1.getSourceId());
        if(ObjectUtils.isEmpty(externalTodoInstanceEntity)) {
            LogUtils.info("ExternalTodoManager.deleteExternalApprovals,externalTodoInstanceEntity is null,arg={}", var1);
            return Result.newError(ResultCodeEnum.DELETE_APPROVAL_TODO_ERROR);
        }

        if(externalTodoInstanceEntity.getStatus() != 0) {
            return Result.newSuccess();
        }

        //组装数据
        String curTime = System.currentTimeMillis() + "";
        ExternalInstancesDetail externalInstancesDetail = JSON.parseObject(externalTodoInstanceEntity.getTodoDetail(), new TypeReference<ExternalInstancesDetail>(){});
        externalInstancesDetail.setUpdateTime(curTime);

        Map<String, String> employeeMap = employeeBindEntities.stream()
                .collect(Collectors.toMap(EmployeeBindEntity::getOutUserId, EmployeeBindEntity::getFsUserId, (v1, v2) -> v1));
        List<ExternalInstancesDetail.Task> deleteInstancesDetail = new LinkedList<>(externalInstancesDetail.getTaskList());
        for(ExternalInstancesDetail.Task task : externalInstancesDetail.getTaskList()) {
            if(employeeMap.containsKey(task.getOpenId())) {
                deleteInstancesDetail.remove(task);
            }
        }

        externalInstancesDetail.setTaskList(deleteInstancesDetail);
        LogUtils.info("ExternalTodoManager.deleteExternalApprovals,deleteInstancesDetail.size={}", deleteInstancesDetail.size());
        if(CollectionUtils.isEmpty(deleteInstancesDetail)) {
            externalInstancesDetail.setUpdateTime(curTime);
            externalInstancesDetail.setEndTime(curTime);
            externalInstancesDetail.setStatus(ApprovalStatusEnum.DELETED.name());
        }

        Result<ExternalInstancesDetailResult> instancesDetailResultResult = externalApprovalsService.syncExternalApprovals(enterpriseBindEntity.getOutEa(), appId, externalInstancesDetail);
        LogUtils.info("ExternalTodoManager.deleteExternalApprovals,instancesDetailResultResult={}", instancesDetailResultResult);
        if(instancesDetailResultResult.isSuccess()) {
            //保存入库
            externalTodoInstanceEntity.setTodoDetail(JSON.toJSONString(instancesDetailResultResult.getData().getData()));
            if(CollectionUtils.isEmpty(deleteInstancesDetail)) {
                externalTodoInstanceEntity.setStatus(ExternalTodoStatusEnum.DELETED.getStatus());
            }
            Integer count = externalTodoInstanceManager.update(externalTodoInstanceEntity);
            LogUtils.info("ExternalTodoManager.deleteExternalApprovals,update,count={}", count);
        } else {
            return Result.newError(ResultCodeEnum.DELETE_APPROVAL_TODO_ERROR.getCode(), instancesDetailResultResult.getMsg());
        }

        return Result.newSuccess();
    }

    public Result<Void> deleteExternalApprovalTodoTask(DeleteTodoArg arg,
                                                     List<EmployeeBindEntity> employeeBindEntities,
                                                     EnterpriseBindEntity enterpriseBindEntity) {
        String fsEa = arg.getEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String sourceId = arg.getSourceId();
        String appId=ObjectUtils.isNotEmpty(enterpriseBindEntity.getAppId())?enterpriseBindEntity.getAppId():ConfigCenter.feishuCrmAppId;
        //根据sourceId查找bot审批
        List<ExternalTodoTaskEntity> externalTodoTaskEntities = externalTodoTaskManager.queryEntities(fsEa, outEa, sourceId, null, null);
        if(CollectionUtils.isEmpty(externalTodoTaskEntities)) {
            return Result.newError(ResultCodeEnum.CREATE_APPROVAL_TODO_ERROR);
        }
        StringBuilder msg = new StringBuilder();
        List<ExternalTodoTaskEntity> filteredExternalTodoTaskEntities = new LinkedList<>();

        for(EmployeeBindEntity employeeBindEntity : employeeBindEntities) {
            boolean isEmpRetry = Boolean.TRUE;
            for(ExternalTodoTaskEntity externalTodoTaskEntity : externalTodoTaskEntities) {
                if(employeeBindEntity.getOutUserId().equals(externalTodoTaskEntity.getOutUserId())) {
                    isEmpRetry = Boolean.FALSE;
                    if(externalTodoTaskEntity.getStatus() != 0) {
                        //已处理过
                        continue;
                    }
                    filteredExternalTodoTaskEntities.add(externalTodoTaskEntity);
                }
            }

            if(isEmpRetry) {
                msg.append(employeeBindEntity.getOutUserId()).append(":").append(ResultCodeEnum.CREATE_APPROVAL_TODO_ERROR.getMsg()).append("\n");
            }
        }

        if(CollectionUtils.isEmpty(filteredExternalTodoTaskEntities)) {
            return Result.newSuccess();
        }

        for(ExternalTodoTaskEntity externalTodoTaskEntity : filteredExternalTodoTaskEntities) {
            ExternalApprovalsTaskUpdateDetail externalApprovalsTaskUpdateDetail = new ExternalApprovalsTaskUpdateDetail();
            externalApprovalsTaskUpdateDetail.setMessageId(externalTodoTaskEntity.getTaskId());
            externalApprovalsTaskUpdateDetail.setStatus(ApprovalBotTaskStatusEnum.DELETED.name());
            Result<ExternalApprovalTaskResult> externalApprovalTaskResultResult = externalApprovalsService.updateExternalApprovalTask(outEa, appId, externalApprovalsTaskUpdateDetail);
            if(externalApprovalTaskResultResult.isSuccess()) {
                externalTodoTaskEntity.setStatus(ExternalTodoStatusEnum.DELETED.getStatus());
                Integer update = externalTodoTaskManager.update(externalTodoTaskEntity);
                LogUtils.info("ExternalTodoManager.GetEnterpriseDataArg,update={}",update);
            } else {
                msg.append(externalTodoTaskEntity.getOutUserId()).append(":").append(externalApprovalTaskResultResult.getMsg()).append("\n");
            }
        }

        if(ObjectUtils.isNotEmpty(msg)) {
            return Result.newError(ResultCodeEnum.CREATE_APPROVAL_TODO_ERROR.getCode(), msg.toString());
        }
        return Result.newSuccess();
    }

    private GetEnterpriseDataResult getEnterpriseInfo(String fsEa) {
        GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
        arg.setEnterpriseAccount(fsEa);
        GetEnterpriseDataResult result = enterpriseEditionService.getEnterpriseData(arg);
        LogUtils.info("ExternalTodoManager.GetEnterpriseDataArg,result={}",result);
        return result;
    }

    private String feishuApprovalUrlGenerator(CreateTodoPushArg createTodoPushArg) {
        String upstreamEa = createTodoPushArg.getUpstreamEa();
        String msgType = createTodoPushArg.getMsgType();
        CreateTodoArg createTodoArg = createTodoPushArg.getCreateTodoArg();
        String todoUrl = null;
        if (msgType.equals(OutOaMsgEventTypeEnum.commonMsg.name())) {
            //正常的
            if (ObjectUtils.isNotEmpty(createTodoArg.getExtraDataMap())
                    && StringUtils.isNotEmpty(createTodoArg.getExtraDataMap().get("workflowInstanceId"))
                    && StringUtils.isNotEmpty(createTodoArg.getExtraDataMap().get("objectApiName"))
                    && StringUtils.isNotEmpty(createTodoArg.getExtraDataMap().get("objectId"))) {
                String domain = getEnterpriseInfo(createTodoArg.getEa()).getEnterpriseData().getDomain();
                todoUrl = domain +
                        ConfigCenter.CRM_FUNCTION_URL + ConfigCenter.CRM_APPROVAL_BRIDGE_URL
                        .replace("{workflowInstanceId}", createTodoArg.getExtraDataMap().get("workflowInstanceId"))
                        .replace("{objectApiName}", createTodoArg.getExtraDataMap().get("objectApiName"))
                        .replace("{objectId}", createTodoArg.getExtraDataMap().get("objectId"))
                        .replace("{ea}", createTodoArg.getEa())
                        + "&forceRedirectH5=true";
            }
        } else {
            //互联的
            if(ObjectUtils.isEmpty(createTodoArg.getExtraDataMap())
                    || StringUtils.isEmpty(createTodoArg.getExtraDataMap().get("workflowInstanceId"))
                    || StringUtils.isEmpty(createTodoArg.getExtraDataMap().get("objectId"))
                    || StringUtils.isEmpty(createTodoArg.getExtraDataMap().get("objectApiName"))) {
                //暂无跳转链接，返回，打印日志，不发送：https://wiki.firstshare.cn/pages/viewpage.action?pageId=214734110
                LogUtils.info("ExternalMsgServiceImpl.createExternalCrmBot,param is null,createTodoArg={}.", createTodoArg);
                return null;
            }
            todoUrl = getEnterpriseInfo(createTodoArg.getEa()).getEnterpriseData().getDomain() + ConfigCenter.CRM_INTERCONNECT_APPROVAL_BRIDGE_URL
                    .replace("{fsAppId}", createTodoArg.getAppId())
                    .replace("{upstreamEa}", upstreamEa)
                    .replace("{objectId}", createTodoArg.getExtraDataMap().get("objectId"))
                    .replace("{objectApiName}", createTodoArg.getExtraDataMap().get("objectApiName"))
                    .replace("{workflowInstanceId}", createTodoArg.getExtraDataMap().get("workflowInstanceId"))
                    .replace("{forceRedirectH5}", String.valueOf(Boolean.TRUE));
        }
        return todoUrl;
    }
}
