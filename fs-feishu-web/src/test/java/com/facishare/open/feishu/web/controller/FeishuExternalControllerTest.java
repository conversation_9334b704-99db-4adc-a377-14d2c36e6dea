package com.facishare.open.feishu.web.controller;

import com.facishare.open.feishu.web.BaseTest;
import com.facishare.open.feishu.web.controller.outer.feishu.FeishuExternalController;
import org.junit.Test;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class FeishuExternalControllerTest extends BaseTest {
    @Resource
    private FeishuExternalController feishuExternalController;
    @Test
    public void push() throws Exception {
        HttpServletRequest request = new MockHttpServletRequest();
        HttpServletResponse response = new MockHttpServletResponse();
        String param = "aHR0cHM6Ly9jcm0uY2VzaGkxMTIuY29tL3Byb2ovcHJtL2ZlaXNodT9mc0FwcElkPUZTQUlEXzExNDkwZDllJnVwc3RyZWFtRWE9ODM5OTgmYXBpTmFtZT1BcHByb3ZhbEluc3RhbmNlT2JqJmRhdGFJZD02NmNlYzQzNjkxM2QwYTZjNDZmYzZkZDImb2JqZWN0QXBpTmFtZT1BY2NvdW50T2JqJm9iamVjdElkPTY2Y2VjNDM0N2MzZTcxMDAwN2IyMWQ2ZSZmb3JjZVJlZGlyZWN0SDU9dHJ1ZS8jL3VpcGFhc19jdXN0b20vb2JqZWN0X2Zsb3cvcGFnZXMvYXBwcm92YWxfYnJpZGdlL2FwcHJvdmFsX2JyaWRnZQ==";
        //String param2 = "eyJ0eXBlIjoxLCJlYSI6Ijg5NzY0IiwiZWkiOjg5NzY0LCJ1cHN0cmVhbUVhIjoiODM5OTgiLCJ1cHN0cmVhbUVpIjo4Mzk5OCwidGFza0lkIjoiNjZjZWM0MzY2MjAxYTYzNGZlMjM2YjNlIiwiZnNBcHBJZCI6IkZTQUlEXzExNDkwZDllIn0=";
        String param2 = "eyJNc2dUeXBlIjoiaW50ZXJjb25uZWN0TXNnIiwiZnNFYSI6Ijg5NzY0IiwidXBzdHJlYW1FYSI6IjgzOTk4IiwidGFza0lkIjoiNjZjZWM0MzY2MjAxYTYzNGZlMjM2YjNlIiwiZnNBcHBJZCI6IkZTQUlEXzExNDkwZDllIn0=";
        feishuExternalController.loginAuth("1", "cli_a3ddeb52763b100c", "89764", param, param2, null, response, request);
    }
}
