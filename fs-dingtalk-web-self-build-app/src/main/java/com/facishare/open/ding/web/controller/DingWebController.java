package com.facishare.open.ding.web.controller;

import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.template.login.DingtalkLoginTemplate;
import com.facishare.open.ding.web.base.BaseController;
import com.facishare.open.ding.web.base.UserVo;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.feishu.syncapi.enums.BindTypeEnum;
import com.facishare.open.feishu.syncapi.model.info.EnterpriseTrialInfo;
import com.facishare.open.feishu.syncapi.model.login.FsUserModel;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * Created by system on 2018/4/4.
 */
@Slf4j
@RestController
@RequestMapping("/web")
public class DingWebController extends BaseController{
    @Autowired
    private DingtalkLoginTemplate dingtalkLoginTemplate;

    /**
     * 留资功能使用的接口，得到企业的最新订单情况和绑定类型
     * @return
     */
    @RequestMapping(value = "/order/getEnterpriseTrialInfo", method = RequestMethod.POST)
    @ResponseBody
    public Result<EnterpriseTrialInfo> getEnterpriseTrialInfo() {
        UserVo userVo = getUserVo();
        if(ObjectUtils.isEmpty(userVo)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        //插件直接返回
        EnterpriseTrialInfo info = new EnterpriseTrialInfo();
        info.setBindType(BindTypeEnum.manual);
        return Result.newSuccess(info);
    }

    /**
     * 用ticket获取纷享用户信息，给俊文使用
     * @param ticket
     * @return
     */
    @Deprecated
    @RequestMapping(value="/getFsUserInfo",method = RequestMethod.GET)
    @ResponseBody
    public com.facishare.open.feishu.syncapi.result.Result<FsUserModel> getFsUserInfo(@RequestParam String ticket) {
        LogUtils.info("DingWebController.getFsUserInfo,ticket={}",ticket);
        MethodContext context = MethodContext.newInstance(ticket);
        dingtalkLoginTemplate.getFsUserInfoByTicket(context);
        com.facishare.open.feishu.syncapi.result.Result<FsUserModel> fsUser = context.getResultData();
        LogUtils.info("DingWebController.getFsUserInfo,fsUser={}",fsUser);
        return fsUser;
    }
}
