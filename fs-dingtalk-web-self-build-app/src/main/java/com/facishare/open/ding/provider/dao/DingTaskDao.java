package com.facishare.open.ding.provider.dao;

import com.facishare.open.ding.api.vo.DingTaskVo;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.*;
import org.joda.time.DateTime;

import java.sql.Timestamp;
import java.util.List;

public interface DingTaskDao extends I<PERSON>rudMapper<DingTaskVo> {

    @Select("SELECT count(*) from ding_source_task where ei = #{ei} and source_id = #{sourceId} and status = '1'")
    Integer getSourceCount(@Param("ei") int ei, @Param("sourceId") String sourceId);

    @Select("SELECT * from ding_source_task where ei = #{ei} and source_id = #{sourceId} and status = '1' and message_type is null ")
    List<DingTaskVo> getDingTaskVo(@Param("ei") int ei, @Param("sourceId") String sourceId);

    @Select("SELECT task_id from ding_source_task where ei = #{ei} and source_id = #{sourceId} and employee_id = #{employeeId} and status = '1'")
    String getTaskId(@Param("ei") int ei, @Param("sourceId") String sourceId, @Param("employeeId") int employeeId);

    @Select("SELECT task_id from ding_source_task where ei = #{ei} and source_id = #{sourceId} and ding_employee_id = #{dingEmployeeId} and status = '1'")
    List<String> getTaskIdByDingEmpId(@Param("ei") int ei, @Param("sourceId") String sourceId, @Param("dingEmployeeId") String dingEmployeeId);

    @Select("SELECT * from ding_source_task where ei = #{ei}   and status = '0' and update_time between #{startTime} and  #{endTime} and message_type is null")
    List<DingTaskVo> getTaskIdByDateTime(@Param("ei") int ei, @Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime);

    @Select("SELECT * from ding_source_task where ei = #{ei}   and status = '0' and source_id =#{sourceId} and message_type is null")
    List<DingTaskVo> getTaskIdByCompleteTime(@Param("ei") int ei, @Param("sourceId") String sourceId);

    @Update("UPDATE ding_source_task " +
            "SET status = #{status} " +
            "where ei = #{ei} and employee_id = #{employeeId} and source_id = #{sourceId} and status = '1'")
    Integer updateStatus(@Param("status") int status, @Param("ei") int ei, @Param("sourceId") String sourceId, @Param("employeeId") int employeeId);

    @Update("UPDATE ding_source_task " +
            "SET status = #{status} " +
            "where ei = #{ei} and ding_employee_id = #{dingEmployeeId} and task_id = #{taskId} and status = '1'")
    Integer updateByTaskId(@Param("status") int status, @Param("ei") int ei, @Param("taskId") String taskId, @Param("dingEmployeeId") String dingEmployeeId);

    @Insert("INSERT ding_source_task " +
            "(ei, employee_id, source_id, task_id, creator_employee_id,data_center_id,message_type,ding_employee_id) " +
            "VALUES(#{dingTaskVo.ei}, #{dingTaskVo.employeeId}, #{dingTaskVo.sourceId}, " +
            "#{dingTaskVo.taskId}, #{dingTaskVo.creatorEmployeeId},#{dingTaskVo.dataCenterId},#{dingTaskVo.messageType},#{dingTaskVo.dingEmployeeId})")
    Integer insertSource(@Param("dingTaskVo") DingTaskVo dingTaskVo);

    @Insert("<script>" +
            "INSERT INTO ding_source_task " +
            "(ei, employee_id, source_id, task_id, creator_employee_id, data_center_id, message_type) " +
            "VALUES " +
            "<foreach collection='dingTaskVos' item='dingTaskVo' separator=',' >" +
            "(#{dingTaskVo.ei}, #{dingTaskVo.employeeId}, #{dingTaskVo.sourceId}, " +
            "#{dingTaskVo.taskId}, #{dingTaskVo.creatorEmployeeId}, #{dingTaskVo.dataCenterId}, #{dingTaskVo.messageType})" +
            "</foreach>" +
            "</script>")
    Integer insertSourceList(@Param("dingTaskVos") List<DingTaskVo> dingTaskVos);

    @Delete("<script>" +
            "Delete from ding_source_task where ei = #{ei} and source_id = #{sourceId} and status = '1' and employee_id in " +
            "<foreach collection='deleteEmployeeIds' item='item' separator=',' open='(' close=')' >" +
            "#{item} " +
            "</foreach> " +
            "</script>")
    Integer updateExecutor(@Param("deleteEmployeeIds") List<Integer> deleteEmployeeIds, @Param("ei") int ei, @Param("sourceId") String sourceId);
}
