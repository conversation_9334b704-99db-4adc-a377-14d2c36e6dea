package com.facishare.open.ding.provider.task.executor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.common.model.Dept;
import com.facishare.open.ding.common.model.User;
import com.facishare.open.ding.common.model.UserDetailVo;
import com.facishare.open.ding.common.utils.BeanUtil;
import com.facishare.open.ding.provider.dingding.DingRequestUtil;
import com.facishare.open.ding.provider.manager.DingMappingEmployeeManager;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2020/7/24 10:10
 * @Version 1.0
 */
@Slf4j
public class InitEmployeeTask implements Runnable {

    private Integer ei;
    private Long deptId;
    private String deptName;
    private Integer userId;
    private String accessToken;
    private String clientIp;
    private DingMappingEmployeeManager dingMappingEmployeeManager;
    private DingEnterpriseResult enterprise;
    public InitEmployeeTask( DingMappingEmployeeManager dingMappingEmployeeManager,Integer ei, Integer userId, Long deptId, String deptName,String clientIp,String accessToken, DingEnterpriseResult enterprise) {
        this.ei = ei;
        this.deptId = deptId;
        this.deptName = deptName;
        this.userId=userId;
        this.clientIp=clientIp;
        this.accessToken=accessToken;
        this.dingMappingEmployeeManager=dingMappingEmployeeManager;
        this.enterprise=enterprise;

    }

    @Override
    public void run() {
        UserDetailVo userDetailVo = DingRequestUtil.queryUserByDeptId(clientIp, accessToken, deptId.intValue(), Long.valueOf(enterprise.getAgentId()));

        if (ObjectUtils.isEmpty(userDetailVo)||CollectionUtils.isEmpty(userDetailVo.getUserlist())) {
            log.info("this dept no user, ei={},deptId={},deptName={}.", ei, deptId, deptName);
            return;
        }
        List<User> deptUsers=Lists.newArrayList();
        userDetailVo.getUserlist().stream().forEach(item ->{
            User user=new User();
            BeanUtils.copyProperties(item,user);
            log.info("item :{},user:{}",item,user);
            deptUsers.add(user);
        });

        //保存钉钉员工信息
        log.info("this dept  user={}, ei={},deptId={},deptName={}.,userSize:{}", deptUsers,ei, deptId, deptName,deptUsers.size());
        Integer count = dingMappingEmployeeManager.initMappingEmployee(deptUsers, ei, userId, deptId, deptName);
        if (Objects.nonNull(count) && count.equals(deptUsers.size())) {
            log.info("初始化员工成功" + count + "条");
        } else {
            log.warn("初始化员工失败,员工可能已经存在,deptId={},deptName={}.", deptId, deptName);
        }
    }
}
