package com.facishare.open.ding.web.job;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.service.EnterpriseService;
import com.facishare.open.ding.common.model.bizlog.DingProxyLiveLog;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.utils.threadpool.ThreadPoolUtils;
import com.facishare.open.ding.web.modle.HttpResponseMessage;
import com.facishare.open.ding.web.utils.OkHttp3MonitorUtils;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.ps.ProtostuffUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.biz.model.TriggerParam;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHander;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@JobHander(value = "ProxyLiveHandler")
@Component
@Slf4j
public class ProxyLiveHandler extends IJobHandler {
    @Resource
    private EnterpriseService enterpriseService;

    @Override
    public ReturnT execute(TriggerParam triggerParam) throws Exception {
        String traceId = UUID.randomUUID().toString();
        TraceUtils.initTraceId(traceId);
        log.info("ProxyLiveHandler.execute,triggerParam={}",triggerParam);
        ThreadPoolUtils.execute(()->{
            TraceUtils.initTraceId(traceId);
            executeNow();
        });
        return new ReturnT(ReturnT.SUCCESS_CODE, "定时任务调用成功"); //ignorei18n
    }

    private void executeNow() {
        Result<List<DingEnterpriseResult>> result = enterpriseService.getAllEnterpriseBindListNotDecrypt();
        log.info("ProxyLiveHandler.executeNow,result={}",result);
        if(CollectionUtils.isNotEmpty(result.getData())) {
            for(DingEnterpriseResult dingEnterpriseResult : result.getData()) {
                if(StringUtils.isEmpty(dingEnterpriseResult.getClientIp())) {
                    log.info("ProxyLiveHandler.executeNow,clientUrl is empty,dingEnterpriseResult={}",dingEnterpriseResult);
                    continue;
                }
                String url = dingEnterpriseResult.getClientIp() + "proxy/proxyLive";
                Map<String,String> body = new HashMap<>();
                body.put("outEa",dingEnterpriseResult.getDingCorpId());
                body.put("fsEa",dingEnterpriseResult.getEa());
                body.put("agentId",dingEnterpriseResult.getAgentId());
                body.put("clientUrl",dingEnterpriseResult.getClientIp());
                DingProxyLiveLog dingProxyLiveLog = DingProxyLiveLog.builder()
                        .stamp(System.currentTimeMillis())
                        .outEa(dingEnterpriseResult.getDingCorpId())
                        .fsEa(dingEnterpriseResult.getEa())
                        .appKey(dingEnterpriseResult.getAppKey())
                        .agentId(dingEnterpriseResult.getAgentId())
                        .proxyUrl(dingEnterpriseResult.getClientIp())
                        .build();
                String json = JSONObject.toJSONString(body);
                try {
                    HttpResponseMessage response = OkHttp3MonitorUtils.sendOkHttp3Post(url, new HashMap<>(), json);
                    log.info("ProxyLiveHandler.executeNow,statusCode={}",response.getStatusCode());

                    dingProxyLiveLog.setStatusCode(response.getStatusCode()+"");

                    if(response.getStatusCode()==200) {
                        Map<String,String> responseMap = null;
                        try {
                            responseMap = JSONObject.parseObject(response.getContent(),HashMap.class);
                        } catch (Exception e) {

                        }
                        log.info("ProxyLiveHandler.executeNow,200,responseMap={}",responseMap);
                        dingProxyLiveLog.setResponse(response.getContent());
                        if(responseMap!=null && !responseMap.isEmpty()) {
                            log.info("ProxyLiveHandler.executeNow,200,proxy service is working,body={},responseMap={}",body,responseMap);
                        } else {
                            log.info("ProxyLiveHandler.executeNow,200,proxy url is wrong,body={},response={}",body,response.getContent());
                        }
                    } else if(response.getStatusCode()==404) {
                        log.info("ProxyLiveHandler.executeNow,404,proxy service is working,but the proxy is old version,body={}",body);
                        dingProxyLiveLog.setErrorMsg("proxy server is old version, no proxyLive interface");
                    } else if(response.getStatusCode()==500) {
                        log.info("ProxyLiveHandler.executeNow,{},proxy service is in exception status,body={}", response.getStatusCode(),body);
                        dingProxyLiveLog.setErrorMsg("proxy service is in exception status");
                    } else {
                        log.info("ProxyLiveHandler.executeNow,{},unknown exception status,body={}", response.getStatusCode(),body);
                        dingProxyLiveLog.setErrorMsg("proxy service is in unknown exception status");
                    }
                } catch (Exception e) {
                    String errorMsg = e.getMessage();
                    if(StringUtils.isEmpty(errorMsg)) {
                        errorMsg = e.getCause().getMessage();
                    }
                    dingProxyLiveLog.setErrorMsg(errorMsg);
                    log.info("ProxyLiveHandler.executeNow,proxy service is unavailable={},body={}",body);
                }

                log.info("ProxyLiveHandler.executeNow,dingProxyLiveLog={}",dingProxyLiveLog);
                BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(dingProxyLiveLog));
            }
        }
    }
}