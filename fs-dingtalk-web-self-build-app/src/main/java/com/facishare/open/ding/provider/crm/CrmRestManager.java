package com.facishare.open.ding.provider.crm;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.vo.CreateCrmEmployeeVo;
import com.facishare.open.ding.api.vo.DeptVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.provider.config.ConfigCenter;
import com.facishare.open.ding.provider.manager.DingEnterpriseManager;
import com.facishare.open.ding.provider.model.HttpResponseMessage;
import com.facishare.open.ding.provider.model.InnerSearchQueryInfo;
import com.facishare.open.ding.provider.utils.OkHttp3MonitorUtilsFromProvider;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Date 2021/2/23 19:45
 * @Version 1.0
 */
@Service
@Slf4j
public class CrmRestManager {

    @Autowired
    private DingEnterpriseManager dingEnterpriseManager;

    private static final Pattern UserPATTERN = Pattern.compile("[a-zA-Z\\d]+[a-zA-Z\\d@._·•()（）-]+");
    /**
     * 创建部门接口
     */
    public Result<Integer> createDept(DeptVo vo){
        String crmParentId= Objects.isNull(vo.getCrmParentId())?"99999":vo.getCrmParentId().toString();
        Integer ei=vo.getEi();
        Map<String,String> hearsMap= Maps.newHashMap();
        hearsMap.put("x-fs-ei",ei.toString());
        hearsMap.put("x-fs-userinfo","-10000");

        Map<String,Object> objectData= Maps.newHashMap();
        objectData.put("object_describe_api_name","DepartmentObj");
        objectData.put("name",vo.getName());
        objectData.put("record_type","default__c");
        objectData.put("status","0");
        objectData.put("manager_id",Objects.isNull(vo.getCrmDeptOwner())? null:Lists.newArrayList(vo.getCrmDeptOwner().toString()));
        objectData.put("parent_id",Lists.newArrayList(crmParentId));
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("object_data", objectData);
        log.info("createDept,objectData={}", objectData);
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtilsFromProvider.sendOkHttp3Post(CrmUrlUtils.createDeptUrl(), hearsMap, JSONObject.toJSONString(paramMap));
        log.info("createDept,httpResponseMessage={}", httpResponseMessage);
        Integer code=Integer.parseInt(JSONPath.read(httpResponseMessage.getContent(), "$.code").toString());
        String message=JSONPath.read(httpResponseMessage.getContent(), "$.message").toString();
        log.info("trace ei:{}, vo:{}, crm createDept message:{}",vo.getEi(),vo,httpResponseMessage.getContent());
        if(ResultCode.SUCCESS.getErrorCode().equals(code)){
            Object deptId = JSONPath.read(httpResponseMessage.getContent(), "$.data.objectData._id");
           return Result.newSuccess(Integer.parseInt(deptId.toString()));
        }
       return Result.newError(code,message);
    }


    /**
     * 修改部门部门接口
     */
    public Result<Void> modifyDept(DeptVo vo){
        Integer ei=vo.getEi();
        String managerId= Objects.isNull(vo.getCrmDeptOwner())? Strings.EMPTY:vo.getCrmDeptOwner().toString();
        String crmParentId= Objects.isNull(vo.getCrmParentId())?"99999":vo.getCrmParentId().toString();
        Map<String,String> hearsMap= Maps.newHashMap();
        hearsMap.put("x-fs-ei",ei.toString());
        hearsMap.put("x-fs-userinfo","-10000");

        Map<String,Object> objectData= Maps.newHashMap();
        Map<String, Object> modifyMap = Maps.newHashMap();
        objectData.put("_id",String.valueOf(vo.getCrmDeptId()));
        objectData.put("name",vo.getName());
        objectData.put("record_type","default__c");
        objectData.put("status","0");
        if(ObjectUtils.isNotEmpty(vo.getCrmDeptOwner())){
            //负责人
            objectData.put("manager_id",Lists.newArrayList(managerId));
        }
        objectData.put("parent_id",Lists.newArrayList(crmParentId));

        modifyMap.put("data", objectData);
        log.info("modifyDept,objectData={}", objectData);
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtilsFromProvider.sendOkHttp3Post(CrmUrlUtils.updateDeptUrl(), hearsMap, JSONObject.toJSONString(modifyMap));
        log.info("modifyDept,httpResponseMessage={}", httpResponseMessage);
        Integer code=Integer.parseInt(JSONPath.read(httpResponseMessage.getContent(), "$.code").toString());
        String message=JSONPath.read(httpResponseMessage.getContent(), "$.message").toString();
        log.info("trace ei:{}, vo:{}, crm modifyDept message:{}",vo.getEi(),vo,httpResponseMessage.getContent());
        return Result.newError(code,message);
    }

    /**
     * 停用部门接口
     */
    public Result<Void> stopDept(DeptVo vo){
        Integer ei=vo.getEi();
        Map<String,String> hearsMap= Maps.newHashMap();
        hearsMap.put("x-fs-ei",ei.toString());
        hearsMap.put("x-fs-userinfo","-10000");

        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("dataIds", Lists.newArrayList(vo.getCrmDeptId().toString()));
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtilsFromProvider.sendOkHttp3Post(CrmUrlUtils.stopDeptUrl(), hearsMap, JSONObject.toJSONString(paramMap));
        Integer code=Integer.parseInt(JSONPath.read(httpResponseMessage.getContent(), "$.code").toString());
        String message=JSONPath.read(httpResponseMessage.getContent(), "$.message").toString();
        log.info("trace ei:{}, vo:{}, crm stop message:{}",vo.getEi(),vo,httpResponseMessage.getContent());

        return Result.newError(code,message);
    }

    /**
     * 创建员工
     */
    public Result<Integer> createEmp(CreateCrmEmployeeVo vo){
        log.info("createEmp,vo={}",vo);
        Integer ei=vo.getEi();
        Map<String,String> hearsMap=Maps.newHashMap();
        hearsMap.put("x-fs-ei",ei.toString());
        hearsMap.put("x-fs-userinfo","-10000");

        Map<String,Object> objectData= Maps.newHashMap();
        objectData.put("object_describe_api_name","PersonnelObj");

        objectData.put("name",vo.getName());
        objectData.put("full_name",vo.getName());
        objectData.put("record_type","default__c");
        objectData.put("status","0");
        objectData.put("position",vo.getPosition());
        Result<DingEnterpriseResult> enterprise = dingEnterpriseManager.queryEnterpriseByEi(ei);
        if(ConfigCenter.DATA_GRAY_TENANTS.contains(enterprise.getData().getEa())){
            objectData.put("email", vo.getEmail());
            objectData.put("is_pause_login", true);
        }
        objectData.put("employee_number", vo.getEmployeeNumber());
        if(vo.getSexType() == null || vo.getSexType() == 0) {
            objectData.put("sex","M");
        } else {
            objectData.put("sex","F");
        }
        objectData.put("main_department",Lists.newArrayList(vo.getCrmMainDeptId()));
        objectData.put("phone",vo.getMobile());
        //登录账号
        //不支持国外
        if(StringUtils.isNotEmpty(vo.getMobile()) && vo.getMobile().matches("[a-zA-Z\\d]+[a-zA-Z\\d@._·•()（）-]+")) {
            objectData.put("user_name",vo.getMobile());
        }
        //附属部门
        objectData.put("vice_departments",vo.getCrmViceDepts());
        if(ObjectUtils.isNotEmpty(vo.getLeader())){
            objectData.put("leader",Lists.newArrayList(vo.getLeader().toString()));
        }
        if(vo.getHiredDate()!=null) {
            //入职日期
            objectData.put("date_of_joining",vo.getHiredDate());
        }

        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("object_data", objectData);
        log.info("createEmp,paramMap={}",paramMap);
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtilsFromProvider.sendOkHttp3Post(CrmUrlUtils.createUserUrl(), hearsMap, JSONObject.toJSONString(paramMap));
        log.info("createEmp,httpResponseMessage={}",httpResponseMessage);
        Integer code=Integer.parseInt(JSONPath.read(httpResponseMessage.getContent(), "$.code").toString());
        String message=JSONPath.read(httpResponseMessage.getContent(), "$.message").toString();

        log.info("trace crm createEmp message:{}",httpResponseMessage.getContent());

        if(ResultCode.SUCCESS.getErrorCode().equals(code)){
            Object emp = JSONPath.read(httpResponseMessage.getContent(), "$.data.objectData.user_id");
            return Result.newSuccess(Integer.parseInt(emp.toString()));
        }
        return Result.newError(code,message);
    }


    /**
     * 修改员工
     */
    public Result<Integer> modifyEmp(CreateCrmEmployeeVo vo){
        log.info("modifyEmp,vo={}",vo);
        //之前员工编辑_id与userid相同的。所以数据库设计没有保存_id.只能每次修改的时候查询一次员工接口获取_id;

        Result<Map<String, Object>> userIds = queryByPhone(vo.getEi(), "user_id", vo.getCrmEmpId().toString());

        Integer ei=vo.getEi();
        Map<String,String> hearsMap=Maps.newHashMap();
        hearsMap.put("x-fs-ei",ei.toString());
        hearsMap.put("x-fs-userinfo","-10000");
        Map<String,Object> objectData= Maps.newHashMap();
        objectData.put("object_describe_api_name","PersonnelObj");
        objectData.put("_id",userIds.getData().get("object_id"));
        objectData.put("name",vo.getName());
        objectData.put("full_name",vo.getName());
        objectData.put("position",vo.getPosition());
        Result<DingEnterpriseResult> enterprise = dingEnterpriseManager.queryEnterpriseByEi(ei);
        if(ConfigCenter.DATA_GRAY_TENANTS.contains(enterprise.getData().getEa())){
            objectData.put("email", vo.getEmail());
        }
        if(vo.getSexType() == null || vo.getSexType() == 0) {
            objectData.put("sex","M");
        } else {
            objectData.put("sex","F");
        }
        objectData.put("employee_number", vo.getEmployeeNumber());
        objectData.put("record_type","default__c");
        objectData.put("status","0");
        objectData.put("main_department",Lists.newArrayList(vo.getCrmMainDeptId()));
        objectData.put("phone",vo.getMobile());
        //登录账号
        //更新的时候，不应该更新人员账号
//        objectData.put("user_name",vo.getMobile());
        //附属部门
        objectData.put("vice_departments",vo.getCrmViceDepts());
        if(ObjectUtils.isNotEmpty(vo.getLeader())){
            objectData.put("leader",Lists.newArrayList(vo.getLeader().toString()));
        }
        if(vo.getHiredDate()!=null) {
            //入职日期
            objectData.put("date_of_joining",vo.getHiredDate());
        }

        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("object_data", objectData);

        log.info("modifyEmp,paramMap={}",paramMap);
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtilsFromProvider.sendOkHttp3Post(CrmUrlUtils.modifyUserUrl(), hearsMap, JSONObject.toJSONString(paramMap));
        log.info("modifyEmp,httpResponseMessage={}",httpResponseMessage);
        Integer code=Integer.parseInt(JSONPath.read(httpResponseMessage.getContent(), "$.code").toString());
        String message=JSONPath.read(httpResponseMessage.getContent(), "$.message").toString();
        log.info("trace crm modifyEmp vo:{},message:{}",vo,httpResponseMessage.getContent());
        return Result.newError(code,message);
    }

    /**
     * 停用员工
     */
    public Result<Integer> stopEmp(Integer crmEmpId,Integer enterpriseId){
        Map<String,String> hearsMap=Maps.newHashMap();
        hearsMap.put("x-fs-ei",enterpriseId.toString());
        hearsMap.put("x-fs-userinfo","-10000");
        Object object_id = queryByPhone(enterpriseId,"user_id",crmEmpId.toString()).getData().get("object_id");
        if(ObjectUtils.isEmpty(object_id))return Result.newSuccess();
        Map<String,Object> objectData= Maps.newHashMap();
        objectData.put("object_describe_api_name","PersonnelObj");
        objectData.put("_id",object_id.toString());
        objectData.put("status","1");
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("object_data", objectData);
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtilsFromProvider.sendOkHttp3Post(CrmUrlUtils.stopUserUrl(), hearsMap, JSONObject.toJSONString(paramMap));
        Integer code=Integer.parseInt(JSONPath.read(httpResponseMessage.getContent(), "$.code").toString());
        String message=JSONPath.read(httpResponseMessage.getContent(), "$.message").toString();
        log.info("trace crm stop message:{}",httpResponseMessage.getContent());
        return Result.newError(code,message);
    }

    /**
     * 根据手机号查询对象数据
     */
    public Result<Map<String,Object>> queryByPhone(Integer enterpriseId,String filed,String filedValue){
        Map<String,String> hearsMap=Maps.newHashMap();
        hearsMap.put("x-fs-ei",enterpriseId.toString());
        hearsMap.put("x-fs-userinfo","-10000");

        InnerSearchQueryInfo innerSearchQueryInfo = new InnerSearchQueryInfo();
        InnerSearchQueryInfo.Filter orderIdFilter = new InnerSearchQueryInfo.Filter();
        orderIdFilter.setOperator("EQ");
        orderIdFilter.setField_name(filed);
        orderIdFilter.setField_values(Lists.newArrayList(filedValue));

        List<InnerSearchQueryInfo.Filter> filters = Lists.newArrayList(orderIdFilter);

        innerSearchQueryInfo.setFilters(filters);
        innerSearchQueryInfo.setOffset(0);
        innerSearchQueryInfo.setLimit(100);
        InnerSearchQueryInfo.Order order=new InnerSearchQueryInfo.Order();
        order.setFieldName("last_modified_time");
        order.setAsc(false);
        List<InnerSearchQueryInfo.Order> orders = Lists.newArrayList(order);
        innerSearchQueryInfo.setOrders(orders);

        Map<String, Object> queryMap = Maps.newHashMap();
        Gson gson=new Gson();
        queryMap.put("object_describe_api_name", "PersonnelObj");
        queryMap.put("search_query_info", gson.toJson(innerSearchQueryInfo));
        HttpResponseMessage httpResponseMessage = OkHttp3MonitorUtilsFromProvider.sendOkHttp3Post(CrmUrlUtils.queryList("/PersonnelObj"), hearsMap, JSONObject.toJSONString(queryMap));
        log.info("query emp obj:arg:{},result:{}",queryMap,httpResponseMessage.getContent());
        JSONArray read = (JSONArray) JSONPath.read(httpResponseMessage.getContent(), "$.data.dataList");
        if(read.size()==0){
            log.warn("query by phone failed,arg:{},result:{}",innerSearchQueryInfo,httpResponseMessage.getContent());
            Map<String, Object> objectMap = Maps.newHashMap();
            return Result.newSuccess(objectMap);
        }
        String userID = JSONPath.read(httpResponseMessage.getContent(), "$.data.dataList[0].user_id").toString();
        String object_id = JSONPath.read(httpResponseMessage.getContent(), "$.data.dataList[0]._id").toString();
        Map<String, Object> objectMap = Maps.newHashMap();
        objectMap.put("user_id",userID);
        objectMap.put("object_id",object_id);
        return Result.newSuccess(objectMap);
    }

}
