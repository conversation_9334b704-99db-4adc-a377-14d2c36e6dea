package com.facishare.open.ding.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson.TypeReference;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.request.OapiSnsGetuserinfoBycodeRequest;
import com.dingtalk.api.response.OapiSnsGetuserinfoBycodeResponse;
import com.dingtalk.api.response.OapiUserGetuserinfoResponse;
import com.dingtalk.oapi.lib.aes.DingTalkEncryptor;
import com.dingtalk.oapi.lib.aes.DingTalkJsApiSingnature;
import com.dingtalk.oapi.lib.aes.Utils;

import com.facishare.open.ding.api.enums.GenerateUrlTypeEnum;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.service.DingtalkUserService;
import com.facishare.open.ding.api.service.EnterpriseService;
import com.facishare.open.ding.api.service.ObjectMappingService;
import com.facishare.open.ding.api.service.RedisDingService;
import com.facishare.open.ding.api.vo.DingUserVo;
import com.facishare.open.ding.common.model.EmployeeDingVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.common.utils.BeanUtil;
import com.facishare.open.ding.common.utils.HttpUtils;
import com.facishare.open.ding.template.login.DingtalkLoginTemplate;
import com.facishare.open.ding.web.base.BaseController;
import com.facishare.open.ding.web.base.UserVo;
import com.facishare.open.ding.web.config.ConfigCenter;
import com.facishare.open.ding.web.constants.Constant;
import com.facishare.open.ding.web.dingding.DingRequestUtil;
import com.facishare.open.ding.web.dingding.DingUrl;
import com.facishare.open.ding.web.dingding.DingUserInfo;
import com.facishare.open.ding.web.handler.QPSLimitHandler;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.feishu.syncapi.model.ticket.GenFsTicketModel;
import com.facishare.restful.common.StopWatch;
import com.facishare.userlogin.api.model.CreateUserTokenDto;
import com.facishare.userlogin.api.model.UserTokenDto;
import com.facishare.userlogin.api.service.SSOLoginService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.taobao.api.ApiException;
import jdk.nashorn.internal.ir.RuntimeNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.util.EntityUtils;
import org.aspectj.lang.annotation.Before;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.owasp.encoder.Encode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * <p>业务回调接口</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019-08-20 10:03
 */
@CrossOrigin
@Slf4j
@RestController
@RequestMapping("/business")
public class CallBackController extends BaseController {

    @Autowired
    private EnterpriseService enterpriseService;

    @Autowired
    private ObjectMappingService objectMappingService;

    @Autowired
    private SSOLoginService ssoLoginService;

    @Autowired
    private RedisDingService redisDingService;

    @Autowired
    private DingtalkLoginTemplate dingtalkLoginTemplate;

    private static final String ADD_USER = "user_add_org";

    private static final String MODIFY_USER = "user_modify_org";

    private static final String LEAVE_USER = "user_leave_org";

    private static final String CREATE_DEPT = "org_dept_create";

    private static final String MODIFY_DEPT = "org_dept_modify";

    private static final String REMOVE_DEPT = "org_dept_remove";
    private static final String SYNC_HTTP_PUSH_MEDIUM = "SYNC_HTTP_PUSH_MEDIUM";

    @ReloadableProperty("sso.redirect.url")
    private String ssoRedirectUrl;

    @ReloadableProperty("sso.source.web.url")
    private String ssoRedirectWebUrl;

    @ReloadableProperty("sso.source.detail.h5.url")
    private String ssoSourceDetailH5Url;

    @ReloadableProperty("sso.source.bpm.h5.url")
    private String ssoSourceBpmH5Url;
    @ReloadableProperty("h5Url")
    private String h5Url;

    @ReloadableProperty("new.sso.source.detail.h5.url")
    private String newSsoSourceDetailH5Url;

//    @ReloadableProperty("proxy_tenant_ids")
//    private String proxyTenantIds;

    private List<String> whiteLists = new ArrayList<>();
    private static final String DING_REDIRECT_URL = "dingtalk://dingtalkclient/page/link?url=";

    private static final String GET_USER_URL = "https://oapi.dingtalk.com/sns/getuserinfo_bycode";

    ExecutorService executorService = Executors.newFixedThreadPool(20);
    private static Integer DING_INVALID_ACCESSTOKEN = 40014;

    /**
     * 相应钉钉回调时的值
     */
    private static final String CALLBACK_RESPONSE_SUCCESS = "success";

    private static final String CALLBACK_RESPONSE_FAIL = "fail";

    //业务流程详情页
    private static final String BPM_URL_TYPE = "2";

    @Autowired
    private QPSLimitHandler qpsLimitHandler;

    private LoadingCache<String, String> cache = Caffeine.newBuilder().maximumSize(10000).expireAfterWrite(80, TimeUnit.MINUTES).refreshAfterWrite(60, TimeUnit.MINUTES).build(new CacheLoader<String, String>() {
        @Nullable
        @Override
        public String load(@NonNull String key) throws Exception {
            Result<DingEnterpriseResult> dingEnterpriseResultResult = enterpriseService.queryEnterpriseByEi(Integer.valueOf(key));
            DingEnterpriseResult result = dingEnterpriseResultResult.getData();
            return DingRequestUtil.getToken(result.getClientIp(), result.getAppKey(), result.getAppSecret());
        }
    });


    //钉钉企业绑定时，需要注册的回调接口
    // 钉钉员工变动都会调用该接口
    @RequestMapping(value = "/callback", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String callback(@RequestParam(value = "signature", required = false) String signature,
                           @RequestParam(value = "timestamp", required = false) String timestamp,
                           @RequestParam(value = "nonce", required = false) String nonce,
                           @RequestParam(value = "ei", required = false) Integer ei,
                           @RequestBody(required = false) JSONObject json) {

        log.info("callback:param signature:{},timestamp:{},nonce:{},ei:{},json:{}", signature, timestamp, nonce, ei, json);

        try {
            if (Objects.isNull(ei)) {
                log.warn("param ei is null");
                return CALLBACK_RESPONSE_FAIL;
            }
            Result<DingEnterpriseResult> result = enterpriseService.queryEnterpriseByEi(ei);
            if (Objects.isNull(result) || Objects.isNull(result.getData())) {
                log.warn("the fx enterprise is not binded, ei={}.", ei);
                return CALLBACK_RESPONSE_FAIL;
            }
            DingTalkEncryptor dingTalkEncryptor = new DingTalkEncryptor(Constant.TOKEN, Constant.ENCODING_AES_KEY,
                    result.getData().getDingCorpId());
            //从post请求的body中获取回调信息的加密数据进行解密处理
            String encryptMsg = json.getString("encrypt");
            String plainText = dingTalkEncryptor.getDecryptMsg(signature, timestamp, nonce, encryptMsg);
            log.info("plainText={}", plainText);
            JSONObject obj = JSON.parseObject(plainText);
            Gson gson = new Gson();
            //根据回调数据类型做不同的业务处理
            String eventType = obj.getString("EventType");
            String corpId = obj.getString("CorpId");
            //匹配事件類型
            //请求客户的服务器有时候会比较慢，需要异步请求
            switch (eventType) {
                case ADD_USER:
                    List<String> AddUserIds = gson.fromJson(obj.getString("UserId"), List.class);
                    log.info("新增钉钉员工，ei:{},userId={}.", ei, obj.getString("UserId"));
                    objectMappingService.updateEmp(ei, AddUserIds);
                    break;
                case MODIFY_USER:
                    List<String> modifyUserIds = gson.fromJson(obj.getString("UserId"), List.class);
                    log.info("钉钉员工发生修改，ei:{},userId={}.", ei, obj.getString("UserId"));
                    objectMappingService.updateEmp(ei, modifyUserIds);
                    break;
                case LEAVE_USER:
                    List<String> leaveUserIds = gson.fromJson(obj.getString("UserId"), List.class);
                    log.info("钉钉员工离职，ei:{},userId={}.", ei, obj.getString("UserId"));
                    objectMappingService.stopEmp(ei, leaveUserIds);
                    break;
                //部門
                case CREATE_DEPT:
                    List<Long> createDepts = gson.fromJson(obj.getString("DeptId"), new TypeToken<ArrayList<Long>>() {
                    }.getType());

                    log.info("新增钉钉部门，ei:{},createDepts={}.", ei, createDepts);
                    objectMappingService.createFxDept(ei, createDepts);
                    break;
                case MODIFY_DEPT:
                    List<Long> modifyDepts = gson.fromJson(obj.getString("DeptId"), new TypeToken<ArrayList<Long>>() {
                    }.getType());
                    log.info("修改钉钉部门，ei:{},modifyDepts={}.", ei, modifyDepts);
                    objectMappingService.modifyFxDept(ei, modifyDepts);
                    break;
                case REMOVE_DEPT:
                    List<Long> removeDepts = gson.fromJson(obj.getString("DeptId"), new TypeToken<ArrayList<Long>>() {
                    }.getType());
                    log.info("刪除钉钉部门，ei:{},removeDepts={}.", ei, removeDepts);
                    objectMappingService.removeFxDept(ei, removeDepts);
                    break;
                default:
                    break;
            }

            // 返回success的加密信息表示回调处理成功
            Map<String, String> map = dingTalkEncryptor.getEncryptedMap(CALLBACK_RESPONSE_SUCCESS, System.currentTimeMillis(), Utils.getRandomStr(8));
            JSONObject jsonObject = new JSONObject();
            jsonObject.putAll(map);
            return jsonObject.toString();
        } catch (Exception e) {
            log.warn("process callback failed，e={}", e);
            return CALLBACK_RESPONSE_FAIL;
        }
        //回调企业部门的变更事件
    }


    //钉钉应用维度注册
    // 钉钉员工变动都会调用该接口
    @RequestMapping(value = "/appCallBack", method = RequestMethod.POST, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public String appCallBack(@RequestParam(value = "signature", required = false) String signature,
                              @RequestParam(value = "timestamp", required = false) String timestamp,
                              @RequestParam(value = "nonce", required = false) String nonce,
                              @RequestParam(value = "ei", required = false) Integer ei,
                              @RequestParam(value = "appKey", required = false) String appkey,
                              @RequestBody(required = false) JSONObject json) {

        log.info("appCallBack:param signature:{},timestamp:{},nonce:{},ei:{},json:{}，appkey：{}", signature, timestamp, nonce, ei, json, appkey);

        try {
            DingTalkEncryptor dingTalkEncryptor = new DingTalkEncryptor(Constant.TOKEN, Constant.ENCODING_AES_KEY,
                    appkey);
            //从post请求的body中获取回调信息的加密数据进行解密处理
            String encryptMsg = json.getString("encrypt");
            String plainText = dingTalkEncryptor.getDecryptMsg(signature, timestamp, nonce, encryptMsg);
            // 返回success的加密信息表示回调处理成功
            Map<String, String> map = dingTalkEncryptor.getEncryptedMap(CALLBACK_RESPONSE_SUCCESS, System.currentTimeMillis(), Utils.getRandomStr(8));
            JSONObject jsonObject = new JSONObject();
            jsonObject.putAll(map);
            log.info("appCallBack,plainText={}", plainText);
            JSONObject obj = JSON.parseObject(plainText);
            Gson gson = new Gson();
            //根据回调数据类型做不同的业务处理
            String eventType = obj.getString("EventType");
            String corpId = obj.getString("CorpId");
            //为了兼容一个钉钉对应多个CRM租户的情况，我们默认一个appKey只能映射一个CRM
            Result<DingEnterpriseResult> enterprise = enterpriseService.queryEnterpriseByAppKey(appkey);
            if(ObjectUtils.isEmpty(enterprise.getData())){
                log.warn("appCall back enterprise not exists,appKey:{}",appkey);
                return jsonObject.toJSONString();
            }
            ei=enterprise.getData().getEi();
            //匹配事件類型
            //请求客户的服务器有时候会比较慢，需要异步请求
            switch (eventType) {
                case ADD_USER:
                    List<String> AddUserIds = gson.fromJson(obj.getString("UserId"), List.class);
                    log.info("新增钉钉员工，ei:{},userId={}.", ei, obj.getString("UserId"));
                    objectMappingService.updateEmp(ei, AddUserIds);
                    break;
                case MODIFY_USER:
                    List<String> modifyUserIds = gson.fromJson(obj.getString("UserId"), List.class);
                    log.info("钉钉员工发生修改，ei:{},userId={}.", ei, obj.getString("UserId"));
                    objectMappingService.updateEmp(ei, modifyUserIds);
                    break;
                case LEAVE_USER:
                    List<String> leaveUserIds = gson.fromJson(obj.getString("UserId"), List.class);
                    log.info("钉钉员工离职，ei:{},userId={}.", ei, obj.getString("UserId"));
                    objectMappingService.stopEmp(ei, leaveUserIds);
                    break;
                //部門
                case CREATE_DEPT:
                    List<Long> createDepts = gson.fromJson(obj.getString("DeptId"), new TypeToken<ArrayList<Long>>() {
                    }.getType());

                    log.info("新增钉钉部门，ei:{},createDepts={}.", ei, createDepts);
                    objectMappingService.createFxDept(ei, createDepts);
                    break;
                case MODIFY_DEPT:
                    List<Long> modifyDepts = gson.fromJson(obj.getString("DeptId"), new TypeToken<ArrayList<Long>>() {
                    }.getType());
                    log.info("修改钉钉部门，ei:{},modifyDepts={}.", ei, modifyDepts);
                    objectMappingService.modifyFxDept(ei, modifyDepts);
                    break;
                case REMOVE_DEPT:
                    List<Long> removeDepts = gson.fromJson(obj.getString("DeptId"), new TypeToken<ArrayList<Long>>() {
                    }.getType());
                    log.info("刪除钉钉部门，ei:{},removeDepts={}.", ei, removeDepts);
                    objectMappingService.removeFxDept(ei, removeDepts);
                    break;
                default:
                    break;
            }

            return jsonObject.toString();
        } catch (Exception e) {
            log.warn("process callback failed，e={}", e);
            return CALLBACK_RESPONSE_FAIL;
        }

    }


    //纷享的代办消息推送到钉钉时，会将该接口的url嵌入消息中，点击代办消息时会进入到该接口，钉钉点击代办
    @RequestMapping(value = "/authorize", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Object authorize(@RequestParam(value = "code", required = false) String code,
                            @RequestParam(value = "state", required = false) String state,
                            @RequestParam(value = "apiname", required = false) String apiName,
                            @RequestParam(value = "id", required = false) String dataId,
                            @RequestParam(value = "instanceId", required = false) String instanceId,
                            @RequestParam(value = "taskId", required = false) String taskId,
                            @RequestParam(value = "ei", required = false) Integer ei,
                            @RequestParam(value = "bizType", required = false) String bizType,
                            @RequestHeader(required = false, value = "User-Agent") String userAgent,
                            @RequestParam(value = "url", required = false) String url) {
        //查询绑定企业
        Result<DingEnterpriseResult> result = enterpriseService.queryEnterpriseByEi(ei);

        if (Objects.isNull(result) || Objects.isNull(result.getData())) {
            log.warn("the fx enterprise is not binded, ei={}.", ei);
            return CALLBACK_RESPONSE_FAIL;
        }
        String redirectAppId = result.getData().getRedirectAppId();
        String redirectAppSecret = result.getData().getRedirectAppSecret();
        String appId = result.getData().getAgentId();
        String corpId = result.getData().getDingCorpId();
        String fsEa = result.getData().getEa();

        Gson gson = new Gson();
        Map<String, String> argMap = new HashMap<>();
        argMap.put("redirectAppId", redirectAppId);
        argMap.put("redirectAppSecret", redirectAppSecret);
        argMap.put("token", result.getData().getToken());
        argMap.put("code", code);
        //获取用户信息
        String getUserUrl = result.getData().getClientIp() + "proxy/getUserByCode";
        log.info("getUserUrl={}, userAgent={}", getUserUrl, userAgent);
        Boolean isQPSLimit = qpsLimitHandler.isQPSLimitByEa(result.getData().getEa());
        if(isQPSLimit) {
            //限流且重试多次失败
            log.info("CallBackController.authorize,query user detail failed.qpsLimit.ei={},code={}", ei, code);
            return CALLBACK_RESPONSE_FAIL;
        }
        CloseableHttpResponse response = HttpUtils.httpPost(getUserUrl, gson.toJson(argMap), null);
        String entity = null;
        try {
            entity = EntityUtils.toString(response.getEntity(), "UTF-8");
            log.info("callBackController entity :{}", entity);
        } catch (IOException e) {
            e.printStackTrace();
        }
        OapiSnsGetuserinfoBycodeResponse.UserInfo dingUser = gson.fromJson(entity, OapiSnsGetuserinfoBycodeResponse.UserInfo.class);
        //存储DingUser到Redis，因为在点击代办消息的时候，安卓机器会触发两次redirect_url请求，code会失效会返回空的信息
        log.info("dinguser callbackController:{}", dingUser);
        if (!Objects.isNull(dingUser)) {
            DingUserVo vo = BeanUtil.copy(dingUser, DingUserVo.class);
            redisDingService.saveInfoToRedis(code, vo);
        } else {
            Result<DingUserVo> infos = redisDingService.getInfoFromRedis(code);
            log.info("redisDingService getInfo:{}", infos.getData());
            dingUser = BeanUtil.copy(infos.getData(), OapiSnsGetuserinfoBycodeResponse.UserInfo.class);
        }
        log.info("redis info:{}", dingUser);
        dingUser = Optional.ofNullable(dingUser).orElse(new OapiSnsGetuserinfoBycodeResponse.UserInfo());
        String name = dingUser.getNick();
        String openId = dingUser.getOpenid();
        String unionId = dingUser.getUnionid();
        log.info("dingUser:{},name={},openId={},unionId={}", dingUser, name, openId, unionId);
        //根据员工绑定关系获取纷享员工身份
        Result<DingMappingEmployeeResult> mappingResult = objectMappingService.queryEmployeeByUnionId(ei, unionId);
        log.info("authorize,mappingResult={}", mappingResult);
        if (Objects.isNull(mappingResult.getData()) || StringUtils.isEmpty(mappingResult.getData().getEmployeeId())) {
            log.info("the emp not bind");
            return Result.newError(ResultCode.NOT_BIND_EMP);
        }
        Integer fsUserId = mappingResult.getData().getEmployeeId();
        String outUserId = mappingResult.getData().getDingEmployeeId();
//        CreateUserTokenDto.Argument userTokenArg = new CreateUserTokenDto.Argument(new UserTokenDto(ei, Integer.valueOf(fsUserId)));
//        CreateUserTokenDto.Result ssoResult = ssoLoginService.createUserToken(userTokenArg);
//        log.info("ssoLogin token :{}", ssoResult);
//        CreateUserTokenDto.LoginStatus loginStatus = ssoResult.getLoginStatus();
//        String fsToken = ssoResult.getToken();
//        if (CreateUserTokenDto.LoginStatus.Succeed != loginStatus) {
//            log.warn("SsoBaseManager call ssoLoginService.createUserToken fail, ei[{}], employeeId[{}], result[{}].", ei, fsUserId, ssoResult);
//        } else if (org.apache.commons.lang3.StringUtils.isBlank(fsToken)) {
//            log.warn("SsoBaseManager call ssoLoginService.createUserToken fail, ei[{}], employeeId[{}], fsToken[{}].", ei, fsUserId, ssoResult);
//        }
//        String redirectUrl = String.format(ssoRedirectUrl, fsToken);
//        log.info("redirectUrl[{}]", redirectUrl);

        String ticket = getTicket(appId, corpId, outUserId, fsEa);
        String redirectUrl = h5Url + "/login?selfBuild=true&ticket=" + ticket;
        log.info("authorize,redirectUrl={}", redirectUrl);

        String sourceUrl = null;
        String finalUrl = null;
        if (!StringUtils.isEmpty(taskId) && BPM_URL_TYPE.equals(taskId)) {
            if(org.apache.commons.lang3.StringUtils.isEmpty(instanceId)) {
                log.info("CallBackController.authorize.todo not support,ei={}", ei);
                return Result.newError(ResultCode.TODO_NOT_SUPPORT);
            }
            if(ConfigCenter.BPM_TODO_EA.contains(result.getData().getEa())) {
                sourceUrl = ssoSourceDetailH5Url + "?id=" + dataId + "&apiname=" + apiName;//业务对象详情页
            } else {
                sourceUrl = ssoSourceBpmH5Url + instanceId;//bpm业务流程详情页
            }
        } else if(!StringUtils.isEmpty(taskId)
                && (String.valueOf(GenerateUrlTypeEnum.FILE_MESSAGE_URL.getType()).equals(taskId))
                || String.valueOf(GenerateUrlTypeEnum.BI_MESSAGE_URL.getType()).equals(taskId)
                || String.valueOf(GenerateUrlTypeEnum.COMMENT_REDIRECT_URL.getType()).equals(taskId)
                || String.valueOf(GenerateUrlTypeEnum.ATME_URL.getType()).equals(taskId)) {
            sourceUrl = new String(Base64.decodeBase64(url.getBytes()));
        } else {
            if(ConfigCenter.TODO_GRAY_EA.contains(result.getData().getEa()) || ConfigCenter.TODO_GRAY_EA.contains("dingtalk_true")) {
                if(!StringUtils.isEmpty(instanceId)
                        && !"null".equals(instanceId)
                        && !"100".equals(instanceId)
                        && !"null".equals(bizType)
                        && "452".equals(bizType)
                        && !"null".equals(dataId)
                        && !"null".equals(apiName)
                        && isNewPagesEi(ei)) {
                    sourceUrl = ConfigCenter.CRM_APPROVAL_INSTANCE_URL
                            .replace("{workflowInstanceId}", instanceId)
                            .replace("{objectApiName}", apiName)
                            .replace("{objectId}", dataId);
                } else {
                    if("null".equals(dataId) || "null".equals(apiName)) {
                        log.info("CallBackController.authorize.todo not support,ei={}", ei);
                        return Result.newError(ResultCode.TODO_NOT_SUPPORT);
                    }
                    sourceUrl = ssoSourceDetailH5Url + "?id=" + dataId + "&apiname=" + apiName;//业务对象详情页
                }
            } else {
                if(ConfigCenter.NEW_H5_GRAY_TENANTS.contains(result.getData().getEa())
                        && !StringUtils.isEmpty(instanceId) && !"null".equals(instanceId) && !"100".equals(instanceId)) {
                    sourceUrl = newSsoSourceDetailH5Url + "?apiName=" + ConfigCenter.APPROVAL_INSTANCE_OBJ + "&dataId=" + instanceId;//业务对象详情页
                } else {
                    if("null".equals(dataId) || "null".equals(apiName)) {
                        log.info("CallBackController.authorize.todo not support,ei={}", ei);
                        return Result.newError(ResultCode.TODO_NOT_SUPPORT);
                    }
                    sourceUrl = ssoSourceDetailH5Url + "?id=" + dataId + "&apiname=" + apiName;//业务对象详情页
                }
            }
        }
        //finalUrl = redirectUrl + "&source=" + URLEncoder.encode(sourceUrl);
        finalUrl = redirectUrl + "&redirectUrl=" + URLEncoder.encode(sourceUrl);
        log.info("authorize,finalUrl={}", finalUrl);
        ModelAndView mv = new ModelAndView("redirect:" + finalUrl);
        mv.addObject("message", "登录成功！");
        return mv;
    }
    //支持免登卡片消息
    @RequestMapping(value = "/authorizeByAuthCode", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
    @ResponseBody
    public Object authorizeByAuthCode(@RequestParam(value = "code", required = false) String code,
                                      @RequestParam(value = "state", required = false) String state,
                                      @RequestParam(value = "apiname", required = false) String apiName,
                                      @RequestParam(value = "id", required = false) String dataId,
                                      @RequestParam(value = "instanceId", required = false) String instanceId,
                                      @RequestParam(value = "taskId", required = false) String taskId,
                                      @RequestParam(value = "ei", required = false) Integer ei,
                                      @RequestParam(value = "bizType", required = false) String bizType,
                                      @RequestHeader(required = false, value = "User-Agent") String userAgent,
                                      @RequestParam(value = "url", required = false) String url) {
        log.info("authorize data code:{} apiname:{},id:{},instanceid:{}taskId:{}.ei:{},bizType:{},userAgent:{},url:{}",code,apiName,dataId,instanceId,taskId,ei,bizType,userAgent,url);
        //查询绑定企业
        Result<DingEnterpriseResult> result = enterpriseService.queryEnterpriseByEi(ei);
        if (Objects.isNull(result) || Objects.isNull(result.getData())) {
            log.warn("the fx enterprise is not binded, ei={}.", ei);
            return CALLBACK_RESPONSE_FAIL;
        }
        String appKey = result.getData().getAppKey();
        String appSecret = result.getData().getAppSecret();
        String appId = result.getData().getAgentId();
        String corpId = result.getData().getDingCorpId();
        String fsEa = result.getData().getEa();


        Result<Map<String, String>> userToken = DingRequestUtil.getUserToken(result.getData(), appKey, appSecret, code);
        if(!userToken.isSuccess()){
            return Result.newError(ResultCode.GET_DING_EMP_FAILED);
        }
        String accessToken = userToken.getData().get("accessToken");
        if (org.apache.commons.lang3.ObjectUtils.isEmpty(accessToken)) {
            String message = userToken.getData().get("message");
            log.info("access querty info:{}", result);
            return Result.newError(ResultCode.GET_DING_EMP_FAILED.getErrorCode(), message);
        }
        Result<EmployeeDingVo> userByMe = DingRequestUtil.getUserByMe(result.getData(), accessToken);
        if (!userByMe.isSuccess()) {
            return Result.newError(ResultCode.GET_DING_EMP_FAILED);
        }

        //根据员工绑定关系获取纷享员工身份
        Result<DingMappingEmployeeResult> mappingResult = objectMappingService.queryEmployeeByUnionId(ei, userByMe.getData().getUnionid());
        if (Objects.isNull(mappingResult.getData()) || StringUtils.isEmpty(mappingResult.getData().getEmployeeId())) {
            log.info("the emp not bind");
            return Result.newError(ResultCode.NOT_BIND_EMP);
        }
        Integer fsUserId = mappingResult.getData().getEmployeeId();
        String outUserId = mappingResult.getData().getDingEmployeeId();
//        CreateUserTokenDto.Argument userTokenArg = new CreateUserTokenDto.Argument(new UserTokenDto(ei, Integer.valueOf(fsUserId)));
//        CreateUserTokenDto.Result ssoResult = ssoLoginService.createUserToken(userTokenArg);
//        log.info("ssoLogin token :{}", ssoResult);
//        CreateUserTokenDto.LoginStatus loginStatus = ssoResult.getLoginStatus();
//        String fsToken = ssoResult.getToken();
//        if (CreateUserTokenDto.LoginStatus.Succeed != loginStatus) {
//            log.warn("SsoBaseManager call ssoLoginService.createUserToken fail, ei[{}], employeeId[{}], result[{}].", ei, fsUserId, ssoResult);
//        } else if (org.apache.commons.lang3.StringUtils.isBlank(fsToken)) {
//            log.warn("SsoBaseManager call ssoLoginService.createUserToken fail, ei[{}], employeeId[{}], fsToken[{}].", ei, fsUserId, ssoResult);
//        }
//        String redirectUrl = String.format(ssoRedirectUrl, fsToken);


        String ticket = getTicket(appId, corpId, outUserId, fsEa);
        String redirectUrl = h5Url + "/login?selfBuild=true&ticket=" + ticket;
        log.info("authorizeByAuthCode,redirectUrl={}", redirectUrl);

        String sourceUrl = null;
        if (!StringUtils.isEmpty(taskId) && BPM_URL_TYPE.equals(taskId)) {
            if(org.apache.commons.lang3.StringUtils.isEmpty(instanceId)) {
                log.info("CallBackController.authorize.todo not support,ei={}", ei);
                return Result.newError(ResultCode.TODO_NOT_SUPPORT);
            }
            if(ConfigCenter.BPM_TODO_EA.contains(result.getData().getEa())) {
                sourceUrl = ssoSourceDetailH5Url + "?id=" + dataId + "&apiname=" + apiName;//业务对象详情页
            } else {
                sourceUrl = ssoSourceBpmH5Url + instanceId;//bpm业务流程详情页
            }
        } else if(!StringUtils.isEmpty(taskId)
                && (String.valueOf(GenerateUrlTypeEnum.FILE_MESSAGE_URL.getType()).equals(taskId))
                || String.valueOf(GenerateUrlTypeEnum.BI_MESSAGE_URL.getType()).equals(taskId)
                || String.valueOf(GenerateUrlTypeEnum.COMMENT_REDIRECT_URL.getType()).equals(taskId)
                || String.valueOf(GenerateUrlTypeEnum.ATME_URL.getType()).equals(taskId)) {
            sourceUrl = new String(Base64.decodeBase64(url.getBytes()));
        } else {
            if(ConfigCenter.TODO_GRAY_EA.contains(result.getData().getEa()) || ConfigCenter.TODO_GRAY_EA.contains("dingtalk_true")) {
                if(!StringUtils.isEmpty(instanceId)
                        && !"null".equals(instanceId)
                        && !"100".equals(instanceId)
                        && !"null".equals(bizType)
                        && "452".equals(bizType)
                        && !"null".equals(dataId)
                        && !"null".equals(apiName)
                        && isNewPagesEi(ei)) {
                    sourceUrl = ConfigCenter.CRM_APPROVAL_INSTANCE_URL
                            .replace("{workflowInstanceId}", instanceId)
                            .replace("{objectApiName}", apiName)
                            .replace("{objectId}", dataId);
                } else {
                    if("null".equals(dataId) || "null".equals(apiName)) {
                        log.info("CallBackController.authorize.todo not support,ei={}", ei);
                        return Result.newError(ResultCode.TODO_NOT_SUPPORT);
                    }
                    sourceUrl = ssoSourceDetailH5Url + "?id=" + dataId + "&apiname=" + apiName;//业务对象详情页
                }
            } else {
                if(ConfigCenter.NEW_H5_GRAY_TENANTS.contains(result.getData().getEa())
                        && !StringUtils.isEmpty(instanceId) && !"null".equals(instanceId) && !"100".equals(instanceId)) {
                    sourceUrl = newSsoSourceDetailH5Url + "?apiName=" + ConfigCenter.APPROVAL_INSTANCE_OBJ + "&dataId=" + instanceId;//业务对象详情页
                } else {
                    if("null".equals(dataId) || "null".equals(apiName)) {
                        log.info("CallBackController.authorize.todo not support,ei={}", ei);
                        return Result.newError(ResultCode.TODO_NOT_SUPPORT);
                    }
                    sourceUrl = ssoSourceDetailH5Url + "?id=" + dataId + "&apiname=" + apiName;//业务对象详情页
                }
            }
        }
        //finalUrl = redirectUrl + "&source=" + URLEncoder.encode(sourceUrl);
        String finalUrl = redirectUrl + "&redirectUrl=" + URLEncoder.encode(sourceUrl);
        log.info("authorizeByAuthCode,finalUrl={}", finalUrl);

        ModelAndView mv = new ModelAndView("redirect:" + finalUrl);
        mv.addObject("message", "登录成功！");
        return mv;
    }



    //点击H5应用图标，会跳转的接口。通过客户client的jsp地址，在jsp中会再次调用这个接口
    @RequestMapping(value = "/authorizeByApp", method = RequestMethod.GET, produces = "application/json;charset=UTF-8")
    @CrossOrigin(origins = "*", maxAge = 3600)
    @ResponseBody
    public Object authorizeByApp(@RequestParam(value = "code", required = false) String code,
                                 @RequestParam(value = "corpId", required = false) String corpId,
                                 @RequestParam(value = "appKey",required = false) String appKey,
                                 @RequestHeader(required = false, value = "User-Agent") String userAgent,
                                 HttpServletResponse httpServletResponse) throws IOException {
        log.info("authorizeByApp,corpId={},code={}",corpId,code);
        //处理XSS攻击
        corpId = Encode.forHtmlContent(corpId);
        code = Encode.forHtmlContent(code);
        appKey = Encode.forHtmlContent(appKey);
        StopWatch stopWatch = StopWatch.create("dingtalk authorize by app" + corpId);
        Result<DingEnterpriseResult> enterprise=Result.newSuccess();
        log.info("appkey:{},corpId:{},code:{}",appKey,corpId,code);
        if(StringUtils.isEmpty(appKey)||"null".equals(appKey)){
            enterprise= enterpriseService.queryEnterpriseByCorpId(corpId);
        }else {
            enterprise = enterpriseService.queryEnterpriseByAppKey(appKey);
        }
        if (Objects.isNull(enterprise) || Objects.isNull(enterprise.getData())) {
            log.warn("the fx enterprise is not bind, corpId={}.appKey:{}", corpId,appKey);
            return CALLBACK_RESPONSE_FAIL;
        }
        stopWatch.lap("ding getEnterprise");

        //获取token
//        Boolean isQPSLimit = qpsLimitHandler.isQPSLimitByEa(enterprise.getData().getEa());
//        if(isQPSLimit) {
//            //限流且重试多次失败
//            log.info("CallBackController.authorizeByApp,get token failed.qpsLimit.ei={},code={}", enterprise.getData().getEi(), code);
//            return CALLBACK_RESPONSE_FAIL;
//        }
        String accessToken = cache.get(String.valueOf(enterprise.getData().getEi()));
        if (org.apache.commons.lang3.StringUtils.isEmpty(accessToken)) {
            log.warn("startConnect appKey或appSecret错误.");
            return Result.newError(ResultCode.DING_CONNECT_PARAM_ERROR);
        }
        stopWatch.lap("ding getToken");

        Gson gson = new Gson();
        String getUserUrl = enterprise.getData().getClientIp() + "proxy/proxyRequest";
        log.info("getUserUrl={}, userAgent={}", getUserUrl, userAgent);
        Map<String, Object> argMap = new HashMap<>();
        argMap.put("type", "GET");
        String getTokenUrl = DingUrl.GET_USER_BY_APP.concat("?access_token=").concat(accessToken).concat("&code=").concat(code);
        argMap.put("url", getTokenUrl);
        argMap.put("token", enterprise.getData().getToken());
        argMap.put("code", code);
        Boolean isQPSLimit1 = qpsLimitHandler.isQPSLimitByEa(enterprise.getData().getEa());
        if(isQPSLimit1) {
            //限流且重试多次失败
            log.info("CallBackController.authorizeByApp,query user detail failed.qpsLimit1.ei={},code={}", enterprise.getData().getEi(), code);
            return CALLBACK_RESPONSE_FAIL;
        }
        CloseableHttpResponse response = HttpUtils.httpPost(getUserUrl, gson.toJson(argMap), null);
        String entity = null;
        try {
            entity = EntityUtils.toString(response.getEntity(), "UTF-8");
        } catch (IOException e) {
            e.printStackTrace();
        }
        stopWatch.lap("ding getUser");
        Integer errcode = ObjectUtils.isEmpty(JSONPath.read(entity, "$.errcode")) ? null : Integer.parseInt(JSONPath.read(entity, "$.errcode").toString());
        if (errcode.equals(DING_INVALID_ACCESSTOKEN)) {
            //重新获取access_token，刷新缓存
            Result<DingEnterpriseResult> dingEnterpriseResultResult = enterpriseService.queryEnterpriseByEi(enterprise.getData().getEi());
            DingEnterpriseResult enterpriseResult = dingEnterpriseResultResult.getData();
            String refreshToken = DingRequestUtil.getToken(enterpriseResult.getClientIp(), enterpriseResult.getAppKey(), enterpriseResult.getAppSecret());
            cache.put(String.valueOf(enterprise.getData().getEi()), refreshToken);
            String refreshTokenUrl = DingUrl.GET_USER_BY_APP.concat("?access_token=").concat(refreshToken).concat("&code=").concat(code);
            argMap.put("url", refreshTokenUrl);
            response = HttpUtils.httpPost(getUserUrl, gson.toJson(argMap), null);
            try {
                entity = EntityUtils.toString(response.getEntity(), "UTF-8");
            } catch (IOException e) {
                e.printStackTrace();
            }
            log.info("trace refresh token url ei:{},accessToken:{},entity:{},", enterprise.getData().getEi(), refreshToken, entity);
        }

        DingUserInfo dingUser = gson.fromJson(entity, DingUserInfo.class);
        String userId = dingUser.getUserid();
        log.info("trace authorizeByApp by app ei={},corpid={},userId={}", enterprise.getData().getEi(),corpId, userId);
        //根据员工绑定关系获取纷享员工身份
        Result<DingMappingEmployeeResult> mappingResult = objectMappingService.queryEmpByDingUserId(enterprise.getData().getEi(), userId);
        if (Objects.isNull(mappingResult) || Objects.isNull(mappingResult.getData()) || StringUtils.isEmpty(mappingResult.getData().getEmployeeId())) {
            log.info("the emp not bind");
            return Result.newError(ResultCode.NOT_BIND_EMP);
        }
        Integer fsUserId = mappingResult.getData().getEmployeeId();
        String outUserId = mappingResult.getData().getDingEmployeeId();
        stopWatch.log();
        stopWatch.lap("ding query dataBase user");

//        CreateUserTokenDto.Argument userTokenArg = new CreateUserTokenDto.Argument(new UserTokenDto(enterprise.getData().getEi(), Integer.valueOf(fsUserId)));
//        CreateUserTokenDto.Result ssoResult = ssoLoginService.createUserToken(userTokenArg);
//        stopWatch.lap("ding create sso token");
//
//        CreateUserTokenDto.LoginStatus loginStatus = ssoResult.getLoginStatus();
//        String fsToken = ssoResult.getToken();
//        if (CreateUserTokenDto.LoginStatus.Succeed != loginStatus) {
//            log.warn("authorizeByApp SsoBaseManager call ssoLoginService.createUserToken fail, ei[{}], employeeId[{}], result[{}].", corpId, fsUserId, ssoResult);
//        } else if (org.apache.commons.lang3.StringUtils.isBlank(fsToken)) {
//            log.warn("authorizeByApp SsoBaseManager call ssoLoginService.createUserToken fail, ei[{}], employeeId[{}], fsToken[{}].", corpId, fsUserId, ssoResult);
//        }
        //String redirectUrl = String.format(ssoRedirectUrl, fsToken);


        String appId = enterprise.getData().getAgentId();
        String fsEa = enterprise.getData().getEa();
        String ticket = getTicket(appId, corpId, outUserId, fsEa);
        String redirectUrl = h5Url + "/login?selfBuild=true&ticket=" + ticket;
        log.info("authorizeByApp,redirectUrl={}", redirectUrl);
        if (!userAgent.contains("AliApp")) {
//            redirectUrl = redirectUrl.concat("&source=").concat(h5Url);
//            log.info("aliapp:{},redirectUrl:{}", userAgent, redirectUrl);
            redirectUrl = redirectUrl + "&redirectUrl=/XV/Home/Index";
        }

        log.info("authorizeByApp,redirectUrl.2={}", redirectUrl);
        Map<String, String> map = new HashMap<>();
        map.put("redirectUrl", redirectUrl);
        httpServletResponse.setHeader("Access-Control-Allow-Origin", "*");
        httpServletResponse.setHeader("Access-Control-Allow-Methods", "GET,POST");
        httpServletResponse.setHeader("Content-Type", "application/javascript");
        stopWatch.log();
        return JSONObject.toJSONString(map);

    }

    private String getTicket(String appId, String corpId, String outUserId, String fsEa) {
        GenFsTicketModel ticketModel = new GenFsTicketModel();
        ticketModel.setAppId(appId);
        ticketModel.setOutEa(corpId);
        ticketModel.setOutUserId(outUserId);
        ticketModel.setFsEa(fsEa);

        MethodContext context = MethodContext.newInstance(ticketModel);
        dingtalkLoginTemplate.genFsTicket(context);

        com.facishare.open.feishu.syncapi.result.Result<String> result = context.getResultData();
        String ticket = result.getData();
        log.info("getTicket,ticket={},ticketModel={}",ticket,ticketModel);
        return ticket;
    }


    public static String sign(String ticket, String nonceStr, long timeStamp, String url) {
        try {
            return DingTalkJsApiSingnature.getJsApiSingnature(url, nonceStr, timeStamp, ticket);
        } catch (Exception ex) {
            log.info("sign failed");
            return null;
        }
    }

    /**
     * 指定部门创建
     */
    @RequestMapping(value = "/createDept", method = RequestMethod.POST)
    public Result<Void> createDept(@RequestParam("corpId") Integer ei, @RequestBody List<Long> ids) {
        UserVo userVo = getUserVo();
        log.info("createDept start ....");
        objectMappingService.createFxDept(ei, ids);

        return new Result<>();
    }

    public Boolean isNewPagesEi(Integer ei) {
        if(ConfigCenter.pagesEiSet.contains(String.valueOf(ei))) {
            return Boolean.TRUE;
        }

        Integer lowerKey = ConfigCenter.rangeMap.floorKey(ei); // 最接近的开始边界（包含ei的）
        if(lowerKey == null) {
            return Boolean.FALSE; // 没有找到小于等于ei的开始边界
        }

        Integer upperBoundary = ConfigCenter.rangeMap.get(lowerKey); // 对应的结束边界
        return ei >= lowerKey && ei <= upperBoundary; // ei必须在开始和结束边界之间
    }
}
