package com.facishare.open.ding.provider.service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.service.ExternalOaTodoService;
import com.facishare.open.ding.api.vo.DingTaskVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.config.ConfigCenter;
import com.facishare.open.ding.provider.dao.DingTaskDao;
import com.facishare.open.ding.provider.dingding.DingRequestUtil;
import com.facishare.open.ding.provider.dingding.DingUrl;
import com.facishare.open.ding.provider.entity.DingMessageArg;
import com.facishare.open.ding.provider.entity.DingUpdateMessageArg;
import com.facishare.open.ding.provider.enums.DingTodoTypeEnum;
import com.facishare.open.ding.provider.handler.QPSLimitHandlerFromProvider;
import com.facishare.open.ding.provider.manager.DingEnterpriseManager;
import com.facishare.open.ding.provider.manager.DingMappingEmployeeManager;
import com.facishare.open.ding.provider.utils.HttpRequestUtils;
import com.facishare.restful.common.StopWatch;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DealTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DeleteTodoArg;
import com.fxiaoke.message.extrnal.platform.model.result.CreateTodoResult;
import com.fxiaoke.message.extrnal.platform.model.result.DealTodoResult;
import com.fxiaoke.message.extrnal.platform.model.result.DeleteTodoResult;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <p>接入webhook消息推送</p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019-10-15 20:06
 */
@Slf4j
@Service("externalOaTodoServiceImpl")
public class ExternalOaTodoServiceImpl implements ExternalOaTodoService {
    @Autowired
    private DingEnterpriseManager dingEnterpriseManager;

    @Autowired
    private DingMappingEmployeeManager employeeManager;

    @ReloadableProperty("mid.authorize.url")
    private String MID_URL;// = "https://www.fxiaoke.com/dingtalk/business/authorize?direct_uri=";

    //客户端
    private static final String DING_SINGLE_URL = "https://oapi.dingtalk.com/connect/oauth2/sns_authorize?response_type=code&scope=snsapi_auth&state=STATE";

    //网页版
    private static final String DING_SINGLE_URL_WEB = "https://oapi.dingtalk.com/connect/oauth2/sns_authorize?response_type=code&scope=snsapi_login&state=STATE";

    private static final String MESSAGE_TYPE = "action_card";
    private static final String MESSAGE_TYPE_OA = "oa";

    private static final String TOKEN_INVALID_CODE = "88";

    @ReloadableProperty("sso.redirect.url.source")
    private String ssoRedirectUrlSource;

    @Autowired
    private QPSLimitHandlerFromProvider qpsLimitHandlerFromProvider;

    @Resource
    private EIEAConverter eieaConverter;
    @Autowired
    private DingTaskDao dingTaskDao;

    private LoadingCache<String, String> cache = Caffeine.newBuilder().maximumSize(10000).expireAfterWrite(100, TimeUnit.MINUTES).refreshAfterWrite(90, TimeUnit.MINUTES).build(new CacheLoader<String, String>() {
        @Nullable
        @Override
        public String load(@NonNull String key) throws Exception {
            Result<DingEnterpriseResult> dingEnterpriseResultResult = dingEnterpriseManager.queryEnterpriseByEi(Integer.valueOf(key));
            DingEnterpriseResult result = dingEnterpriseResultResult.getData();
            return DingRequestUtil.getToken(result.getClientIp(), result.getAppKey(), result.getAppSecret());
        }
    });

    @Override
    public CreateTodoResult createTodo(CreateTodoArg arg, DingEnterpriseResult enterpriseResult) {
        //灰度企业接入mq，这里直接返回
        CreateTodoResult result = new CreateTodoResult();
        result.setCode(200);
//        if(ConfigCenter.OA_GRAY_TENANTS.contains(arg.getEa())){
//            return result;
//        }
        StopWatch stopWatch = StopWatch.create("trace createTodo:" + arg.getEa());
        log.info("ExternalOaToDO messageArg:{}", arg);
        if (Objects.isNull(arg)) {
            log.warn("sendMessage param is null");
            result.setMessage("sendMessage param is null");
            return result;
        }
        Integer ei = arg.getEi();
        stopWatch.lap("getEnterprise");
        List<String> needSendUserIds = convertDingUserIds(arg.getReceiverIds(), ei);
        stopWatch.lap("getUser");
        for (String needSendUserId : needSendUserIds) {
            if (StringUtils.isNotEmpty(needSendUserId)) {
                Gson gson = new Gson();
                String clientUrl = DingRequestUtil.appendUrl(enterpriseResult.getClientIp());
                Map<String, Object> messageArg = new HashMap<>();
                String accessToken = cache.get(String.valueOf(arg.getEi()));
                if (StringUtils.isEmpty(accessToken)) {
                    log.warn("createTodo not accessToken  ea:{}", arg.getEa());
                    return result;
                }
                stopWatch.lap("getToken");
                String proxyMessageUrl = DingUrl.CORP_MESSAGE.concat("?access_token=").concat(accessToken);
                messageArg.put("url", proxyMessageUrl);//钉钉发送消息的url
                messageArg.put("type", "POST");
                messageArg.put("token", enterpriseResult.getToken());

                DingMessageArg dingMessageArg = null;
                if(ConfigCenter.SUPPORT_UPDATE_TODO_MESSAGE_STATUS_TENANTS.contains(String.valueOf(arg.getEi()))){
                    dingMessageArg = getOADingMessageArg(arg, enterpriseResult, needSendUserId);
                }else {
                    dingMessageArg=getDingMessageArg(arg, enterpriseResult, needSendUserId);
                }
                messageArg.put("data", dingMessageArg);
                Boolean isQPSLimit = qpsLimitHandlerFromProvider.isQPSLimitByEa(arg.getEa());
                if(isQPSLimit) {
                    //限流且重试多次失败
                    log.info("向钉钉发送待办消息失败，限流且重试多次失败.messageArg={}", messageArg);
                    return result;
                }
                Object messageResult = DingRequestUtil.proxyRequest(clientUrl, gson.toJson(messageArg));
                JSONObject jsonObject = JSONObject.parseObject(messageResult.toString());
                stopWatch.lap("sendMessage");
                if (Objects.isNull(messageResult) || ObjectUtils.isEmpty(jsonObject.get("errcode"))) {
                    log.warn("向钉钉发送待办消息失败，messageArg={}.", messageArg);
                    return result;
                }
                if (!HttpRequestUtils.DING_SUCCESS.equals(jsonObject.get("errcode"))) {
                    if (jsonObject.get("errcode").equals(TOKEN_INVALID_CODE)) {
                        messageResult = refreshRequest(messageArg, ei, clientUrl);
                        log.info("向钉钉发送待办消息重试，messageArg={},messageResult={}.", messageArg, messageResult);
                        jsonObject = JSONObject.parseObject(messageResult.toString());
                    }
                    log.info("向钉钉发送待办消息重试，messageArg={},messageResult={}.", messageArg, messageResult);
                }
                if(jsonObject.get("errcode").equals(HttpRequestUtils.DING_SUCCESS)){
                    String taskId=jsonObject.get("task_id").toString();
                    DingTaskVo dingTaskVo=new DingTaskVo();
                    dingTaskVo.setEi(ei);
                    dingTaskVo.setSourceId(arg.getSourceId());
                    dingTaskVo.setDingEmployeeId(needSendUserId);
                    dingTaskVo.setMessageType(DingTodoTypeEnum.DING_WORK.name());
                    dingTaskVo.setTaskId(taskId);
                    dingTaskDao.insertSource(dingTaskVo);

                }
            }
        }
        stopWatch.log();
        result.setMessage("发送成功");
        return result;
    }
    //之前是发送卡片消息
    @NotNull
    private DingMessageArg getDingMessageArg(CreateTodoArg arg, DingEnterpriseResult enterpriseResult, String needSendUserId) {
        StringBuilder title = new StringBuilder();
        if(StringUtils.isNotEmpty(arg.getTitle())) {
            title.append(arg.getTitle()).append("：");
        }
        DingMessageArg dingMessageArg = new DingMessageArg();
        dingMessageArg.setAgent_id(enterpriseResult.getAgentId());
        dingMessageArg.setUserid_list(needSendUserId);
        dingMessageArg.getMsg().setMsgtype(MESSAGE_TYPE);
        //优化卡片消息
        StringBuilder markdown = new StringBuilder();
        if (CollectionUtils.isNotEmpty(arg.getForm())) {
            for (int i = 0; i < arg.getForm().size(); i++) {
                if (i == 0) {
                    markdown.append("#### ").append(arg.getForm().get(i).getKey()).append(": ").append(arg.getForm().get(i).getValue()).append("\n");
                    title.append(arg.getForm().get(i).getValue());
                } else {
                    markdown.append("#### ").append(arg.getForm().get(i).getKey()).append(": ").append(arg.getForm().get(i).getValue()).append("\n");
                }

            }
        }
        dingMessageArg.getMsg().getAction_card().setMarkdown(!StringUtils.isEmpty(markdown.toString()) ? markdown.toString() : arg.getContent());
        //判断标题的长度
        if(title.toString().length() > 30) {
            title.replace(30, title.toString().length(), "...");
        }
        String time = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        title.append(" ").append(time);
        dingMessageArg.getMsg().getAction_card().setTitle(title.toString());
        dingMessageArg.getMsg().getAction_card().setSingle_title("查看详情");

        String objectApiName = arg.getExtraDataMap().get("objectApiName");
        String objectId = arg.getExtraDataMap().get("objectId");
        String instanceId = arg.getExtraDataMap().get("workflowInstanceId");
        String loginMessageUrl = getRedirectUrl(arg, enterpriseResult, objectApiName, objectId, instanceId);
        dingMessageArg.getMsg().getAction_card().setSingle_url(loginMessageUrl);//需要跳转到纷享的url
        log.info("oatoDo message url:{}",loginMessageUrl);
        return dingMessageArg;
    }
    //现在支持OA消息
    private DingMessageArg getOADingMessageArg(CreateTodoArg arg, DingEnterpriseResult enterpriseResult, String needSendUserId) {
        StringBuilder title = new StringBuilder();
        if(StringUtils.isNotEmpty(arg.getTitle())) {
            title.append(arg.getTitle()).append("：");
        }
        DingMessageArg dingMessageArg = new DingMessageArg();
        dingMessageArg.setAgent_id(enterpriseResult.getAgentId());
        dingMessageArg.setUserid_list(needSendUserId);
        dingMessageArg.getMsg().setMsgtype(MESSAGE_TYPE_OA);
        DingMessageArg.OA messageOA=new DingMessageArg.OA();
        DingMessageArg.Head messageHead=new DingMessageArg.Head();
        messageOA.setHead(messageHead);
        messageHead.setText("CRM待办");
        DingMessageArg.StatusBarArg statusBarArg=new DingMessageArg.StatusBarArg();
        statusBarArg.setStatus_value("待审批");
        statusBarArg.setStatus_bg("0xFFFF9D46");
        DingMessageArg.Body messageBody=new DingMessageArg.Body();
        List<DingMessageArg.Form> forms= Lists.newArrayList();
        messageBody.setForm(forms);
        //避免删除人员，重新新增人员的时候，钉钉幂等导致数据没有发送，消息没有收到
        String time = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");
        String contentSuffix=new StringBuilder().append(arg.getTitle()).append(" ").append(time).toString();
        messageBody.setTitle(contentSuffix);
        messageBody.setContent(arg.getContent());
        if (CollectionUtils.isNotEmpty(arg.getForm())) {
            for (int i = 0; i < arg.getForm().size(); i++) {
                String formateKey=new StringBuilder().append(arg.getForm().get(i).getKey()).append(": ").toString();
                forms.add(new DingMessageArg.Form(formateKey,arg.getForm().get(i).getValue()));
            }
        }
        String objectApiName = arg.getExtraDataMap().get("objectApiName");
        String objectId = arg.getExtraDataMap().get("objectId");
        String instanceId = arg.getExtraDataMap().get("workflowInstanceId");
        String loginMessageUrl = getRedirectUrl(arg, enterpriseResult, objectApiName, objectId, instanceId);
//        dingMessageArg.getMsg().getAction_card().setSingle_url(loginMessageUrl);//需要跳转到纷享的url
        messageOA.setMessage_url(loginMessageUrl);
        messageOA.setPc_message_url(loginMessageUrl);
        messageOA.setBody(messageBody);
        messageOA.setStatus_bar(statusBarArg);
        dingMessageArg.getMsg().setOa(messageOA);
        log.info("getOADingMessageArg message messageOA:{}",JSONObject.toJSONString(messageOA));
        return dingMessageArg;
    }

    /**
     * 以前旧版登录
     * @param arg
     * @param enterpriseResult
     * @param objectApiName
     * @param objectId
     * @param instanceId
     * @return
     */
    @NotNull
    private String getRedirectUrl(CreateTodoArg arg, DingEnterpriseResult enterpriseResult, String objectApiName, String objectId, String instanceId) {
        //TODO 新版登录https://open.dingtalk.com/document/orgapp/tutorial-obtaining-user-personal-information
        /**
         * https://login.dingtalk.com/oauth2/auth?
         * redirect_uri=https%3A%2F%2Fwww.aaaaa.com%2Fauth
         * &response_type=code
         * &client_id=dingxxxxxxx   //应用的AppKey
         * &scope=openid   //此处的openId保持不变
         * &state=dddd
         * &prompt=consent
         */
        String directUri = MID_URL + "?ei=" + arg.getEi() + "&apiname=" + objectApiName
                + "&id=" + objectId + "&instanceId=" + instanceId + "&taskId=" + arg.getGenerateUrlType() + "&bizType=" + arg.getBizType();
        if(ConfigCenter.MESSAGE_LOGIN_URL_TENANTS.contains(String.valueOf(arg.getEi()))){
            directUri= ConfigCenter.AUTH_TOKEN_LOGIN_URL+ "?ei=" + arg.getEi() + "&apiname=" + objectApiName
                    + "&id=" + objectId + "&instanceId=" + instanceId + "&taskId=" + arg.getGenerateUrlType() + "&bizType=" + arg.getBizType();;
            String loginDataUrl=String.format(ConfigCenter.DINGTALK_SKIP_LOGIN_URL,URLEncoder.encode(directUri),enterpriseResult.getAppKey(),enterpriseResult.getAppKey());
            return loginDataUrl;
        }
        StringBuilder stringBuilder = new StringBuilder();
        String directAppId = enterpriseResult.getRedirectAppId();
        stringBuilder.append(DING_SINGLE_URL).append("&appid=").append(directAppId).append("&redirect_uri=").append(URLEncoder.encode(directUri));
        return stringBuilder.toString();
    }

    private Object refreshRequest(Map<String, Object> messageArg,Integer ei, String clientUrl) {
        //重新获取token，发送消息
        Gson gson = new Gson();
        cache.invalidate(String.valueOf(ei));
        String refreshToken = cache.get(String.valueOf(ei));
        String refreshUrl = DingUrl.CORP_MESSAGE.concat("?access_token=").concat(refreshToken);
        messageArg.put("url", refreshUrl);//钉钉发送消息的url
        Boolean isQPSLimit = qpsLimitHandlerFromProvider.isQPSLimitByEa(eieaConverter.enterpriseIdToAccount(ei));
        if(isQPSLimit) {
            //限流且重试多次失败
            log.info("向钉钉发送待办消息失败，限流且重试多次失败.messageArg={}", messageArg);
            return null;
        }
        Object refreshResult = DingRequestUtil.proxyRequest(clientUrl, gson.toJson(messageArg));
        return refreshResult;
    }

    private List<String> convertDingUserIds(List<Integer> empUsers,int ei){

        List<String> needSendUserIds= Lists.newArrayList();
        if (CollectionUtils.isEmpty(empUsers)) {
           return needSendUserIds;
        }
        for (Integer fxId : empUsers) {
            DingMappingEmployeeResult mappingEmployeeResult = employeeManager.queryMappingEmployeeByEi(ei, fxId);
            if (Objects.isNull(mappingEmployeeResult)) {
                log.info("emp not bind,ei={},fxId={}", ei, fxId);
                continue;
            }
            needSendUserIds.add(mappingEmployeeResult.getDingEmployeeId());
        }
        return needSendUserIds;
    }



    @Override
    public DealTodoResult dealTodo(DealTodoArg dealTodoArg,DingEnterpriseResult mappingEnterprise) {
        DealTodoResult result = new DealTodoResult();
        Integer tenantId=dealTodoArg.getEi();
        List<String> needSendUserIds = convertDingUserIds(dealTodoArg.getOperators(), tenantId);
        for (String needSendUserId : needSendUserIds) {
            //需要查询数据有没有在task存储
            List<String> taskIdByDingEmpIds = dingTaskDao.getTaskIdByDingEmpId(tenantId, dealTodoArg.getSourceId(), needSendUserId);
            if(ObjectUtils.isEmpty(taskIdByDingEmpIds)){
                continue;
            }
            //更新状态
            Gson gson = new Gson();
            String clientUrl = DingRequestUtil.appendUrl(mappingEnterprise.getClientIp());
            Map<String, Object> messageArg = new HashMap<>();
            String accessToken = cache.get(String.valueOf(tenantId));
            if (StringUtils.isEmpty(accessToken)) {
                log.warn("createTodo not accessToken  ea:{}", dealTodoArg.getEa());
                return result;
            }
            for (String taskIdByDingEmpId : taskIdByDingEmpIds) {
                String proxyMessageUrl = DingUrl.CORP_MESSAGE_UPDATE.concat("?access_token=").concat(accessToken);
                messageArg.put("url", proxyMessageUrl);//钉钉发送消息的url
                messageArg.put("type", "POST");
                messageArg.put("token", mappingEnterprise.getToken());
                DingUpdateMessageArg dingUpdateMessageArg=new DingUpdateMessageArg();
                dingUpdateMessageArg.setTask_id(taskIdByDingEmpId);
                dingUpdateMessageArg.setAgent_id(mappingEnterprise.getAgentId());
                dingUpdateMessageArg.setStatus_value("已处理");
                dingUpdateMessageArg.setStatus_bg("0xFF78C06E");
                messageArg.put("data",dingUpdateMessageArg);
                Object messageResult = DingRequestUtil.proxyRequest(clientUrl, gson.toJson(messageArg));
                JSONObject jsonObject = JSONObject.parseObject(messageResult.toString());
                if (Objects.isNull(messageResult) || ObjectUtils.isEmpty(jsonObject.get("errcode"))) {
                    log.warn("向钉钉处理待办消息失败，messageArg={}.", messageArg);
                    return result;
                }
                if (HttpRequestUtils.DING_SUCCESS.equals(jsonObject.get("errcode"))) {
                    Integer integer = dingTaskDao.updateByTaskId(0, tenantId, taskIdByDingEmpId, needSendUserId);
                    log.info("update task status taskId:{}",taskIdByDingEmpId);
                }
            }

        }
        result.setCode(200);
        return result;
    }

    @Override
    public DeleteTodoResult deleteTodo(DeleteTodoArg deleteTodoArg,DingEnterpriseResult mappingEnterprise) {
        DeleteTodoResult result = new DeleteTodoResult();

        Integer tenantId=deleteTodoArg.getEi();
        List<String> needSendUserIds = convertDingUserIds(deleteTodoArg.getDeleteEmployeeIds(), tenantId);
        for (String needSendUserId : needSendUserIds) {
            //需要查询数据有没有在task存储
            List<String> taskIdByDingEmpIds = dingTaskDao.getTaskIdByDingEmpId(tenantId, deleteTodoArg.getSourceId(), needSendUserId);
            if(ObjectUtils.isEmpty(taskIdByDingEmpIds)){
                continue;
            }
            //更新状态
            Gson gson = new Gson();
            String clientUrl = DingRequestUtil.appendUrl(mappingEnterprise.getClientIp());
            Map<String, Object> messageArg = new HashMap<>();
            String accessToken = cache.get(String.valueOf(tenantId));
            if (StringUtils.isEmpty(accessToken)) {
                log.warn("createTodo not accessToken  ea:{}", deleteTodoArg.getEa());
                return result;
            }
            for (String taskIdByDingEmpId : taskIdByDingEmpIds) {
                String proxyMessageUrl = DingUrl.CORP_MESSAGE_UPDATE.concat("?access_token=").concat(accessToken);
                messageArg.put("url", proxyMessageUrl);//钉钉发送消息的url
                messageArg.put("type", "POST");
                messageArg.put("token", mappingEnterprise.getToken());
                DingUpdateMessageArg dingUpdateMessageArg=new DingUpdateMessageArg();
                dingUpdateMessageArg.setTask_id(taskIdByDingEmpId);
                dingUpdateMessageArg.setAgent_id(mappingEnterprise.getAgentId());
                dingUpdateMessageArg.setStatus_value("已删除");
                messageArg.put("data",dingUpdateMessageArg);
                Object messageResult = DingRequestUtil.proxyRequest(clientUrl, gson.toJson(messageArg));
                JSONObject jsonObject = JSONObject.parseObject(messageResult.toString());
                if (Objects.isNull(messageResult) || ObjectUtils.isEmpty(jsonObject.get("errcode"))) {
                    log.warn("向钉钉删除待办消息失败，messageArg={}.", messageArg);
                    return result;
                }
                if (HttpRequestUtils.DING_SUCCESS.equals(jsonObject.get("errcode"))) {
                    Integer integer = dingTaskDao.updateByTaskId(0, tenantId, taskIdByDingEmpId, needSendUserId);
                    log.info("delete task status taskId:{}",taskIdByDingEmpId);
                }
            }

        }
        result.setCode(200);
        return result;
    }
}
