package com.facishare.open.ding.web.controller;

import com.facishare.open.ding.api.model.FsUserInfoModel;
import com.facishare.open.ding.api.result.ConditionEmployeeResult;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.service.AuthService;
import com.facishare.open.ding.api.service.DingtalkUserService;
import com.facishare.open.ding.api.service.EnterpriseService;
import com.facishare.open.ding.api.service.ObjectMappingService;
import com.facishare.open.ding.api.vo.*;
import com.facishare.open.ding.common.model.Dept;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.web.arg.GetFsUserInfoArg;
import com.facishare.open.ding.web.base.BaseController;
import com.facishare.open.ding.web.base.UserVo;
import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@RestController
@RequestMapping("/objMapping")
public class ObjectMappingController extends BaseController {
    @Autowired
    private AuthService authService;

    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private EnterpriseService enterpriseService;

    @Autowired
    private EnterpriseEditionService enterpriseEditionService;

    @Autowired
    private DingtalkUserService dingtalkUserService;

    ExecutorService executorService = Executors.newFixedThreadPool(20);


    @RequestMapping(value = "/queryMappingEmployee", method = RequestMethod.POST)
    public Result<Map<String, Object>> queryEmployeeBind(@RequestBody BindEmpVo vo) {
        UserVo userVo = getUserVo();
        if (userVo == null) {
            log.warn("纷享用户为空，queryKcEnterprise userVo=[{}].", userVo);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        log.info("user = " + userVo);
        String fsUserAccount = "E." + userVo.getEnterpriseAccount() + "." + userVo.getEmployeeId();
        Result<Map<String, Object>> dataResult = objectMappingService.queryMappingEmployee(vo, userVo.getEnterpriseId());
        return dataResult;
    }

    /**
     * 模式2:查询通讯录
     */
    @RequestMapping(value = "/model2SyncEmployee", method = RequestMethod.POST)
    public Result<Map<String, Object>> model2SyncEmployee(@RequestBody BindEmpVo vo) {
        UserVo userVo = getUserVo();
        if (userVo == null) {
            log.warn("纷享用户为空，queryKcEnterprise userVo=[{}].", userVo);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        log.info("user = " + userVo);
        String fsUserAccount = "E." + userVo.getEnterpriseAccount() + "." + userVo.getEmployeeId();
        Result<Map<String, Object>> dataResult = objectMappingService.queryMappingEmployee(vo, userVo.getEnterpriseId());
        return dataResult;
    }


    /**
     * 查询新的需要同步的职员
     *
     * @return
     */
    @RequestMapping(value = "/queryNewEmployee", method = RequestMethod.POST)
    public Result<Map<String, Object>> queryNewEmployee(@RequestBody BindEmpVo vo) {
        log.info("queryNewEmployee,vo={}",vo);
        UserVo userVo = getUserVo();
        if (userVo == null) {
            log.warn("纷享用户为空，queryNewEmployee userVo=[{}].", userVo);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        String fsUserAccount = "E." + userVo.getEnterpriseAccount() + "." + userVo.getEmployeeId();

        Result<Map<String, Object>> result = objectMappingService.queryNewEmployee(vo, userVo.getEnterpriseId(), userVo.getEnterpriseAccount(), userVo.getEmployeeId());
        return result;
    }
    //syncPullOrganizationData

    /**
     * 查询新的需要同步的职员
     *
     * @return
     */
    @RequestMapping(value = "/syncPullOrganizationData", method = RequestMethod.POST)
    public Result<Map<String, Object>> syncPullOrganizationData(@RequestBody BindEmpVo vo) {
        UserVo userVo = getUserVo();
        if (userVo == null) {
            log.warn("纷享用户为空，queryNewEmployee userVo=[{}].", userVo);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        String fsUserAccount = "E." + userVo.getEnterpriseAccount() + "." + userVo.getEmployeeId();
        String traceId = TraceUtils.getTraceId();
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                TraceUtils.initTraceId(traceId);
                objectMappingService.syncPullOrganizationData(userVo.getEnterpriseId(), userVo.getEnterpriseAccount(), userVo.getEmployeeId());
            }
        });

        return Result.newError(ResultCode.ALL_PULL_ORGANIZATION_ING);
    }


    /**
     * 是否使用回调地址
     *
     * @return
     */
    @RequestMapping(value = "/queryCallBack", method = RequestMethod.POST)
    public Result<Boolean> queryCallBack() {
        UserVo userVo = getUserVo();
        if (userVo == null) {
            log.warn("纷享用户为空，queryNewEmployee userVo=[{}].", userVo);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        Result<DingEnterpriseResult> dingEnterpriseResultResult = enterpriseService.queryEnterpriseByEi(userVo.getEnterpriseId());
        return Result.newSuccess(dingEnterpriseResultResult.getData().getIsCallback() == 0 ? true : false);
    }

    /**
     * 立即同步
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/syncEmployee", method = RequestMethod.POST)
    public Result<Boolean> syncEmployee() {
        UserVo userVo = getUserVo();
        Result<Boolean> mapResult = objectMappingService.syncNewEmployee(userVo.getEnterpriseId(), userVo.getEmployeeId());
        return mapResult;
    }


    @RequestMapping(value = "/bindEmployee", method = RequestMethod.POST)
    public Result<Map<String, Integer>> bindEmployee(@RequestBody BindEmpVo vo) {
        UserVo userVo = getUserVo();
        if (userVo == null) {
            log.warn("纷享用户为空，queryKcEnterprise userVo=[{}].", userVo);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        String fsUserAccount = "E." + userVo.getEnterpriseAccount() + "." + userVo.getEmployeeId();

        List<DingMappingEmployeeResult> list = vo.getEmpList();
        Result<Map<String, Integer>> result = objectMappingService.bindEmployee(list, userVo.getEnterpriseId(), userVo.getEmployeeId(), false);
        return result;
    }

    /**
     * 解绑
     *
     * @param dingMappingEmployeeResult
     * @return
     */
    @RequestMapping(value = "/deleteBind", method = RequestMethod.POST)
    public Result<Integer> deleteBind(@RequestBody DingMappingEmployeeResult dingMappingEmployeeResult) {
        UserVo userVo = getUserVo();
        if (userVo == null) {
            log.warn("纷享用户为空，queryKcEnterprise userVo=[{}].", userVo);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        if (dingMappingEmployeeResult == null) {
            log.warn("参数异常，dingMappingEmployeeResult=[{}].", dingMappingEmployeeResult);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        return objectMappingService.deleteBind(userVo.getEnterpriseId(), dingMappingEmployeeResult.getDingEmployeeId());
    }

    /**
     * 设置
     *
     * @return
     */
    @RequestMapping(value = "/saveOperation", method = RequestMethod.POST)
    public Result<Integer> saveOperation(@RequestBody DingMappingEmployeeResult dingMappingEmployeeResult) {
        UserVo userVo = getUserVo();
        if (userVo == null) {
            log.warn("纷享用户为空，queryKcEnterprise userVo=[{}].", userVo);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        String fsUserAccount = "E." + userVo.getEnterpriseAccount() + "." + userVo.getEmployeeId();

        if (dingMappingEmployeeResult == null) {
            log.warn("设置参数异常，dingMappingEmployeeResult=[{}].", dingMappingEmployeeResult);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        dingMappingEmployeeResult.setEi(userVo.getEnterpriseId());
        return objectMappingService.saveOperation(dingMappingEmployeeResult);
    }

    @RequestMapping(value = "/createFxEmployee", method = RequestMethod.POST)
    public Result<Integer> createFxEmployee(@RequestBody EmployeeVo vo) {
        UserVo userVo = getUserVo();
        if (userVo == null) {
            log.warn("纷享用户为空，queryKcEnterprise userVo=[{}].", userVo);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        String fsUserAccount = "E." + userVo.getEnterpriseAccount() + "." + userVo.getEmployeeId();

        if (vo == null) {
            log.warn("创建纷享账户参数异常，vo=[{}].", vo);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        vo.setEi(userVo.getEnterpriseId());
        vo.setUpdateBy(userVo.getEmployeeId());
        return objectMappingService.createFxEmployee(vo);
    }

    //model2选择员工的时候，同步在crm创建员工
    @RequestMapping(value = "/syncCreateEmployee", method = RequestMethod.POST)
    public Result<Integer> syncCreateEmployee(@RequestBody List<CreateCrmEmployeeVo> voList) {
        UserVo userVo = getUserVo();
        if (userVo == null) {
            log.warn("纷享用户为空，queryKcEnterprise userVo=[{}].", userVo);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        String fsUserAccount = "E." + userVo.getEnterpriseAccount() + "." + userVo.getEmployeeId();

        if (voList == null) {
            log.warn("创建纷享账户参数异常，vo=[{}].", voList);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
//        //判断选中的员工数量是否满足可用配额
//        GetEnterpriseEditionArg arg = new GetEnterpriseEditionArg();
//        arg.setEnterpriseId(userVo.getEnterpriseId());
//        GetEnterpriseEditionResult enterpriseEdition = enterpriseEditionService.getEnterpriseEdition(arg);
//        Integer available = enterpriseEdition.getEnterpriseEditionData().getEmployeeNumLimit() - enterpriseEdition.getEnterpriseEditionData().getUsedEmployeeNum();
//        //判断是否可用配额
//        if (voList.size() > available) {
//            return Result.newError(ResultCode.AVAILABLE_EMPLOYEE_NOT_ENOUGH);
//        }
//        vo.setEi(userVo.getEnterpriseId());
//        vo.setUpdateBy(userVo.getEmployeeId());
        log.info("syncCreateEmployee,voList={}",voList);
        return objectMappingService.batchCreateFxEmployee(voList, userVo.getEnterpriseId(), userVo.getEmployeeId());
    }


    //条件查询员工
    @RequestMapping(value = "/conditionQueryEmployees", method = RequestMethod.POST)
    public Result<ConditionEmployeeResult> ConditionQueryEmployees(@RequestBody QueryEmployeeVo queryEmployeeVo) {
        UserVo userVo = getUserVo();
        if (userVo == null) {
            log.warn("纷享用户为空，queryKcEnterprise userVo=[{}].", userVo);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        if (queryEmployeeVo == null) {
            log.warn("ObjectMapping controller ConditionQueryEmployees queryEmployeeVo:{}", queryEmployeeVo);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        queryEmployeeVo.setPageNumber(Optional.ofNullable(queryEmployeeVo.getPageNumber()).orElse(1));
        queryEmployeeVo.setPageSize(Optional.ofNullable(queryEmployeeVo.getPageSize()).orElse(20));
//        queryEmployeeVo.setDingDeptId(Optional.ofNullable(queryEmployeeVo.getDingDeptId()).orElse(1L));
        Result<ConditionEmployeeResult> conditionEmployeeResultResult = objectMappingService.conditionEmployee(queryEmployeeVo, userVo.getEnterpriseId());
        log.info("cobjectMapping controller onditionEmployeeResultResult:{}", conditionEmployeeResultResult);
        return conditionEmployeeResultResult;
    }

    //返回部门
    @RequestMapping(value = "/conditionDepts", method = RequestMethod.GET)
    public Result<List<DeptVo>> conditionDepts() {
        UserVo userVo = getUserVo();
        if (userVo == null) {
            log.warn("纷享用户为空，queryKcEnterprise userVo=[{}].", userVo);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        return objectMappingService.conditionDepts(userVo.getEnterpriseId());
    }

    //全量同步
    @RequestMapping(value = "/allSynchronizedEmployee", method = RequestMethod.GET)
    public Result<Void> allSynchronizedEmployee(@RequestParam Long dingDeptId) {
        UserVo userVo = getUserVo();
        if (userVo == null) {
            log.warn("纷享用户为空，queryKcEnterprise userVo=[{}].", userVo);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        //查询未同步状态的员工，按照钉钉部门的ID
        QueryEmployeeVo queryEmployeeVo = new QueryEmployeeVo();
        queryEmployeeVo.setBindStatus(0);
        queryEmployeeVo.setDingDeptId(dingDeptId);
        Result<ConditionEmployeeResult> result = objectMappingService.conditionEmployee(queryEmployeeVo, userVo.getEnterpriseId());
        CountDownLatch countDownLatch = new CountDownLatch(result.getData().getDataList().size());
        executorService.execute(new Runnable() {
            @Override
            public void run() {

                List<CreateCrmEmployeeVo> createCrmEmployeeVos = Lists.newArrayList();
                result.getData().getDataList().stream().forEach(item -> {
                    executorService.execute(new Runnable() {
                        @Override
                        public void run() {
                            CreateCrmEmployeeVo vo = new CreateCrmEmployeeVo();
                            vo.setDingDeptId(item.getDingDeptId());
                            vo.setDingEmployeeId(item.getDingEmployeeId());
                            vo.setGender(item.getGender());
                            vo.setEi(item.getEi());
                            vo.setMobile(item.getDingEmployeePhone());
                            vo.setName(item.getDingEmployeeName());
                            vo.setUpdateBy(userVo.getEmployeeId());
                            vo.setId(item.getId().intValue());
                            createCrmEmployeeVos.add(vo);
                            countDownLatch.countDown();
                        }
                    });
                });
                try {
                    countDownLatch.await();
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                log.info("countDown latch createEmployeesize:{},result size:{}", createCrmEmployeeVos.size(), result.getData().getDataList().size());
                objectMappingService.batchCreateFxEmployee(createCrmEmployeeVos, userVo.getEnterpriseId(), userVo.getEmployeeId());
            }
        });
        return new Result<>();
    }

    /**
     * 重新映射。处理员工在对应的部门下
     */
    @RequestMapping(value = "/againEmployeeMapping", method = RequestMethod.POST)
    public Result<Void> againEmployeeMapping() {
        UserVo userVo = getUserVo();
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                log.info("againEmployeeMapping start ....");
                objectMappingService.againEmployeeMapping(userVo.getEnterpriseId());
            }
        });
        return new Result<>();
    }

    /**
     * 不依赖外部接口，重新映射部门
     */
    @RequestMapping(value = "/independenceAgainMapping", method = RequestMethod.POST)
    public Result<Void> independenceAgainMapping() {
        UserVo userVo = getUserVo();
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                log.info("againEmployeeMapping start ....");
                objectMappingService.independenceAgainMapping(userVo.getEnterpriseId());
            }
        });
        return new Result<>();
    }


    /**
     * 全部员工拉取数据插入
     */
    @RequestMapping(value = "/allPullEmployeeInsert", method = RequestMethod.GET)
    public Result<Void> allPullEmployeeInsert() {
        UserVo userVo = getUserVo();
        String traceId = TraceUtils.getTraceId();
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                TraceUtils.initTraceId(traceId);
                log.info("allPullInsert start ....");
                objectMappingService.allPullEmployeeInsert(userVo.getEnterpriseId(), userVo.getEmployeeId(), userVo.getEnterpriseAccount());
            }
        });
        return new Result<>();
    }

    /**
     * 全量拉取部门，在映射表upsert部门信息
     */
    @RequestMapping(value = "/upsertAllDept", method = RequestMethod.POST)
    public Result<Void> upsertAllDept() {
        UserVo userVo = getUserVo();
        String traceId = TraceUtils.getTraceId();
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                TraceUtils.initTraceId(traceId);
                log.info("allPullInsert start ....");
                objectMappingService.upsertAllDept(userVo.getEnterpriseId(), userVo.getEmployeeId(), userVo.getEnterpriseAccount());
            }
        });
        return new Result<>();
    }

    /**
     * 处理沒有主部門的员工
     */
    @RequestMapping(value = "/fixNoMainDept", method = RequestMethod.GET)
    public Result<Void> fixNoMainDept() {
        UserVo userVo = getUserVo();
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                log.info("fixNoMainDept start ....");
                objectMappingService.fixNoMainDept(userVo.getEnterpriseId(), userVo.getEmployeeId(), userVo.getEnterpriseAccount());
            }
        });
        return new Result<>();
    }

    /**
     * 处理钉钉老板电器的部门组织架构问题，多个部门在根级
     */
    @RequestMapping(value = "/fixTreeDept", method = RequestMethod.GET)
    public Result<Void> fixTreeDept() {
        UserVo userVo = getUserVo();
        executorService.execute(new Runnable() {
            @Override
            public void run() {
                log.info("fixNoMainDept start ....");
                objectMappingService.fixTreeDept(userVo.getEnterpriseId(), userVo.getEnterpriseAccount(), userVo.getEmployeeId());
            }
        });
        return new Result<>();
    }

    /**
     * 删除重复的部门
     */
    @RequestMapping(value = "/deleteDept", method = RequestMethod.GET)
    public Result<Void> deleteDept(@RequestParam("ei") Integer ei, @RequestParam("id") Integer id) {
        UserVo userVo = getUserVo();
        log.info("fixNoMainDept start ....");
        objectMappingService.removeDataDept(ei, id);
        return new Result<>();
    }
    //objectMappingService.stopEmp(corpId, leaveUserIds);

    /**
     * 删除钉钉员工
     */
    @RequestMapping(value = "/deleteEmp", method = RequestMethod.GET)
    public Result<Void> deleteEmp(@RequestParam("ei") Integer corpId, @RequestParam("id") String empId) {
        UserVo userVo = getUserVo();
        log.info("fixNoMainDept start ....");
        objectMappingService.deleteEmpByDingId(corpId, empId);
        return new Result<>();
    }

    @RequestMapping(value = "/pullEmp",method = RequestMethod.POST)
    public Result<Integer> pullEmp(@RequestBody Dept dept,@RequestParam("ei") Integer ei,@RequestParam("clientIp") String clientIp){
        Result<Integer> voidResult = objectMappingService.insertMappingEmp(dept, ei, clientIp, null);
        return voidResult;
    }

    @RequestMapping(value = "/isGrayCorp",method = RequestMethod.GET)
    public Result<Boolean> isGrayCorp(){
        UserVo userVo = getUserVo();
        Result<Boolean> isGrayCorp = objectMappingService.isGrayCorp(userVo.getEnterpriseAccount());
        return isGrayCorp;
    }

    @RequestMapping(value = "/initAgainDept",method = RequestMethod.GET)
    public Result<Boolean> initAgainDept() throws InterruptedException {
        UserVo userVo = getUserVo();
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    objectMappingService.initAgainDept(userVo.getEnterpriseId());
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();
        return Result.newSuccess();
    }

    /**
     * 根据钉钉用户信息查询纷享用户信息
     * @param arg
     * @return
     */
    @RequestMapping(value = "/getFsUserInfo", method = RequestMethod.POST)
    public Result<FsUserInfoModel> getFsUserInfo(@RequestBody GetFsUserInfoArg arg) {
        if(arg==null || StringUtils.isEmpty(arg.getOutEa()) || StringUtils.isEmpty(arg.getOutUserId())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        return objectMappingService.getFsUserInfo(arg.getOutEa(),arg.getOutUserId());
    }
}
