package com.facishare.open.qywx.web.manager;

import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.outer.oa.connector.i18n.qywx.I18NStringEnum;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.messagesend.data.ObjectData;
import com.facishare.open.qywx.web.utils.HttpHelper;
import com.fxiaoke.otherrestapi.function.arg.FunctionServiceExecuteArg;
import com.fxiaoke.otherrestapi.function.data.HeaderObj;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.rmi.RemoteException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class SfaApiManager {
    private HttpHelper httpHelper = new HttpHelper();
    /**
     * CRM系统管理员身份
     **/
    private final int USER_SYSTEM = -10000;

    public Result<Object> executeCustomFunction(HeaderObj headerObj, FunctionServiceExecuteArg arg) throws RemoteException {
        Map<String, String> headerMap = getHeader(headerObj);

        String url = Joiner.on(StringUtils.EMPTY).join(ConfigCenter.PAAS_FUNCTION_URL, "/v1/function/currencyFunction");

        String httpResult = httpHelper.postJsonData3(url, headerMap, arg);
        log.debug("CustomFunctionServiceImpl.currencyFunction,objectData={}", httpResult);
        ObjectData objectData = new Gson().fromJson(httpResult, new TypeToken<ObjectData>() {
        });
        if (objectData == null) {
            return new Result<>().addError(ErrorRefer.INTERNAL_ERROR.getCode(), "调用函数失败", I18NStringEnum.s3.getI18nKey());
        }

        String errCode = objectData.getString("errCode");
        String errMessage = objectData.getString("errMessage");
        Map<String, Object> resultMap = objectData.getMap("result");

        if (resultMap == null)
            return new Result<>().addError(ErrorRefer.INTERNAL_ERROR.getCode(), "调用函数失败", I18NStringEnum.s3.getI18nKey());

        Boolean success = MapUtils.getBoolean(resultMap, "success");
        String errorInfo = MapUtils.getString(resultMap, "errorInfo");

        if (success == false) {
            List<String> items = Splitter.on(":::").splitToList(errorInfo);
            if (CollectionUtils.isNotEmpty(items) && items.size() == 2) {
                String code = "0";
                try {
                    code = items.get(0);
                } catch (Exception e) {
                    code = errCode;
                }
                return new Result<>(code, items.get(1),null);
            }
        }

        if (success != null && success == false) {
            return new Result<>(errCode, errMessage,null);
        }
        return new Result<>(resultMap.get("functionResult"));
    }

    private Map<String, String> getHeader(HeaderObj headerObj) {
        Map<String, String> headerMap = new HashMap<>();
        headerObj.forEach((key, value) -> {
            headerMap.put(key, value == null ? "" : value.toString());
        });
        return headerMap;
    }
}
