package com.facishare.open.qywx.web.controller.outer;

import com.alibaba.dubbo.common.URL;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.qywx.web.template.model.GenFsTicketModel;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountbind.utils.TraceUtil;
import com.facishare.open.qywx.accountinner.arg.SendTextNoticeArg;
import com.facishare.open.qywx.accountinner.service.NotificationService;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountsync.core.enums.QYWXDataTypeEnum;
import com.facishare.open.qywx.accountsync.model.EnterpriseModel;
import com.facishare.open.qywx.accountsync.model.OAConnectorOpenDataModel;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinCorpBindBo;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.web.enums.SourceTypeEnum;
import com.facishare.open.qywx.web.template.inner.login.QyweixinLoginTemplate;
import com.facishare.open.qywx.web.utils.SecurityUtil;
import com.facishare.open.outer.oa.connector.i18n.qywx.I18NStringEnum;
import com.facishare.open.outer.oa.connector.i18n.qywx.I18NStringManager;
import com.facishare.open.outer.oa.connector.i18n.qywx.I18nUtils;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.type.EmployeeEntityStatus;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 企业微信auth授权相关接口集合
 * <AUTHOR>
 * @date ********
 */
@RestController
@Slf4j
@RequestMapping("/qyweixin")
public class AuthController {
    @Autowired
    private QyweixinGatewayInnerService qyweixinGatewayService;
    @Autowired
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;
    @Autowired
    private QyweixinAccountSyncService qyweixinAccountSyncService;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;
    @Resource(name = "qywxI18NStringManager")
    private I18NStringManager i18NStringManager;

    @ReloadableProperty("domainPrefix")
    private String domainPrefix;
    @ReloadableProperty("appLoginAuthScope")
    private String appLoginAuthScope;

    @Autowired
    private RedisDataSource redisDataSource;

    @Resource
    private QyweixinLoginTemplate qyweixinLoginTemplate;

    /**
     * 固定值：/hcrm/wechat/function/redirect?ticket={ticket}
     */
    @ReloadableProperty("hcrm.wechat.function.redirect.url")
    private String hcrmWechatFunctionRedirectUrl;

    private static final String STATE = "neeJw0w3Yw32Qhi6";

    private static final String VER = "V1_";

    /**
     * 企业微信通用auth授权接口，可用于企微侧边栏等需要通用授权的场景，原生支持一对多
     * @param appId CRM应用的appId
     * @param redirectUrl 默认采用base64编码
     * @param response
     * @throws IOException
     */
    @RequestMapping(value = "/doAuth", method = RequestMethod.GET)
    public void doAuth(@RequestParam(required = false) String appId,
                       @RequestHeader(value = "user-agent") String userAgent,
                       @RequestParam String redirectUrl,
                       HttpServletResponse response) throws IOException {
        log.info("AuthController.doAuth,appId={},redirectUrl={}", appId,redirectUrl);

        log.info("AuthController.doAuth,userAgent={}", userAgent);
        String lang = I18nUtils.getLang(userAgent);
        log.info("AuthController.doAuth,lang={}", lang);

        if (StringUtils.isEmpty(appId)) {
            appId = ConfigCenter.crmAppId;
        }
        String callbackUrl = domainPrefix + "/qyweixin/callback/auth?appId=" + appId +"&lang=" + lang + "&redirectUrl=" + redirectUrl;
        callbackUrl = URL.encode(callbackUrl);
        String url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" + appId + "&redirect_uri=" + callbackUrl + "&response_type=code&scope=" + appLoginAuthScope + "&state=" + STATE + "#wechat_redirect";
        log.info("AuthController.doAuth,url={},callbackUrl={}", url, callbackUrl);
        response.sendRedirect(url);
    }

    /**
     * 企业微信通用auth授权接口，可用于企微侧边栏等需要通用授权的场景，原生支持一对多
     * @param appId CRM应用的appId
     * @param redirectUrl 默认采用base64编码
     * @param response
     * @throws IOException
     */
    @RequestMapping(value = "/doRepAuth", method = RequestMethod.GET)
    public void doRepAuth(@RequestParam String corpId,
                          @RequestParam(required = false) String appId,
                          @RequestHeader(value = "user-agent") String userAgent,
                          @RequestParam String redirectUrl,
                          HttpServletRequest request,
                          HttpServletResponse response) throws Exception {
        log.info("AuthController.doRepAuth,appId={},redirectUrl={}", appId, redirectUrl);

        log.info("AuthController.doRepAuth,userAgent={}", userAgent);
        String lang = I18nUtils.getLang(userAgent);
        log.info("AuthController.doRepAuth,lang={}", lang);

        if (StringUtils.isEmpty(appId)) {
            appId = ConfigCenter.crmAppId;
        }
        String callbackUrl = domainPrefix + "/qyweixin/callback/auth?appId=" + appId + "&lang=" + lang + "&redirectUrl=" + redirectUrl;
        callbackUrl = URL.encode(callbackUrl);


        String state = appId + "@" + corpId;

        Result<QyweixinCorpBindBo> corpBindBoResult = qyweixinGatewayService.getCorpBindInfo(corpId, appId);
        log.info("ControllerQYWeixin.doRepAppLogin,corpBindBoResult={}", corpBindBoResult);

        if(!corpBindBoResult.isSuccess() || corpBindBoResult.getData() == null) {
            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", i18NStringManager.get(I18NStringEnum.s117,lang,null));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s118,lang,null));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return;
        }

        String agentId = corpBindBoResult.getData().getAgentId();

        String url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid="+corpId+"&redirect_uri="+callbackUrl+"&response_type=code&scope=snsapi_privateinfo&state="+state+"&agentid=" +agentId+"#wechat_redirect";
        log.info("trace doRepAppLogin, get url:{} ", url);
        response.sendRedirect(url);
    }

    /**
     * 企业微信oauth授权回调接口，支持一对多场景
     * @param code 企微授权码
     * @param state
     * @param appId
     * @param redirectUrl 默认采用base64编码
     * @param response
     * @throws IOException
     */
    @RequestMapping(value = "/callback/auth", method = RequestMethod.GET)
    public void callbackAuth(@RequestParam String code,
                             @RequestParam String state,
                             @RequestParam String appId,
                             @RequestParam(required = false,defaultValue = I18NStringManager.DEFAULT_LANG) String lang,
                             @RequestParam String redirectUrl,
                             HttpServletResponse response,
                             HttpServletRequest request) throws Exception {
        log.info("AuthController.callbackAuth,code={},state={},appid={},redirectUrl={}", code, state, appId, redirectUrl);

        if (StringUtils.isEmpty(appId)) {
            appId = ConfigCenter.crmAppId;
        }

        String outEa = null;
        if(StringUtils.startsWithIgnoreCase(appId,"dk")) {
            List<String> items = Splitter.on("@").splitToList(state);
            appId = items.get(0);
            outEa = items.get(1);
        }

        if(StringUtils.isEmpty(lang)) {
            lang = TraceUtil.getLocale();
        }
        log.info("callbackAuth,lang={}",lang);
        i18NStringManager.setDefaultRequestScope(request,lang);

        //1.用code换企业微信用户信息
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("code", code);
        dataMap.put("appId", appId);
        dataMap.put("outEa", outEa);
        dataMap.put("isIsvCode", Boolean.TRUE);
        MethodContext context = MethodContext.newInstance(dataMap);

        qyweixinLoginTemplate.getOutUserInfoByCode(context);

        Result<Object> result = (Result<Object>) context.getResult().getData();

//        Result<Object> result = qyweixinGatewayService.code2AppLoginUserInfo(code, appId, outEa);
        log.info("AuthController.callbackAuth,result={}",result);
        if(!result.isSuccess() || ObjectUtils.isEmpty(result.getData())) {
            request.setAttribute("errorCode", result.getErrorCode());
            request.setAttribute("errorMsg", StringUtils.isNotEmpty(result.getErrorMsg()) ? result.getErrorMsg() : i18NStringManager.get(I18NStringEnum.s119,lang,null));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s120,lang,null));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return;
        }
        Object userInfo = result.getData();

        String outUserId = null;
        if (userInfo instanceof QyweixinUserDetailInfoRsp) {
            QyweixinUserDetailInfoRsp userDetailInfoRsp = (QyweixinUserDetailInfoRsp) userInfo;
            outEa = userDetailInfoRsp.getCorpid();
            outUserId = userDetailInfoRsp.getUserid();
        } else if(userInfo instanceof QyweixinUserSimpleInfoRsp) {
            QyweixinUserSimpleInfoRsp userSimpleInfoRsp = (QyweixinUserSimpleInfoRsp) userInfo;
            outEa = userSimpleInfoRsp.getCorpId();
            outUserId = userSimpleInfoRsp.getUserId();
        }
        log.info("AuthController.callbackAuth,outEa={},outUserId={}",outEa,outUserId);
        //有跨云逻辑不能直接查询人员绑定关系作为开始
        //优先查询企业绑定关系
        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEnterpriseMapping>> enterpriseMappingResult = qyweixinAccountBindService.selectAllEnterpriseBind(SourceTypeEnum.QYWX.getSourceType(), outEa);
        if(!enterpriseMappingResult.isSuccess()) {
            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", StringUtils.isNotEmpty(enterpriseMappingResult.getErrorMsg()) ? enterpriseMappingResult.getErrorMsg() : i18NStringManager.get(I18NStringEnum.s121,lang,null));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s122,lang,null));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return;
        }

        List<QyweixinAccountEnterpriseMapping> enterpriseMappings = enterpriseMappingResult.getData();
        if(CollectionUtils.isEmpty(enterpriseMappings)) {
            //上报
            OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                    .appId(appId)
                    .channelId(ChannelEnum.qywx.name())
                    .dataTypeId(QYWXDataTypeEnum.EMPLOYEE_LOGIN.getDataType())
                    .corpId(outEa)
                    .outUserId(outUserId)
                    .errorCode("100")
                    .errorMsg(i18NStringManager.get(I18NStringEnum.s123,lang,null))
                    .build();
            qyweixinAccountSyncService.uploadOaConnectorOpenData(model);
            //告警
            SendTextNoticeArg arg = new SendTextNoticeArg();
            arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
            List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
            arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
            arg.setMsgTitle(i18NStringManager.get(I18NStringEnum.s124,lang,null));
            String msg = String.format(I18NStringEnum.s125.getI18nValue(), outEa, outUserId, TraceUtil.get());
            arg.setMsg(i18NStringManager.get2(I18NStringEnum.s125.getI18nKey(),lang,null,msg,Lists.newArrayList(
                    outEa, outUserId, TraceUtil.get()
            )));
            notificationService.sendQYWXNotice(arg);

            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s126.getI18nKey(),
                    lang,
                    null,
                    String.format(I18NStringEnum.s126.getI18nValue(),outEa,outUserId),
                    Lists.newArrayList(
                            outEa, outUserId
                    )));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s127,lang,null));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return;
        }

        String domain = domainPrefix;

        //获取正常状态下的企业列表
        List<QyweixinAccountEnterpriseMapping> normalEnterpriseMappingList = enterpriseMappings.stream()
                .filter(v -> v.getStatus() == 0)
                .collect(Collectors.toList());

        boolean hasMore = Boolean.FALSE;
        Result<List<QyweixinAccountEmployeeMapping>> employeeMappingResult = null;
        if(normalEnterpriseMappingList.size() > 1) {
            //有跨云的企业不用
            Optional<QyweixinAccountEnterpriseMapping> matchingMapping = normalEnterpriseMappingList.stream()
                    .filter(mapping -> !mapping.getDomain().equals(domainPrefix))
                    .findFirst();

            if (matchingMapping.isPresent()) {
                hasMore = Boolean.TRUE;
            } else {
                //兼容老逻辑，看下人员绑定关系
                employeeMappingResult = qyweixinAccountBindInnerService.getEmployeeMapping(outEa,
                        outUserId, -1, null);
                log.info("AuthController.callbackAuth,employeeMappingResult={}", employeeMappingResult);
                if (!employeeMappingResult.isSuccess()) {
                    request.setAttribute("errorCode", "s320050002");
                    request.setAttribute("errorMsg", StringUtils.isNotEmpty(employeeMappingResult.getErrorMsg()) ? employeeMappingResult.getErrorMsg() : i18NStringManager.get(I18NStringEnum.s128,lang,null));
                    request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s122,lang,null));
                    request.getRequestDispatcher("/index.jsp").forward(request, response);
                    return;
                }

                if (employeeMappingResult.getData().size() > 1) {
                    hasMore = Boolean.TRUE;
                }
            }
        }
        log.info("AuthController.callbackAuth,hasMore={}",hasMore);

        //一对多
        if(normalEnterpriseMappingList.size() > 1 && hasMore) {
            //在纷享环境处理
            response.sendRedirect(domain + "/pc-login/build/select_enterprise.html?channel=qywx&outEa="+ URLEncoder.encode(SecurityUtil.encryptStr(VER+outEa), "utf-8")+"&appId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+appId), "utf-8")+"&userId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+outUserId), "utf-8")+"&redirectUrl="+redirectUrl);
        } else if(CollectionUtils.isEmpty(normalEnterpriseMappingList)) {
            //没有正常的
            List<QyweixinAccountEnterpriseMapping> createEnterpriseMappingList = enterpriseMappings.stream()
                    .filter(v -> v.getStatus() == 100)
                    .collect(Collectors.toList());
            if(CollectionUtils.isEmpty(createEnterpriseMappingList)) {
                //只有停用状态的企业
                throw new RuntimeException(i18NStringManager.get(I18NStringEnum.s129,lang,null));
            }
            log.info("AuthController.callbackAuth,createEnterpriseMappingList={}",createEnterpriseMappingList);
            long time = System.currentTimeMillis() - createEnterpriseMappingList.get(0).getGmtCreate().getTime();
            log.info("AuthController.callbackAuth,time={}",time);
            if(time > 15 * 1000) {
                //开通中的
                //上报
                OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                        .appId(appId)
                        .channelId(ChannelEnum.qywx.name())
                        .dataTypeId(QYWXDataTypeEnum.EMPLOYEE_LOGIN.getDataType())
                        .ea(createEnterpriseMappingList.get(0).getFsEa())
                        .corpId(outEa)
                        .outUserId(outUserId)
                        .errorCode("101")
                        .errorMsg(i18NStringManager.get(I18NStringEnum.s123,lang,null))
                        .build();
                qyweixinAccountSyncService.uploadOaConnectorOpenData(model);
                //告警
                SendTextNoticeArg arg = new SendTextNoticeArg();
                arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
                List<String> receivers = new LinkedList<>(ConfigCenter.ENTERPRISE_OPEN_NOTIFICATION_MEMBERS);
                arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
                arg.setMsgTitle(i18NStringManager.get(I18NStringEnum.s124,lang,null));
                String msg = String.format(I18NStringEnum.s143.getI18nValue(), outEa, outUserId, createEnterpriseMappingList.get(0).getFsEa(), TraceUtil.get());
                arg.setMsg(i18NStringManager.get2(I18NStringEnum.s143.getI18nKey(),lang,null,msg,Lists.newArrayList(
                        outEa, outUserId, createEnterpriseMappingList.get(0).getFsEa(), TraceUtil.get()
                )));
                notificationService.sendQYWXNotice(arg);
            }

            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s149.getI18nKey(),
                    lang,
                    null,
                    String.format(I18NStringEnum.s149.getI18nValue(),outEa,outUserId),
                    Lists.newArrayList(
                            outEa, outUserId
                    )));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s144,lang,null));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
        } else {
            //只有一对一，特殊逻辑，还需要查询
            QyweixinAccountEnterpriseMapping enterpriseMapping = normalEnterpriseMappingList.get(0);
            if(normalEnterpriseMappingList.size() > 1 && employeeMappingResult != null && CollectionUtils.isNotEmpty(employeeMappingResult.getData())) {
                log.info("AuthController.callbackAuth, hasMore={}, employeeMappingResult={}", hasMore, employeeMappingResult);
                String fsAccount = employeeMappingResult.getData().get(0).getFsAccount();
                List<String> accountList = Splitter.on(".").splitToList(fsAccount);

                // 使用 Stream API 查找匹配项
                Optional<QyweixinAccountEnterpriseMapping> matchingMapping = normalEnterpriseMappingList.stream()
                        .filter(mapping -> mapping.getFsEa().equals(accountList.get(1)))
                        .findFirst();

                if(matchingMapping.isPresent()) {
                    enterpriseMapping = matchingMapping.get();
                }
            }
            log.info("AuthController.callbackAuth,enterpriseMapping={}", enterpriseMapping);
            domain = enterpriseMapping.getDomain();
            if(!domain.equals(domainPrefix)) {
                //转到对应的环境处理
                String redirectUri = domain + "/qyweixin/loginCloudAuth?channel=qywx&outEa="+URLEncoder.encode(SecurityUtil.encryptStr(VER+outEa), "utf-8")+"&appId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+appId), "utf-8")+"&userId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+outUserId), "utf-8")+"&fsEa="+URLEncoder.encode(SecurityUtil.encryptStr(VER+enterpriseMapping.getFsEa()), "utf-8")+"&redirectUrl="+URLEncoder.encode(redirectUrl, "utf-8");
                response.sendRedirect(redirectUri);
                return;
            }
            //查询人员是否正常
            employeeMappingResult = qyweixinAccountBindInnerService.getEmployeeMapping(outEa,
                    outUserId, -1, enterpriseMapping.getFsEa());
            log.info("AuthController.callbackAuth,employeeMappingResult={}",employeeMappingResult);
            if(!employeeMappingResult.isSuccess()) {
                request.setAttribute("errorCode", "s320050002");
                request.setAttribute("errorMsg", StringUtils.isNotEmpty(employeeMappingResult.getErrorMsg()) ? employeeMappingResult.getErrorMsg() : i18NStringManager.get(I18NStringEnum.s128,lang,null));
                request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s122,lang,null));
                request.getRequestDispatcher("/index.jsp").forward(request, response);
                return;
            }
            if(CollectionUtils.isEmpty(employeeMappingResult.getData())) {
                if(enterpriseMapping.getBindType() == 0) {
                    //上报
                    OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                            .appId(appId)
                            .channelId(ChannelEnum.qywx.name())
                            .dataTypeId(QYWXDataTypeEnum.EMPLOYEE_LOGIN.getDataType())
                            .ea(enterpriseMapping.getFsEa())
                            .corpId(outEa)
                            .outUserId(outUserId)
                            .errorCode("102")
                            .errorMsg(i18NStringManager.get(I18NStringEnum.s130,lang,null))
                            .build();
                    qyweixinAccountSyncService.uploadOaConnectorOpenData(model);
                    //告警
                    SendTextNoticeArg arg = new SendTextNoticeArg();
                    arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
                    List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
                    arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
                    arg.setMsgTitle(i18NStringManager.get(I18NStringEnum.s124,lang,null));
                    String msg = String.format(I18NStringEnum.s145.getI18nValue(), outEa, outUserId, enterpriseMapping.getFsEa(), TraceUtil.get());
                    arg.setMsg(i18NStringManager.get2(I18NStringEnum.s145.getI18nKey(),lang,null,msg,Lists.newArrayList(
                            outEa, outUserId, enterpriseMapping.getFsEa(), TraceUtil.get()
                    )));
                    notificationService.sendQYWXNotice(arg);

                    request.setAttribute("errorCode", "s320050002");
                    request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s147.getI18nKey(),
                            lang,
                            null,
                            String.format(I18NStringEnum.s147.getI18nValue(), outEa, outUserId),
                            Lists.newArrayList(
                                    outEa, outUserId
                            )));
                    request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s131,lang,null));
                    request.setAttribute("errorPageOutEa", outEa);
                    request.setAttribute("errorPageFsEa", enterpriseMapping.getFsEa());
                    request.setAttribute("errorPageOutUserId", outUserId);
                    request.setAttribute("errorPageToken", ConfigCenter.ERROR_PAGE_TOKEN);
                    request.setAttribute("errorPageAppId", appId);
                    request.setAttribute("createEmployee", Boolean.TRUE);
                    request.getRequestDispatcher("/index.jsp").forward(request, response);
                    return;
                }
                request.setAttribute("errorCode", "s320050002");
                request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s147.getI18nKey(),
                        lang,
                        null,
                        String.format(I18NStringEnum.s147.getI18nValue(), outEa, outUserId),
                        Lists.newArrayList(
                                outEa, outUserId
                        )));
                request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s132,lang,null));
                request.getRequestDispatcher("/index.jsp").forward(request, response);
                return;
            }
            List<QyweixinAccountEmployeeMapping> normalEmployeeMapping = employeeMappingResult.getData().stream()
                    .filter(v -> v.getStatus() == 0)
                    .collect(Collectors.toList());
            if(CollectionUtils.isEmpty(normalEmployeeMapping)) {
                request.setAttribute("errorCode", "s320050002");
                request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s150.getI18nKey(),
                        lang,
                        null,
                        String.format(I18NStringEnum.s150.getI18nValue(),outEa,outUserId),
                        Lists.newArrayList(
                                outEa, outUserId
                        )));
                request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s146,lang,null));
                request.getRequestDispatcher("/index.jsp").forward(request, response);
                return;
            }
            List<String> accountList = Splitter.on(".").splitToList(normalEmployeeMapping.get(0).getFsAccount());
            //查询人员在crm的状态
            Result<List<EmployeeDto>> fsEmpUserResult = qyweixinAccountSyncService.getFsEmpUser(accountList.get(1), 1000, Lists.newArrayList(Integer.parseInt(accountList.get(2))));
            if(!fsEmpUserResult.isSuccess() || ObjectUtils.isEmpty(fsEmpUserResult.getData())) {
                request.setAttribute("errorCode", fsEmpUserResult.getErrorCode());
                request.setAttribute("errorMsg", StringUtils.isNotEmpty(fsEmpUserResult.getErrorMsg()) ? fsEmpUserResult.getErrorMsg() : i18NStringManager.get(I18NStringEnum.s133,lang,null));
                request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s151.getI18nKey(),
                    lang,
                    null,
                    String.format(I18NStringEnum.s151.getI18nValue(),accountList.get(1),accountList.get(2)),
                    Lists.newArrayList(
                            accountList.get(1),accountList.get(2)
                    )));
                request.getRequestDispatcher("/index.jsp").forward(request, response);
                return;
            } else if(fsEmpUserResult.getData().get(0).getStatus() != EmployeeEntityStatus.NORMAL) {
                request.setAttribute("errorCode", fsEmpUserResult.getErrorCode());
                request.setAttribute("errorMsg", StringUtils.isNotEmpty(fsEmpUserResult.getErrorMsg()) ? fsEmpUserResult.getErrorMsg() : i18NStringManager.get(I18NStringEnum.s134,lang,null));
                request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s152.getI18nKey(),
                    lang,
                    null,
                    String.format(I18NStringEnum.s152.getI18nValue(),accountList.get(1),accountList.get(2)),
                    Lists.newArrayList(
                            accountList.get(1),accountList.get(2)
                    )));
                request.getRequestDispatcher("/index.jsp").forward(request, response);
                return;
            }

            //正常登陆
            //4.如果一个企微企业只关联一个CRM企业，直接重定向到CRM登录页面
            Result<CorpTicketResult> ticket = qyweixinGatewayService.appAuth(userInfo, state,enterpriseMapping.getFsEa());
            log.info("AuthController.callbackAuth,ticket={}",ticket);
            if(!ticket.isSuccess()) {
                request.setAttribute("errorCode", ticket.getErrorCode());
                request.setAttribute("errorMsg", ticket.getErrorMsg());
                request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s135,lang,null));
                request.getRequestDispatcher("/index.jsp").forward(request, response);
                return;
            }

            //为兼容专属云老企业，还不能直接去掉
            GetEnterpriseDataResult enterpriseInfo = this.getEnterpriseInfo(accountList.get(1));
            if(ObjectUtils.isNotEmpty(enterpriseInfo) && ObjectUtils.isNotEmpty(enterpriseInfo.getEnterpriseData()) && StringUtils.isNotEmpty(enterpriseInfo.getEnterpriseData().getDomain())) {
                domain = enterpriseInfo.getEnterpriseData().getDomain();
            }
            log.info("AuthController.callbackAuth,domain1={}",domain);


            String loginUrl = domain + hcrmWechatFunctionRedirectUrl.replace("{ticket}",ticket.getData().getTicket()) + "&redirectUrl="+redirectUrl;
            log.info("AuthController.callbackAuth,loginUrl={}",loginUrl);
            response.sendRedirect(loginUrl);
        }
    }

    private String decodeData(String data) {
        Pattern pattern = Pattern.compile(VER+"(.*)");
        Matcher matcher = pattern.matcher(SecurityUtil.decryptStr(data));
        if (matcher.find()) {
            return matcher.group(1);
        } else {
            return null;
        }
    }

    /**
     * 跨云免登
     * @param outEa 外部企业id
     * @param appId 应用id
     * @param userId 用户id
     * @param fsEa 纷享ea
     * @param request
     * @throws Exception
     */
    @RequestMapping(value = "/loginCloudAuth", method = RequestMethod.GET)
    public void loginCloudAuth(@RequestParam String channel,
                               @RequestParam String appId,
                               @RequestParam(required = false,defaultValue = I18NStringManager.DEFAULT_LANG) String lang,
                               @RequestParam(required = false) String fsEa,
                               @RequestParam String outEa,
                               @RequestParam String userId,
                               @RequestParam(required = false) String redirectUrl,
                               @RequestParam(required = false) String url, //待办直接推送的
                               HttpServletResponse response,
                               HttpServletRequest request) throws Exception {
        log.info("AuthController.loginCloudAuth,channel={},appId={},fsEa={},outEa={},userId={}",channel,appId,fsEa,outEa,userId);

        appId = decodeData(appId);
        if(StringUtils.isEmpty(appId)) {
            return;
        }

        if(StringUtils.isNotEmpty(fsEa)) {
            fsEa = decodeData(fsEa);
            if(StringUtils.isEmpty(fsEa)) {
                return;
            }
        }

        outEa = decodeData(outEa);
        if(StringUtils.isEmpty(outEa)) {
            return;
        }

        userId = decodeData(userId);
        if(StringUtils.isEmpty(userId)) {
            return;
        }

        if(StringUtils.isEmpty(lang)) {
            lang = TraceUtil.getLocale();
        }
        log.info("loginCloudAuth,lang={}",lang);
        i18NStringManager.setDefaultRequestScope(request,lang);

        //查询企业绑定
        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEnterpriseMapping>> enterpriseMappingResult = qyweixinAccountBindService.selectAllEnterpriseBind(SourceTypeEnum.QYWX.getSourceType(), outEa);
        if(!enterpriseMappingResult.isSuccess()) {
            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", StringUtils.isNotEmpty(enterpriseMappingResult.getErrorMsg()) ? enterpriseMappingResult.getErrorMsg() : i18NStringManager.get(I18NStringEnum.s121,lang,null));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s122,lang,null));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return;
        }

        List<QyweixinAccountEnterpriseMapping> enterpriseMappings = enterpriseMappingResult.getData();
        if(CollectionUtils.isEmpty(enterpriseMappings)) {
            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s153.getI18nKey(),
                    lang,
                    null,
                    String.format(I18NStringEnum.s153.getI18nValue(),outEa,userId),
                    Lists.newArrayList(
                            outEa,userId
                    )));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s136,lang,null));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return;
        }
        //查询人员绑定
        Result<List<QyweixinAccountEmployeeMapping>> employeeMappingResult = qyweixinAccountBindInnerService.getEmployeeMapping(outEa,
                userId, -1, fsEa);
        log.info("AuthController.loginCloudAuth,employeeMappingResult={}",employeeMappingResult);
        if(!employeeMappingResult.isSuccess()) {
            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", StringUtils.isNotEmpty(employeeMappingResult.getErrorMsg()) ? employeeMappingResult.getErrorMsg() : i18NStringManager.get(I18NStringEnum.s128,lang,null));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s122,lang,null));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return;
        }

        if(CollectionUtils.isEmpty(employeeMappingResult.getData())) {
            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s147.getI18nKey(),
                    lang,
                    null,
                    String.format(I18NStringEnum.s147.getI18nValue(), outEa, userId),
                    Lists.newArrayList(
                            outEa, userId
                    )));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s132,lang,null));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return;
        }

        //正常登陆

        GenFsTicketModel ticketModel = new GenFsTicketModel();
        ticketModel.setAppId(appId);
        ticketModel.setOutEa(outEa);
        ticketModel.setFsEa(fsEa);
        ticketModel.setFsUserId(userId);

        MethodContext context2 = MethodContext.newInstance(ticketModel);
        qyweixinLoginTemplate.genFsTicket(context2);
        Result<String> ticket = context2.getResultData();

//        Result<String> ticket = qyweixinGatewayService.genFsTicket(appId, outEa, userId, fsEa);
        log.info("AuthController.loginCloudAuth,ticket={}",ticket);
        if(!ticket.isSuccess()) {
            request.setAttribute("errorCode", ticket.getErrorCode());
            request.setAttribute("errorMsg", ticket.getErrorMsg());
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s135,lang,null));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return;
        }
        //老企业兼容
        String domain = getEnterpriseInfo(fsEa).getEnterpriseData().getDomain();
        String loginUrl;
        if(StringUtils.isNotEmpty(url)) {
            url = url + (url.contains("?") ? "&" : "?") + "ticket="+ticket.getData();
            loginUrl = url.replace("https://www.fxiaoke.com",domain);
        } else if(StringUtils.isNotEmpty(redirectUrl)) {
            loginUrl = domain + hcrmWechatFunctionRedirectUrl.replace("{ticket}",ticket.getData()) + "&redirectUrl="+redirectUrl;
        } else {
            //跳转到正常的CRM主页
            loginUrl = qyweixinGatewayService.getRedirectLoginUrl(ticket.getData(), appId,outEa,domain);
        }
        log.info("AuthController.loginCloudAuth,loginUrl={}",loginUrl);
        response.sendRedirect(loginUrl);
    }

    /**
     * 获取和企业微信绑定的CRM企业EA列表
     * @param outEa
     * @return
     */
    @RequestMapping(value="/getFsEaList",method = RequestMethod.GET)
    @ResponseBody
    public Result<List<EnterpriseModel>> getFsEaList(@RequestParam String outEa,
                                                     @RequestParam String outUserId) throws Exception {
        log.info("ControllerQYWeixin.getFsEaList,outEa={},outUserId={}",outEa,outUserId);
        outEa = decodeData(URLDecoder.decode(outEa, "utf-8"));
        if(StringUtils.isEmpty(outEa)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        outUserId = decodeData(URLDecoder.decode(outUserId, "utf-8"));
        if(StringUtils.isEmpty(outUserId)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        Result<List<EnterpriseModel>> result = qyweixinAccountBindInnerService.getFsEaList(outEa, outUserId);
        log.info("ControllerQYWeixin.getFsEaList,result={}",result);

        if(result.isSuccess() && CollectionUtils.isNotEmpty(result.getData())) {
            for(EnterpriseModel model : result.getData()) {
                model.setEa(URLEncoder.encode(SecurityUtil.encryptStr(VER + model.getEa()), "utf-8"));
            }
        }
        return result;
    }

    /**
     * CRM登录授权，前端在选择CRM企业列表页面使用
     * @param outEa
     * @param appId
     * @param userId
     * @param fsEa 用于一个企微企业对多个CRM的场景
     * @param redirectUrl 需要重定向到的业务URL
     * @throws IOException
     */
    @RequestMapping(value="/loginByFsEa",method = RequestMethod.GET)
    @ResponseBody
    public Result<String> loginByFsEa(@RequestParam String outEa,
                                      @RequestParam String appId,
                                      @RequestParam String userId,
                                      @RequestParam String fsEa,
                                      @RequestParam(required = false) String redirectUrl,
                                      @RequestHeader(value = "user-agent") String userAgent,
                                      HttpServletResponse response,
                                      HttpServletRequest request) throws Exception {
        log.info("ControllerQYWeixin.loginByFsEa,outEa={},appId={},userId={},fsEa={}",outEa,appId,userId,fsEa);

        log.info("AuthController.loginByFsEa,userAgent={}", userAgent);
        String lang = I18nUtils.getLang(userAgent);
        log.info("AuthController.loginByFsEa,lang={}", lang);
        i18NStringManager.setDefaultRequestScope(request,lang);

        outEa = decodeData(URLDecoder.decode(outEa, "utf-8"));
        if(StringUtils.isEmpty(outEa)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        appId = decodeData(URLDecoder.decode(appId, "utf-8"));
        if(StringUtils.isEmpty(appId)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        userId = decodeData(URLDecoder.decode(userId, "utf-8"));
        if(StringUtils.isEmpty(userId)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        fsEa = decodeData(URLDecoder.decode(fsEa, "utf-8"));
        if(StringUtils.isEmpty(fsEa)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }

        //查看企业所处环境
        com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> qyweixinCorpIDResult =
                qyweixinAccountBindService.fsEaToOutEaAllResult2("qywx", fsEa, outEa);
        log.info("ControllerOpenQYWeixin.loginByFsEa,qyweixinCorpIDResult={}", qyweixinCorpIDResult);
        String domain = qyweixinCorpIDResult.getData().getDomain();
        if(!domain.equals(domainPrefix)) {
            //转到对应的环境处理
            String redirectUri = domain + "/qyweixin/loginCloudAuth?channel=qywx&outEa="+URLEncoder.encode(SecurityUtil.encryptStr(VER+outEa), "utf-8")+"&appId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+appId), "utf-8")+"&userId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+userId), "utf-8")+"&fsEa="+URLEncoder.encode(SecurityUtil.encryptStr(VER+fsEa), "utf-8")+"&redirectUrl="+URLEncoder.encode(redirectUrl, "utf-8");
            return new Result<>(redirectUri);
        }

        //1.生成CRM ticket

        GenFsTicketModel ticketModel = new GenFsTicketModel();
        ticketModel.setAppId(appId);
        ticketModel.setOutEa(outEa);
        ticketModel.setFsEa(fsEa);
        ticketModel.setFsUserId(userId);

        MethodContext context2 = MethodContext.newInstance(ticketModel);
        qyweixinLoginTemplate.genFsTicket(context2);
        Result<String> ticket = context2.getResultData();

//        Result<String> ticket = qyweixinGatewayService.genFsTicket(appId,outEa,userId,fsEa);
        if(!ticket.isSuccess() || StringUtils.isEmpty(ticket.getData())) {
            request.setAttribute("errorCode", ticket.getErrorCode());
            request.setAttribute("errorMsg", ticket.getErrorMsg());
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s135,lang,null));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return null;
        }
        //查询人员在crm的状态
        Result<List<QyweixinAccountEmployeeMapping>> employeeMappingResult = qyweixinAccountBindInnerService.getEmployeeMapping(outEa,
                userId, 0, fsEa);
        if(CollectionUtils.isEmpty(employeeMappingResult.getData())) {
            //先查询企业是否已经绑定了
            List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(outEa);
            log.info("ControllerQYWeixin.loginByFsEa,enterpriseMappingList={}", enterpriseMappingList);
            if(CollectionUtils.isEmpty(enterpriseMappingList)) {
                request.setAttribute("errorCode", "s320050002");
                request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s126.getI18nKey(),
                        lang,
                        null,
                        String.format(I18NStringEnum.s126.getI18nValue(),outEa,userId),
                        Lists.newArrayList(
                                outEa, userId
                        )));
                request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s137,lang,null));
                request.getRequestDispatcher("/index.jsp").forward(request, response);
                return null;
            } else {
                request.setAttribute("errorCode", "s320050002");
                request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s147.getI18nKey(),
                        lang,
                        null,
                        String.format(I18NStringEnum.s147.getI18nValue(), outEa, userId),
                        Lists.newArrayList(
                                outEa, userId
                        )));
                request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s132,lang,null));
                request.getRequestDispatcher("/index.jsp").forward(request, response);
                return null;
            }
        }
        List<String> accountList = Splitter.on(".").splitToList(employeeMappingResult.getData().get(0).getFsAccount());
        Result<List<EmployeeDto>> fsEmpUserResult = qyweixinAccountSyncService.getFsEmpUser(accountList.get(1), 1000, Lists.newArrayList(Integer.parseInt(accountList.get(2))));
        if(!fsEmpUserResult.isSuccess() || ObjectUtils.isEmpty(fsEmpUserResult.getData())) {
            request.setAttribute("errorCode", fsEmpUserResult.getErrorCode());
            request.setAttribute("errorMsg", StringUtils.isNotEmpty(fsEmpUserResult.getErrorMsg()) ? fsEmpUserResult.getErrorMsg() : i18NStringManager.get(I18NStringEnum.s133,lang,null));
            request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s151.getI18nKey(),
                    lang,
                    null,
                    String.format(I18NStringEnum.s151.getI18nValue(),accountList.get(1),accountList.get(2)),
                    Lists.newArrayList(
                            accountList.get(1),accountList.get(2)
                    )));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return null;
        } else if(fsEmpUserResult.getData().get(0).getStatus() != EmployeeEntityStatus.NORMAL) {
            request.setAttribute("errorCode", fsEmpUserResult.getErrorCode());
            request.setAttribute("errorMsg", StringUtils.isNotEmpty(fsEmpUserResult.getErrorMsg()) ? fsEmpUserResult.getErrorMsg() : i18NStringManager.get(I18NStringEnum.s134,lang,null));
            request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s152.getI18nKey(),
                    lang,
                    null,
                    String.format(I18NStringEnum.s152.getI18nValue(),accountList.get(1),accountList.get(2)),
                    Lists.newArrayList(
                            accountList.get(1),accountList.get(2)
                    )));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return null;
        }
        log.info("ControllerQYWeixin.loginByFsEa,ticket={}",ticket);

        //2.生成重定向uri
        String loginUrl = null;
        //需要直接透传redirectUrl
        //为兼容专属云老企业，还不能直接去掉
        GetEnterpriseDataResult enterpriseInfo = this.getEnterpriseInfo(accountList.get(1));
        domain = domainPrefix;
        if(ObjectUtils.isNotEmpty(enterpriseInfo) && ObjectUtils.isNotEmpty(enterpriseInfo.getEnterpriseData()) && StringUtils.isNotEmpty(enterpriseInfo.getEnterpriseData().getDomain())) {
            domain = enterpriseInfo.getEnterpriseData().getDomain();
        }
        log.info("AuthController.loginByFsEa,domain={}",domain);
        if(StringUtils.isNotEmpty(redirectUrl)) {
            loginUrl = domain + hcrmWechatFunctionRedirectUrl.replace("{ticket}",ticket.getData()) + "&redirectUrl="+redirectUrl;
        } else {
            //跳转到正常的CRM主页
            loginUrl = qyweixinGatewayService.getRedirectLoginUrl(ticket.getData(), appId,outEa,null);
        }
        log.info("AuthController.loginByFsEa,loginUrl={}",loginUrl);

        return new Result<>(loginUrl);
    }

    /**
     * 企微连接器auth授权回调URL
     * 场景：
     * 1.从集成平台->企微连接器设置页面->扫企微的码->回调这个URL
     * @param code
     * @param state 前端需要传 fsEa_dataCenterId
     * @param appId
     * @throws Exception
     */
    @RequestMapping(value = "/callback/auth2")
    public void callbackAuth2(@RequestParam(required = false) String code,
                              @RequestParam(required = false) String state,
                              @RequestParam(required = false) String appId,
                              @RequestParam(required = false,defaultValue = I18NStringManager.DEFAULT_LANG) String lang,
                              HttpServletResponse response,
                              HttpServletRequest request) throws Exception {
        log.info("callbackAuth2,code={},state={},appId={}",code,state,appId);
        log.info("AuthController.callbackAuth2,lang={}", lang);
        Result<QyweixinUserInfo3rdRsp> result = qyweixinGatewayService.getUserInfoByLoginAuthApp(code);
        log.info("callbackAuth2,result={}",result);

        i18NStringManager.setDefaultRequestScope(request,lang);
        String mainCrmAppId = ConfigCenter.crmAppId;
        if(result.isSuccess() && result.getData()!=null) {
            if(StringUtils.isNotEmpty(result.getData().getCorpid())) {
                String authUserId = result.getData().getUserid();
                String authCorpId = result.getData().getCorpid();
                Result<QyweixinCorpBindBo> corpBindBoResult = qyweixinGatewayService.getCorpBindInfo(authCorpId,
                        mainCrmAppId);
                log.info("callbackAuth2,corpBindBoResult={}",corpBindBoResult);
                if(corpBindBoResult.getData()==null || corpBindBoResult.getData().getStatus()!=0) {
                    mainCrmAppId = qyweixinGatewayService.getMainAppId(authCorpId).getData();
                    log.info("callbackAuth2,mainCrmAppId={}",mainCrmAppId);
                    corpBindBoResult = qyweixinGatewayService.getCorpBindInfo(authCorpId,
                            mainCrmAppId);
                    log.info("callbackAuth2,corpBindBoResult.2={}",corpBindBoResult);
                }
                if(corpBindBoResult.getData()==null) {
                    request.setAttribute("errorCode", "s320050002");
                    request.setAttribute("errorMsg", i18NStringManager.get(I18NStringEnum.s138,lang,null));
                    request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s138,lang,null));
                    request.getRequestDispatcher("/index.jsp").forward(request, response);
                    return;
                }
                //代开发应用不支持调用获取企微应用管理员接口
                if(!StringUtils.startsWithIgnoreCase(mainCrmAppId,"dk")) {
                    QyweixinCorpBindBo corpBindBo = corpBindBoResult.getData();
                    Integer agentId = Integer.valueOf(corpBindBo.getAgentId());
                    Result<QyweixinGetAdminListRsp> adminList = qyweixinAccountSyncService.getAdminList(corpBindBo.getCorpId(),
                            mainCrmAppId,
                            agentId);
                    log.info("callbackAuth2,adminList={}",adminList);
                    if(StringUtils.equalsIgnoreCase(adminList.getErrorCode(),"48004")) {
                        request.setAttribute("errorCode", "s320050002");
                        request.setAttribute("errorMsg", i18NStringManager.get(I18NStringEnum.s139,lang,null));
                        request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s140,lang,null));
                        request.getRequestDispatcher("/index.jsp").forward(request, response);
                        return;
                    }
                    if(adminList.getData()==null) {
                        request.setAttribute("errorCode", "s320050002");
                        request.setAttribute("errorMsg", i18NStringManager.get(I18NStringEnum.s141,lang,null));
                        request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s142,lang,null));
                        request.getRequestDispatcher("/index.jsp").forward(request, response);
                        return;
                    }
                    if(adminList.getData()!=null && CollectionUtils.isNotEmpty(adminList.getData().getAdmin())) {
                        boolean isAdmin = false;
                        for(QyweixinGetAdminListRsp.AdminModel adminModel : adminList.getData().getAdmin()) {
                            if(StringUtils.equalsIgnoreCase(adminModel.getUserid(),authUserId)) {
                                isAdmin = true;
                                break;
                            }
                        }
                        if(!isAdmin) {
                            request.setAttribute("errorCode", "s320050002");
                            request.setAttribute("errorMsg", i18NStringManager.get(I18NStringEnum.s141,lang,null));
                            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s142,lang,null));
                            request.getRequestDispatcher("/index.jsp").forward(request, response);
                            return;
                        }
                    }
                }
            }
        } else {
            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", i18NStringManager.get(I18NStringEnum.s138,lang,null));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s138,lang,null));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return;
        }
        String json = JSONObject.toJSONString(result.getData());
        String url = ConfigCenter.qywx_scan_code_auth_success_redirect_url+"?lang="+lang+"&code="+URLEncoder.encode(json);
        response.sendRedirect(url);
    }

    /**
     * 查询飞书连接器连接信息
     *
     */
    @RequestMapping(value = "/queryScanCodeAuthUrl", method = RequestMethod.POST)
    @ResponseBody
    public Result<String> queryScanCodeAuthUrl() {
        String lang = TraceUtil.getLocale();
        String fsLang = lang;
        log.info("queryScanCodeAuthUrl,lang={}",lang);
        if(StringUtils.isNotEmpty(lang) && StringUtils.containsIgnoreCase(lang,"zh")) {
            lang = "zh";
        } else {
            lang = "en";
        }
        log.info("queryScanCodeAuthUrl,lang2={}",lang);
        //企微登录授权专用suiteId，这个值不会变
        String appId = "wx867ad65506013329";
        String scanCodeAuthUrl = "https://login.work.weixin.qq.com/wwlogin/sso/login?login_type=ServiceApp&appid={appid}&redirect_uri={redirect_uri}&state={state}&lang={lang}";
        scanCodeAuthUrl = scanCodeAuthUrl.replace("{appid}", appId)
                .replace("{redirect_uri}", URLEncoder.encode(ConfigCenter.qywx_scan_code_auth_redirect_url)+"?lang="+fsLang)
                .replace("{state}", ConfigCenter.crmAppId)
                .replace("{lang}", lang);
        return new Result<>(scanCodeAuthUrl);
    }

    private GetEnterpriseDataResult getEnterpriseInfo(String fsEa) {
        GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
        arg.setEnterpriseAccount(fsEa);
        GetEnterpriseDataResult result = enterpriseEditionService.getEnterpriseData(arg);
        Map<String, String> cloudDomainEa = new Gson().fromJson(ConfigCenter.CLOUD_DOMAIN_EA, new TypeToken<Map<String, String>>() {
        });
        if(ObjectUtils.isNotEmpty(result) && cloudDomainEa.containsKey(fsEa)) {
            log.info("AuthController.getEnterpriseInfo,old={},new={}", result.getEnterpriseData().getDomain(), cloudDomainEa.get(fsEa));
            result.getEnterpriseData().setDomain(cloudDomainEa.get(fsEa));
        }
        log.info("AuthController.getEnterpriseInfo,result={}",result);
        return result;
    }
}
