package com.facishare.open.qywx.web.service.impl;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.feishu.syncapi.config.GlobalValue;
import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.qywx.accountbind.arg.QyweixinEmpBindArg;
import com.facishare.open.qywx.accountbind.arg.QyweixinEnterpriseBindArg;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.web.db.dao.QyweixinAccountDepartmentBindDao;
import com.facishare.open.qywx.web.db.dao.QyweixinAccountEmployeeBindDao;
import com.facishare.open.qywx.web.db.dao.QyweixinAccountEnterpriseBindDao;
import com.facishare.open.qywx.web.manager.DataPersistorManager;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountDepartmentMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinEmployeeAccountModel;
import com.facishare.open.qywx.web.mq.sender.MQSender;
import com.facishare.open.qywx.web.notify.AutoConfRocketMQProducer;
import com.facishare.open.qywx.accountbind.result.EmployeeAccountMatchResult;
import com.facishare.open.qywx.accountbind.result.ErrorRefer;
import com.facishare.open.qywx.accountbind.result.Result;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountbind.utils.TraceUtil;
import com.facishare.open.qywx.accountinner.model.OaconnectorEventDateChangeProto;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountinner.service.ToolsService;
import com.facishare.open.qywx.accountsync.core.enums.CloudProxyEnum;
import com.facishare.open.qywx.accountsync.core.enums.QYWXBindTypeEnum;
import com.facishare.open.qywx.accountsync.core.enums.SourceTypeEnum;
import com.facishare.open.qywx.accountsync.model.CloudMessageProxyProto;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Options;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by liuwei on 2018/7/18.
 */
@Slf4j
@Service("qyweixinAccountBindService")
public class QyweixinAccountBindServiceImpl implements QyweixinAccountBindService {

    @Autowired
    private QyweixinAccountEmployeeBindDao accountEmployeeBindDao;

    @Autowired
    private QyweixinAccountEnterpriseBindDao accountEnterpriseBindDao;

    @Autowired
    private QyweixinAccountDepartmentBindDao accountDepartmentBindDao;

    @Autowired
    private DataPersistorManager dataPersistorManager;

    @Autowired
    private ToolsService toolsService;

    @Autowired
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;

    @Autowired
    private QyweixinGatewayInnerService qyweixinGatewayInnerService;


    @Resource(name = "enterpriseAccountBindSender")
    private AutoConfRocketMQProducer enterpriseAccountBindSender;

    @Resource(name = "employeeAccountBindSender")
    private AutoConfRocketMQProducer employeeAccountBindSender;

    @Autowired
    private MQSender mqSender;

    @Resource
    private EIEAConverter eieaConverter;

    @PostConstruct
    public void init() {
        String traceId = UUID.randomUUID().toString();
        log.info("QyweixinAccountBindServiceImpl.init,traceId={}",traceId);
        TraceUtil.initTrace(traceId);
    }

    @Override
    public Result<Boolean> bindAccountEmployeeMapping(List<QyweixinAccountEmployeeMapping> arg) {
        if(arg == null || arg.isEmpty()){
            throw new IllegalArgumentException("exist null List");
        }
        arg.stream().forEach(m->{
            if(!m.checkArg()) {
                throw new IllegalArgumentException("exist null parameter");
            }
            String mainAppId = qyweixinGatewayInnerService.getMainAppId(m.getOutEa()).getData();
            m.setAppId(mainAppId);
            if(m.getStatus() == null) {
                m.setStatus(0);
            }
        });


        int bindCount = accountEmployeeBindDao.bindAccountEmployeeMapping(arg);
        if(bindCount > 0) {
            //发送员工绑定成功的mq
            log.info("QyweixinAccountBindServiceImpl.bindAccountEmployeeMapping,arg={}.", arg);
            for(QyweixinAccountEmployeeMapping employeeMapping : arg) {
                //如果状态为100，未开通成功,状态为0是开通成功并绑定
                if(employeeMapping.getStatus() == 0) {
                    List<String> fsList = Splitter.on(".").splitToList(employeeMapping.getFsAccount());
                    String ea = fsList.get(1);
                    String fsUserId = fsList.get(2);
                    Message message = new Message();
                    message.setTags(ConfigCenter.EMPLOYEE_BIND);
                    QyweixinEmpBindArg body = new QyweixinEmpBindArg();
                    body.setEa(ea);
                    body.setCorpId(employeeMapping.getOutEa());
                    body.setFsUserId(fsUserId);
                    body.setQwUserId(employeeMapping.getOutAccount());
                    message.setBody(body.toProto());
                    if(ConfigCenter.TEM_CLOUD_EA.contains(ea)) {
                        CloudMessageProxyProto cloudMessageProxyProto = new CloudMessageProxyProto();
                        cloudMessageProxyProto.setType(CloudProxyEnum.employeeAccountBindSender.name());
                        cloudMessageProxyProto.setCorpId(employeeMapping.getOutEa());
                        cloudMessageProxyProto.setFsEa(ea);
                        cloudMessageProxyProto.setMessage(message);
                        //跨云
                        mqSender.sendCloudProxyMQ(eieaConverter.enterpriseAccountToId(ea), cloudMessageProxyProto);
                    } else {
                        SendResult sendResult = employeeAccountBindSender.send(message, employeeMapping.getOutEa());
                        log.info("QyweixinAccountBindServiceImpl.bindAccountEmployeeMapping,sendResult={},message={}.", sendResult, message);
                    }
                }
            }
        }
        return new Result<>(bindCount > 0);
    }


    @Override
    public Result<Boolean> bindAccountEnterpriseMapping(QyweixinAccountEnterpriseMapping arg) {
        if(!arg.checkArg()) {
            throw new IllegalArgumentException("exist null parameter");
        }

        if(null == arg.getBindType()){
            arg.setBindType(0);
        }

        if(StringUtils.isEmpty(arg.getDepId())) {
            arg.setDepId("1");
        }

        if(StringUtils.isEmpty(arg.getDomain())) {
            arg.setDomain(ConfigCenter.crm_domain);
        }
        arg.setExtend(GlobalValue.enterprise_extend);
        int bindCount = accountEnterpriseBindDao.bindAccountEnterpriseMapping(arg);
        if(bindCount >=1){
            //埋点统计企业绑定数量
            dataPersistorManager.enterpriseAccountBind(arg);
            //如果状态为100，未开通成功,状态为0是开通成功并绑定
            if(arg.getStatus() == 0) {
                //绑定企业成发送mq
                log.info("QyweixinAccountBindServiceImpl.bindAccountEnterpriseMapping,arg={}.", arg);
                Message message = new Message();
                QyweixinEnterpriseBindArg body = new QyweixinEnterpriseBindArg();
                body.setEa(arg.getFsEa());
                body.setCorpId(arg.getOutEa());
                message.setBody(body.toProto());
                if(ConfigCenter.TEM_CLOUD_EA.contains(arg.getFsEa())) {
                    CloudMessageProxyProto cloudMessageProxyProto = new CloudMessageProxyProto();
                    cloudMessageProxyProto.setType(CloudProxyEnum.enterpriseAccountBindSender.name());
                    cloudMessageProxyProto.setCorpId(arg.getOutEa());
                    cloudMessageProxyProto.setFsEa(arg.getFsEa());
                    cloudMessageProxyProto.setMessage(message);
                    //跨云
                    mqSender.sendCloudProxyMQ(eieaConverter.enterpriseAccountToId(arg.getFsEa()), cloudMessageProxyProto);
                } else {
                    //不是这个环境的企业
                    if(ConfigCenter.crm_domain.equals(arg.getDomain())) {
                        SendResult sendResult = enterpriseAccountBindSender.send(message, arg.getOutEa());
                        log.info("QyweixinAccountBindServiceImpl.bindAccountEnterpriseMapping,sendResult={},message={}.", sendResult, message);
                    }
                }

                if(!ConfigCenter.MAIN_ENV) {
                    log.info("QyweixinAccountBindServiceImpl.bindAccountEnterpriseMapping,MAIN_ENV={}.", ConfigCenter.MAIN_ENV);
                    OaconnectorEventDateChangeProto proto = new OaconnectorEventDateChangeProto();
                    proto.setOutEa(arg.getOutEa());
                    proto.setEventType("oaconnector_enterprise_bind");
                    proto.setType("insert");
                    proto.setFsEa(arg.getFsEa());
                    proto.setDomain(ConfigCenter.crm_domain);
                    proto.setContent(new Gson().toJson(arg));
                    mqSender.sendOaconnectorEventDataChangeMQ(ChannelEnum.qywx.name(), proto, String.valueOf(eieaConverter.enterpriseAccountToId(arg.getFsEa())));
                }
            }
            return new Result<>();
        } else {
            return new Result<Boolean>(false).addError(ErrorRefer.BIND_ERROR.getCode());
        }
    }

    /**
     * 绑定企业下的部门,
     *
     * @param arg : 请参考 AccountDepartmentMapping的注释说明
     * @return :true-绑定关系保存成功，false-保存失败。
     **/
    @Override
    public Result<Boolean> bindAccountDepartmentMapping(List<QyweixinAccountDepartmentMapping> arg) {
        arg.stream().forEach(m->{
            if(!m.checkArg()) {
                throw new IllegalArgumentException("exist null parameter");
            }
            String mainAppId = qyweixinGatewayInnerService.getMainAppId(m.getOutEa()).getData();
            m.setAppId(StringUtils.defaultString(m.getAppId(), mainAppId));
        });

        int bindCount = accountDepartmentBindDao.bindAccountEnterpriseMapping(arg);
        return new Result<>(bindCount > 0);
    }

    /**
     *
     * @param source: "qywx"是企业微信
     * @param fsEnterpriseAccount: 纷享企业账号
     * @param outAccountList
     * @return 内部账号。 格式E.xx.yyy
     */
    @Override
    public Result<Map<String, String>> outAccountToFsAccountBatch(String source, String fsEnterpriseAccount, List<String> outAccountList) {
        return outAccountToFsAccountBatch(source, fsEnterpriseAccount, ConfigCenter.crmAppId, outAccountList);
    }

    @Override
    public Result<List<QyweixinAccountEmployeeMapping>> outAccountToFsAccount(String source, String fsEnterpriseAccount, String appId, String outAccount) {
        QyweixinAccountEmployeeMapping result = new QyweixinAccountEmployeeMapping();
        Result<String> outEaResult = fsEaToOutEa(source, fsEnterpriseAccount);
        String outEa = "";
        if(outEaResult.isSuccess()){
            if(StringUtils.isEmpty(outEaResult.getData())){
                return new Result().addError("未查到企业绑定信息");
            }
            outEa = outEaResult.getData();
        }
        List<QyweixinAccountEmployeeMapping> accountEmployeeMappingList =
                accountEmployeeBindDao.queryMappingFromOutAccountBatch(source, appId,  Lists.newArrayList(outAccount), outEa);
        if(CollectionUtils.isEmpty(accountEmployeeMappingList)) {
            com.facishare.open.qywx.accountsync.result.Result<String> userId2OpenUserId = toolsService.userId2OpenUserId2(outEa,appId,outAccount);
            if(StringUtils.isNotEmpty(userId2OpenUserId.getData())) {
                outAccount = userId2OpenUserId.getData();
                accountEmployeeMappingList =
                        accountEmployeeBindDao.queryMappingFromOutAccountBatch(source, appId,  Lists.newArrayList(outAccount), outEa);
            }
        }
        return new Result<>(accountEmployeeMappingList);
    }

    @Override
    public Result<List<QyweixinAccountEmployeeMapping>> employeeToOutAccount(String source, String outEa, String appId, List<String> outAccounts) {
        List<QyweixinAccountEmployeeMapping> accountEmployeeMappingList =
                accountEmployeeBindDao.queryMappingFromOutAccountBatch(source, appId,  outAccounts, outEa);
        return new Result<>(accountEmployeeMappingList);
    }

    @Override
    public Result<Map<String, String>> outAccountToFsAccountBatch(String source, String fsEnterpriseAccount,
                                                                  String appId,List<String> outAccountList) {
        Result<String> outEaResult = fsEaToOutEa(source, fsEnterpriseAccount);
        String outEa = "";
        if(outEaResult.isSuccess()){
            if(StringUtils.isEmpty(outEaResult.getData())){
                return new Result<Map<String, String>>().addError("未查到企业绑定信息");
            }

            outEa = outEaResult.getData();
        }
        List<QyweixinAccountEmployeeMapping> accountEmployeeMappingList = accountEmployeeBindDao.queryMappingFromOutAccountBatch(source, null, outAccountList, outEa);
        if(null != accountEmployeeMappingList) {
            Map<String, String> outAccountToFsAccountMap = accountEmployeeMappingList.stream()
                    .collect(Collectors.toMap(QyweixinAccountEmployeeMapping::getOutAccount, QyweixinAccountEmployeeMapping::getFsAccount,
                            (key1, key2) -> key2));
            return new Result<>(outAccountToFsAccountMap);
        }else {
            return new Result<>(Maps.newHashMap());
        }
    }

    @Override
    public Result<List<QyweixinAccountEmployeeMapping>> outAccountToFsAccountBatch(String source, String fsEnterpriseAccount,
                                                                                   String appId, List<String> outAccountList, int status) {
        Result<String> outEaResult = fsEaToOutEa(source, fsEnterpriseAccount);
        String outEa = "";
        if(outEaResult.isSuccess()){
            if(StringUtils.isEmpty(outEaResult.getData())){
                return new Result<List<QyweixinAccountEmployeeMapping>>().addError("未查到企业绑定信息");
            }

            outEa = outEaResult.getData();
        }
        List<QyweixinAccountEmployeeMapping> accountEmployeeMappingList = accountEmployeeBindDao.queryMappingFromOutAccountBatch(source, appId, outAccountList, outEa);
        if (status == -1) {
            return new Result<>(accountEmployeeMappingList);
        }
        List<QyweixinAccountEmployeeMapping> result = accountEmployeeMappingList.stream().filter(mapping -> mapping.getStatus() == status).collect(Collectors.toList());
        return new Result<>(result);
    }

    /**
     * 内部账号换外部账号。
     *
     * @param source        : "qywx"是企业微信
     * @param fsAccountList
     * @return : 外部账号。  企业微信账号
     */
    @Deprecated
    @Override
    public Result<Map<String, String>> fsAccountToOutAccountBatch(String source, List<String> fsAccountList) {
        return fsAccountToOutAccountBatch(source, ConfigCenter.crmAppId, fsAccountList);
    }

    @Deprecated
    @Override
    public Result<Map<String, String>> fsAccountToOutAccountBatch(String source, String appId, List<String> fsAccountList) {

        List<QyweixinAccountEmployeeMapping> accountEmployeeMappingList =
                accountEmployeeBindDao.queryMappingFromFsAccountBatch(source, appId, 0, fsAccountList, null);
        if (CollectionUtils.isNotEmpty(accountEmployeeMappingList)) {
            Map<String, String> fsAccountToOutAccountMap = accountEmployeeMappingList.stream()
                    .collect(Collectors.toMap(QyweixinAccountEmployeeMapping::getFsAccount,
                            QyweixinAccountEmployeeMapping::getOutAccount, (oldValue, newValue) -> newValue));
            return new Result<>(fsAccountToOutAccountMap);
        } else {
            return new Result<>(Maps.newHashMap());
        }
    }

    /**
     * 支持一对多场景，
     * @param source
     * @param appId
     * @param fsAccountList
     * @param outEa
     * @return
     */
    @Override
    public Result<List<EmployeeAccountMatchResult>> fsAccountToOutAccountBatch2(String source, String appId, List<String> fsAccountList, String outEa) {
        List<QyweixinAccountEmployeeMapping> mappingList = accountEmployeeBindDao.queryMappingFromFsAccountBatch(source,
                appId,
                0,
                fsAccountList, outEa);
        List<EmployeeAccountMatchResult> matchResultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(mappingList)) {
            for (QyweixinAccountEmployeeMapping mapping : mappingList) {
                matchResultList.add(new EmployeeAccountMatchResult(mapping.getFsAccount(), mapping.getOutAccount(), mapping.getOutEa(),mapping.getAppId()));
            }
        }
        return new Result<>(matchResultList);
    }

    @Deprecated
    @Override
    public Result<Map<String, String>> fsAccountToOutAccount(String source, List<String> fsAccountList) {
        List<QyweixinAccountEmployeeMapping> accountEmployeeMappingList =
                accountEmployeeBindDao.queryMappingFromFsAccount(source, 0, fsAccountList, null);
        if (CollectionUtils.isNotEmpty(accountEmployeeMappingList)) {
            Map<String, String> fsAccountToOutAccountMap = accountEmployeeMappingList.stream()
                    .collect(Collectors.toMap(QyweixinAccountEmployeeMapping::getFsAccount,
                            QyweixinAccountEmployeeMapping::getOutAccount, (oldValue, newValue) -> newValue));
            return new Result<>(fsAccountToOutAccountMap);
        } else {
            return new Result<>();
        }
    }

    @Override
    public Result<List<EmployeeAccountMatchResult>> fsAccountToOutAccount2(String source, List<String> fsAccountList, String outEa) {
        List<QyweixinAccountEmployeeMapping> mappingList = accountEmployeeBindDao.queryMappingFromFsAccount(source,
                0,
                fsAccountList,
                outEa);
        List<EmployeeAccountMatchResult> matchResultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(mappingList)) {
            for (QyweixinAccountEmployeeMapping mapping : mappingList) {
                matchResultList.add(new EmployeeAccountMatchResult(mapping.getFsAccount(), mapping.getOutAccount(), mapping.getOutEa(),mapping.getAppId()));
            }
        }
        return new Result<>(matchResultList);
    }

    @Deprecated
    @Override
    public Result<Map<String, String>> fsAccountToOutAccount(String source, List<String> fsAccountList, Integer status) {

        List<QyweixinAccountEmployeeMapping> accountEmployeeMappingList =
                accountEmployeeBindDao.queryMappingFromFsAccount(source, status, fsAccountList, null);
        if (CollectionUtils.isNotEmpty(accountEmployeeMappingList)) {
            Map<String, String> fsAccountToOutAccountMap = accountEmployeeMappingList.stream()
                    .collect(Collectors.toMap(QyweixinAccountEmployeeMapping::getFsAccount,
                            QyweixinAccountEmployeeMapping::getOutAccount, (oldValue, newValue) -> newValue));
            return new Result<>(fsAccountToOutAccountMap);
        } else {
            return new Result<>(Maps.newHashMap());
        }
    }

    @Override
    public Result<List<EmployeeAccountMatchResult>> fsAccountToOutAccount2(String source, List<String> fsAccountList, Integer status, String outEa) {
        List<QyweixinAccountEmployeeMapping> mappingList = accountEmployeeBindDao.queryMappingFromFsAccount(source,
                status,
                fsAccountList,
                outEa);
        List<EmployeeAccountMatchResult> matchResultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(mappingList)) {
            for (QyweixinAccountEmployeeMapping mapping : mappingList) {
                matchResultList.add(new EmployeeAccountMatchResult(mapping.getFsAccount(), mapping.getOutAccount(), mapping.getOutEa(), mapping.getAppId()));
            }
        }
        return new Result<>(matchResultList);
    }

    @Deprecated
    @Override
    public Result<Map<String, String>> fsAccountToOutAccountBatchByIsv(String source, String appId, List<String> fsAccountList) {

        List<QyweixinAccountEmployeeMapping> accountEmployeeMappingList =
                accountEmployeeBindDao.queryMappingFromFsAccountBatch(source, appId, 0, fsAccountList, null);
        if (CollectionUtils.isNotEmpty(accountEmployeeMappingList)) {
            for (int i = 0; i < accountEmployeeMappingList.size(); i++) {
                if (StringUtils.isEmpty(accountEmployeeMappingList.get(i).getIsvAccount())) {
                    accountEmployeeMappingList.get(i).setIsvAccount(accountEmployeeMappingList.get(i).getOutAccount());
                }
            }
            Map<String, String> fsAccountToOutAccountMap = accountEmployeeMappingList.stream()
                    .collect(Collectors.toMap(QyweixinAccountEmployeeMapping::getFsAccount,
                            QyweixinAccountEmployeeMapping::getIsvAccount, (oldValue, newValue) -> newValue));

            return new Result<>(fsAccountToOutAccountMap);
        } else {
            return new Result<>(Maps.newHashMap());
        }
    }

    @Override
    public Result<List<EmployeeAccountMatchResult>> fsAccountToOutAccountBatchByIsv2(String source, String appId, List<String> fsAccountList, String outEa) {
        List<QyweixinAccountEmployeeMapping> mappingList = accountEmployeeBindDao.queryMappingFromFsAccountBatch(source,
                appId,
                0,
                fsAccountList,
                outEa);
        List<EmployeeAccountMatchResult> matchResultList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(mappingList)) {
            for (int i = 0; i < mappingList.size(); i++) {
                if(StringUtils.isEmpty(mappingList.get(i).getIsvAccount())) {
                    mappingList.get(i).setIsvAccount(mappingList.get(i).getOutAccount());
                }
            }
            for (QyweixinAccountEmployeeMapping mapping : mappingList) {
                matchResultList.add(new EmployeeAccountMatchResult(mapping.getFsAccount(), mapping.getIsvAccount(), mapping.getOutEa(),mapping.getAppId()));
            }
        }

        return new Result<>(matchResultList);
    }

    @Deprecated
    @Override
    public Result<Map<String, String>> fsAccountToOutAccountByIsv(String source, List<String> fsAccountList) {

        List<QyweixinAccountEmployeeMapping> accountEmployeeMappingList =
                accountEmployeeBindDao.queryMappingFromFsAccount(source, 0, fsAccountList, null);
        if (CollectionUtils.isNotEmpty(accountEmployeeMappingList)) {
            Map<String, String> fsAccountToOutAccountMap = accountEmployeeMappingList.stream()
                    .collect(Collectors.toMap(QyweixinAccountEmployeeMapping::getFsAccount,
                            QyweixinAccountEmployeeMapping::getIsvAccount, (oldValue, newValue) -> newValue));
            return new Result<>(fsAccountToOutAccountMap);
        } else {
            return new Result<>(Maps.newHashMap());
        }
    }

    @Override
    public Result<List<EmployeeAccountMatchResult>> fsAccountToOutAccountByIsv2(String source, List<String> fsAccountList, String outEa) {
        List<QyweixinAccountEmployeeMapping> mappingList = accountEmployeeBindDao.queryMappingFromFsAccount(source,
                0,
                fsAccountList,
                outEa);
        List<EmployeeAccountMatchResult> matchResultList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(mappingList)) {
            for (int i = 0; i < mappingList.size(); i++) {
                if(StringUtils.isEmpty(mappingList.get(i).getIsvAccount())) {
                    mappingList.get(i).setIsvAccount(mappingList.get(i).getOutAccount());
                }
            }
            for (QyweixinAccountEmployeeMapping mapping : mappingList) {
                matchResultList.add(new EmployeeAccountMatchResult(mapping.getFsAccount(), mapping.getIsvAccount(), mapping.getOutEa(), mapping.getAppId()));
            }
        }

        return new Result<>(matchResultList);
    }

    @Deprecated
    @Override
    public Result<QyweixinAccountEmployeeMapping> getQywxEmployeeMapping(String fsEa, String fsUserId) {
        return getQywxEmployeeMapping2(fsEa, fsUserId, null);
    }

    @Override
    public Result<QyweixinAccountEmployeeMapping> getQywxEmployeeMapping2(String fsEa, String fsUserId, String outEa) {
        return new Result<>(accountEmployeeBindDao.getQywxEmployeeMapping("E."+fsEa+"."+fsUserId, outEa));
    }

    @Override
    public Result<QyweixinAccountEmployeeMapping> getQywxEmployeeMapping3(String fsEa, String fsUserId, String outEa, String outUserId) {
        return new Result<>(accountEmployeeBindDao.getQywxEmployeeMapping2("E."+fsEa+"."+fsUserId, outEa, outUserId));
    }

    @Override
    public Result<QyweixinAccountDepartmentMapping> getQywxDepartmentMapping(String fsEa, String fsDepId, String outEa, String outDepId) {
        return new Result<>(accountDepartmentBindDao.getQywxDepartmentMapping(fsEa, fsDepId, outEa, outDepId, -1));
    }

    @Override
    public Result<QyweixinAccountEmployeeMapping> getFsEmployeeMapping(String fsEa, String qywxUserId) {
        return new Result<>(accountEmployeeBindDao.getFsEmployeeMapping(fsEa,qywxUserId));
    }

    @Override
    public Result<List<QyweixinAccountEmployeeMapping>> getFsEmployeeMapping2(String outEa, String qywxUserId) {
        return new Result<>(accountEmployeeBindDao.getFsEmployeeMapping2(outEa,qywxUserId));
    }

    @Deprecated
    @Override
    public Result<List<QyweixinAccountEmployeeMapping>> fsAccountToOutAccountBatchV2(String source, String appId, int status, List<String> fsAccountList) {
        return fsAccountToOutAccountBatchV21(source,appId,status,fsAccountList,null);
    }

    @Override
    public Result<List<QyweixinAccountEmployeeMapping>> fsAccountToOutAccountBatchV21(String source, String appId, int status, List<String> fsAccountList, String outEa) {
        List<QyweixinAccountEmployeeMapping> result = accountEmployeeBindDao.queryMappingFromFsAccountBatch(source, appId, status, fsAccountList, outEa);
        return new Result<>(result);
    }

    /**
     * 修改员工状态
     * @param isDelete true-正常 false-停用
     * @return
     */
    @Override
    public Result<Void> deleteOrResumeEmployee(String source, String ea, String appId, List<String> outAccounts, boolean isDelete) {
        int status = isDelete ? 1 : 0;
        int i = accountEmployeeBindDao.changeEmployeeStatus(source, ea, appId, outAccounts, status);
        log.debug("deleteOrResumeEmployee success. ea:{}, appId:{}, outAccounts:{}, status:{}", ea, appId, outAccounts, status);
        return new Result<>();
    }

    @Override
    public Result<String> outEaToFsEa(String source, String outEa) {
        return outEaToFsEa(source, outEa,"1");
    }

    /**
     * 企业的外部账号转内部账号
     *
     * @param source : "qywx"是企业微信
     * @param outEa  :企业在外部平台上的账号
     * @return : 企业在纷享的账号
     */
    @Override
    public Result<String> outEaToFsEa(String source, String outEa, String depId) {
        log.info("QyweixinAccountBindServiceImpl.outEaToFsEa,outEa={}",outEa);
        if(StringUtils.isEmpty(depId)) {
            depId = "1";
        }
        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = accountEnterpriseBindDao.queryEaMappingFromOutEa(source, outEa,depId,0, ConfigCenter.crm_domain);
        log.info("QyweixinAccountBindServiceImpl.outEaToFsEa,enterpriseMappingList={}",enterpriseMappingList);
        if (CollectionUtils.isNotEmpty(enterpriseMappingList)) {
            return new Result<>(enterpriseMappingList.get(0).getFsEa());
        } else {
            return new Result<>();
        }
    }

    @Override
    public Result<List<QyweixinAccountEnterpriseMapping>> selectEnterpriseBind(String source, String outEa) {
        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = accountEnterpriseBindDao.queryEaMappingFromOutEa(source, outEa,null,-1, ConfigCenter.crm_domain);
        log.info("QyweixinAccountBindServiceImpl.selectEnterpriseBind source:{}, outEa:{}, enterpriseMappingList:{}", source, outEa, enterpriseMappingList);
        if(CollectionUtils.isEmpty(enterpriseMappingList)) {
            return new Result<>();
        }
        return new Result<>(enterpriseMappingList);
    }

    @Override
    public Result<List<QyweixinAccountEnterpriseMapping>> selectAllEnterpriseBind(String source, String outEa) {
        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = accountEnterpriseBindDao.queryEaMappingFromOutEa(source, outEa,null,-1, null);
        log.info("QyweixinAccountBindServiceImpl.selectAllEnterpriseBind source:{}, outEa:{}, enterpriseMappingList:{}", source, outEa, enterpriseMappingList);
        if(CollectionUtils.isEmpty(enterpriseMappingList)) {
            return new Result<>();
        }
        return new Result<>(enterpriseMappingList);
    }

    /**
     * 企业的外部账号转内部账号
     *
     * @param source : "yunzhijia"是企业微信
     * @param fsEa   :企业在纷享上的账号
     * @return : 企业在外部平台上的账号
     */
    @Override
    public Result<String> fsEaToOutEa(String source, String fsEa) {

        QyweixinAccountEnterpriseMapping mapping = accountEnterpriseBindDao.queryEaMappingFromFsEa(source, fsEa, null, ConfigCenter.crm_domain);
        if(null != mapping) {
            return new Result<>(mapping.getOutEa());
        }else {
            return new Result<>();
        }
    }

    @Override
    public Result<QyweixinAccountEnterpriseMapping> fsEaToOutEaResult(String source, String fsEa) {
        return fsEaToOutEaResult2(source,fsEa,null);
    }

    @Override
    public Result<List<QyweixinAccountEnterpriseMapping>> fsEaToOutEaResultList(String source, String fsEa) {
        List<QyweixinAccountEnterpriseMapping> mappingList = accountEnterpriseBindDao.queryEaMappingListFromFsEa(source,fsEa, ConfigCenter.crm_domain);
        return new Result<>(mappingList);
    }

    @Override
    public Result<QyweixinAccountEnterpriseMapping> fsEaToOutEaResult2(String source, String fsEa, String outEa) {
        QyweixinAccountEnterpriseMapping mapping = accountEnterpriseBindDao.queryEaMappingFromFsEa(source, fsEa, outEa, ConfigCenter.crm_domain);
        return new Result<>(mapping);
    }

    @Override
    public Result<QyweixinAccountEnterpriseMapping> fsEaToOutEaAllResult2(String source, String fsEa, String outEa) {
        QyweixinAccountEnterpriseMapping mapping = accountEnterpriseBindDao.queryEaMappingFromFsEa(source, fsEa, outEa, null);
        return new Result<>(mapping);
    }

    /**
     * 默认使用CRM应用查询
     */
    @Override
    public Result<List<QyweixinAccountDepartmentMapping>> queryDepartmentBindByFsDepartment(String source, String fsEa, List<Integer> fsDepartmentIdList) {
        return queryDepartmentBindByFsDepartment(source, fsEa, ConfigCenter.crmAppId, fsDepartmentIdList);
    }

    @Override
    public Result<List<QyweixinAccountDepartmentMapping>> queryDepartmentBindByFsDepartment(String source,
                                                                                            String fsEa,
                                                                                            String appId,
                                                                                            List<Integer> fsDepartmentIdList) {
        List<QyweixinAccountDepartmentMapping> result = accountDepartmentBindDao.queryDepartmentBindByFsDepartment(source, fsEa, appId, fsDepartmentIdList);
        if(null != result && !result.isEmpty()) {
            return new Result<>(result);
        } else {
            return new Result<>(Lists.newArrayList());
        }
    }

    /**
     * 修改部门状态
     * @param isDelete true-正常 false-停用
     * @return
     */
    @Override
    public Result<Void> deleteOrResumeDepartment(String source, String ea, String appId,
                                                 List<String> outDepartmentIds, boolean isDelete) {
        int status = isDelete ? 1 : 0;
        int i = accountDepartmentBindDao.changeDepartmentStatus(source, ea, appId, outDepartmentIds, status);
        log.debug("deleteOrResumeDepartment success. ea:{}, appId:{}, outDepartmentIds:{}, status:{}", ea, appId, outDepartmentIds, status);
        return new Result<>();
    }

    @Override
    public Result<List<QyweixinAccountDepartmentMapping>> queryDepartmentBindByOutDepartment(String source, String outEa, List<String> outDepartmentIdList) {
        return queryDepartmentBindByOutDepartment(source, outEa, ConfigCenter.crmAppId, 0, outDepartmentIdList);
    }

    @Override
    public Result<List<QyweixinAccountDepartmentMapping>> queryDepartmentBindByOutDepartment(String source,
                                                                                             String outEa,
                                                                                             String appId,
                                                                                             List<String> outDepartmentIdList){
        return queryDepartmentBindByOutDepartment(source, outEa, appId, 0, outDepartmentIdList);
    }

    @Override
    public Result<List<QyweixinAccountDepartmentMapping>> queryDepartmentBindByOutDepartment(String source,
                                                                                             String outEa,
                                                                                             String appId,
                                                                                             int status,
                                                                                             List<String> outDepartmentIdList) {
        List<QyweixinAccountDepartmentMapping> result =
                accountDepartmentBindDao.queryDepartmentBindByOutDepartment(source, outEa, appId, status,
                        outDepartmentIdList);
        if(null != result && ! result.isEmpty() && CollectionUtils.isNotEmpty(outDepartmentIdList)) {
            //主属部门放在第一位
            List<QyweixinAccountDepartmentMapping> DepartmentResult = this.swapDepartment(result, outDepartmentIdList.get(0));
            return new Result<>(DepartmentResult);
        } else {
            return new Result<>(result);
        }
    }

    private List<QyweixinAccountDepartmentMapping> swapDepartment(List<QyweixinAccountDepartmentMapping> result, String mainDepartment) {
        for (int i = 0; i < result.size(); i++) {
            if(result.get(0).getOutDepartmentId().equals(mainDepartment)) {
                return result;
            }
            if(result.get(i).getOutDepartmentId().equals(mainDepartment)) {
                Collections.swap(result, 0, i);
                break;
            }
        }
        return result;
    }

    @Override
    public Result<List<String>> queryFsEaBindBatch(int pageNum, int pageSize) {
        pageNum = pageNum <=0 ? 1:pageNum;
        pageSize = pageSize <=0 ? 50: pageSize;
        ArrayList<HashMap<String, String>> queryFsEaBindLists = accountEnterpriseBindDao.queryFsEaBindBatch((pageNum - 1) * pageSize, pageSize, ConfigCenter.crm_domain);
        if(queryFsEaBindLists.isEmpty()){
            return new Result<>(Lists.newArrayList());
        }
        return new Result<>(queryFsEaBindLists.stream().map(v -> v.get("fs_ea")).collect(Collectors.toList()));
    }

    /**
     * 企业微信用户修改UserId后根据旧的UserId查询企业微信的绑定关系
     *
     * @param
     * @return 企业微信的员工绑定关系
     **/
    @Override
    public Result<List<QyweixinAccountEmployeeMapping>> queryFsAccountBindByOldOutAccount(String source,List outAccountList, String outEa) {
        return queryFsAccountBindByOldOutAccount(source, outAccountList, ConfigCenter.crmAppId, outEa);
    }

    @Override
    public Result<List<QyweixinAccountEmployeeMapping>> queryFsAccountBindByOldOutAccount(String source,
                                                                                          List outAccountList,
                                                                                          String appId,
                                                                                          String outEa) {
        List<QyweixinAccountEmployeeMapping> result = accountEmployeeBindDao.queryMappingFromOutAccountBatch(source, appId, outAccountList, outEa);
        log.info("trace queryFsAccountBindByOldOutAccount result" + result);
        if(null != result && ! result.isEmpty()) {
            return new Result<>(result);
        } else {
            return new Result<>(Lists.newArrayList());
        }
    }

    /**
     * 更新企业微信的userId绑定关系
     *
     * @param newAccount 新userId
     * @param oldAccount 旧userId
     * @param outEa 企业微信ID
     * @return
     **/
    @Override
    public int updateByNewOutAccount(String newAccount,String oldAccount, String outEa) {
        return updateByNewOutAccount(newAccount, oldAccount, ConfigCenter.crmAppId, outEa);
    }

    @Override
    public int updateByNewOutAccount(String newAccount,String oldAccount,String appId, String outEa) {
        List<QyweixinAccountEmployeeMapping> mappings = accountEmployeeBindDao.queryMappingFromOutAccountBatch(
                SourceTypeEnum.QYWX.getSourceType(), appId, Lists.newArrayList(newAccount), outEa);
        log.info("updateByNewOutAccount start. newAccount:{}, oldAccount:{}, " +
                "appId:{}, outEa:{}, mappings:{}", newAccount, oldAccount, appId, outEa, mappings);
        if (!mappings.isEmpty() && mappings.get(0).getOutAccount().equals(newAccount)){
            log.warn("updateByNewOutAccount failed. duplicate new account, newAccount:{}, oldAccount:{}, appId:{}, " +
                    "outEa:{}, mappings:{}", newAccount, oldAccount, appId, outEa, mappings);
            return 1;
        }
        return accountEmployeeBindDao.updateByNewOutAccount(newAccount, oldAccount, appId, outEa);
    }

    @Override
    public int updateAccountByIsv(String newAccount,String oldAccount,String appId, String outEa) {
        return accountEmployeeBindDao.updateAccountByIsv(newAccount, oldAccount, appId, outEa);
    }

    @Override
    public void resumeStoppedEmployees(String source, String fsEa,String appId) {
        List<QyweixinAccountEmployeeMapping> entityList = accountEmployeeBindDao.queryAccountBindByFsEa("E."+fsEa+".", appId, null);
        log.info("QyweixinAccountBindServiceImpl.resumeStoppedEmployees,entityList={}",entityList);
        if (entityList != null && entityList.size() > 0) {
            List<String> outAccountList = new ArrayList<>();
            for (QyweixinAccountEmployeeMapping item : entityList) {
                if(item.getStatus()==0) continue;
                outAccountList.add(item.getOutAccount());
            }
            if(outAccountList.size()>0) {
                accountEmployeeBindDao.changeEmployeeStatus(source,fsEa,appId,outAccountList,0);
            }
            log.info("QyweixinAccountBindServiceImpl.resumeStoppedEmployees,resume stopped employee finished");
        }
    }

    @Override
    public void resumeStoppedDepartments(String source, String fsEa,String appId) {
        QyweixinAccountDepartmentMapping entity = new QyweixinAccountDepartmentMapping();
        entity.setSource(source);
        entity.setFsEa(fsEa);
        entity.setAppId(appId);
        entity.setStatus(1);
        List<QyweixinAccountDepartmentMapping> entityList = accountDepartmentBindDao.findByEntity(entity);
        log.info("QyweixinAccountBindServiceImpl.resumeStoppedDepartments,entityList={}",entityList);
        if (entityList != null && entityList.size() > 0) {
            List<String> outDepartmentIdList = new ArrayList<>();
            for (QyweixinAccountDepartmentMapping item : entityList) {
                outDepartmentIdList.add(item.getOutDepartmentId());
            }
            accountDepartmentBindDao.changeDepartmentStatus(source,fsEa,appId,outDepartmentIdList,0);
            log.info("QyweixinAccountBindServiceImpl.resumeStoppedDepartments,resume stopped department finished");
        }
    }

    @Override
    public Result<Integer> bindOutAccountEnterpriseMapping(QyweixinAccountEnterpriseMapping arg) {
        Integer enterpriseMapping = accountEnterpriseBindDao.updateEnterpriseMapping(arg);
        return new Result<>(enterpriseMapping);
    }

    /**
     * 查询corpId企业下的所有员工
     * @param corpId
     * @return
     */
    @Override
    public Result<List<QyweixinAccountEmployeeMapping>> selectAll(String corpId) {
        QyweixinAccountEmployeeMapping employeeMapping = new QyweixinAccountEmployeeMapping();
        employeeMapping.setOutEa(corpId);
        List<QyweixinAccountEmployeeMapping> mappingList = accountEmployeeBindDao.findByEntity(employeeMapping);
        return new Result<>(mappingList);
    }

    /**
     * 批量更新员工信息
     * @param employeeMappingList
     * @return
     */
    @Transactional(isolation = Isolation.SERIALIZABLE)
    @Override
    public Result<Integer> batchUpdateEmployeeMapping(List<QyweixinAccountEmployeeMapping> employeeMappingList) {
        if(CollectionUtils.isEmpty(employeeMappingList)) return new Result<>(0);
        int totalCount = 0;
        for(QyweixinAccountEmployeeMapping mapping : employeeMappingList) {
            int count = accountEmployeeBindDao.update(mapping);
            log.info("QyweixinAccountBindServiceImpl.batchUpdateEmployeeMapping,count={}",count);
            if(count>0) {
                totalCount+=count;
            }
        }
        return new Result<>(totalCount);
    }

    /**
     * 更新企业信息
     * @param enterpriseMapping
     * @return
     */
    @Transactional(isolation = Isolation.SERIALIZABLE)
    @Options(useGeneratedKeys = false)
    @Override
    public Result<Integer> updateQyweixinAccountEnterpriseMapping(QyweixinAccountEnterpriseMapping enterpriseMapping) {
        if(ObjectUtils.isEmpty(enterpriseMapping)) return new Result<>(0);
        int totalCount = 0;
        int count = accountEnterpriseBindDao.updateQyweixinAccountEnterpriseMapping(enterpriseMapping);
        log.info("QyweixinAccountBindServiceImpl.batchUpdateEnterpriseMapping,count={}",count);
        if(count>0) {
            totalCount+=count;
        }
        return new Result<>(totalCount);
    }

    /**
     * 批量更新部门信息
     * @return
     */
    @Transactional(isolation = Isolation.SERIALIZABLE)
    @Override
    public Result<Integer> batchUpdateDepartmentBind(String outEa, String openOutEa) {
        int count = accountDepartmentBindDao.BatchUpdateDepartmentBind(outEa, openOutEa, "qywx");
        log.info("QyweixinAccountBindServiceImpl.batchUpdateDepartmentBind,outEa={},count={}",outEa, count);
        return new Result<>(count);
    }

    @Override
    public Result<Boolean> isManualBinding(String fsEa, String eid) {
        boolean isBinding = false;

        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(eid);
        if(CollectionUtils.isEmpty(enterpriseMappingList)) {
            return new Result<>(isBinding);
        }
        QyweixinAccountEnterpriseMapping enterpriseMapping = enterpriseMappingList.get(0);
        Integer type = enterpriseMapping.getBindType();
        if(StringUtils.isEmpty(fsEa)) {
            fsEa = enterpriseMapping.getFsEa();
        }
        log.debug("isManualBinding start. fsEa:{}, eid:{}, enterpriseMapping:{}", fsEa, eid, enterpriseMapping);
        if (QYWXBindTypeEnum.OLD_CORP_BIND.getCode().equals(type) && !ConfigCenter.EMPLOYEE_SYNC_EA.contains(fsEa)) {
            isBinding = true;
        }
        return new Result<>(isBinding);
    }

    @Override
    public Result<List<QyweixinAccountEnterpriseMapping>> queryEnterpriseMappingByBindType(Integer bindType) {
        return new Result<>(accountEnterpriseBindDao.queryEnterpriseMappingByBindType(bindType, ConfigCenter.crm_domain));
    }

    @Deprecated
    @Override
    public Result<List<QyweixinAccountEmployeeMapping>> batchGetEmployeeMapping(String fsEa, String appId) {
        return batchGetEmployeeMapping2(fsEa, appId, null);
    }

    @Override
    public Result<List<QyweixinAccountEmployeeMapping>> batchGetEmployeeMapping2(String fsEa, String appId, String outEa) {
        if(StringUtils.isEmpty(appId)) {
            String mainAppId = qyweixinGatewayInnerService.getMainAppId(outEa).getData();
            appId = mainAppId;
        }
        List<QyweixinAccountEmployeeMapping> entityList = accountEmployeeBindDao.queryAccountBindByFsEa("E."+fsEa+".", appId, outEa);
        return new Result<>(entityList);
    }

    @Override
    public Result<Integer> updateEnterpriseBindStatus(String fsEa, int status) {
        //查询之前的状态，如果是100，证明是创建企业后的更新状态，需要发送mq
        QyweixinAccountEnterpriseMapping enterpriseMapping = accountEnterpriseBindDao.queryEaMappingFromFsEa("qywx", fsEa, null, ConfigCenter.crm_domain);
        log.info("QyweixinAccountBindServiceImpl.updateEnterpriseBindStatus,enterpriseMapping={}", enterpriseMapping);
        String outEa = enterpriseMapping!=null ? enterpriseMapping.getOutEa() : null;
        Integer count = accountEnterpriseBindDao.updateEnterpriseBindStatus(fsEa, status, outEa);
        if(enterpriseMapping.getStatus() == 100) {
            //绑定企业成发送mq
            log.info("QyweixinAccountBindServiceImpl.updateEnterpriseBindStatus,fsEa={}", fsEa);
            Message message = new Message();
            QyweixinEnterpriseBindArg body = new QyweixinEnterpriseBindArg();
            body.setEa(fsEa);
            body.setCorpId(enterpriseMapping.getOutEa());
            message.setBody(body.toProto());
            if(ConfigCenter.TEM_CLOUD_EA.contains(fsEa)) {
                CloudMessageProxyProto cloudMessageProxyProto = new CloudMessageProxyProto();
                cloudMessageProxyProto.setType(CloudProxyEnum.enterpriseAccountBindSender.name());
                cloudMessageProxyProto.setCorpId(enterpriseMapping.getOutEa());
                cloudMessageProxyProto.setFsEa(fsEa);
                cloudMessageProxyProto.setMessage(message);
                //跨云
                mqSender.sendCloudProxyMQ(eieaConverter.enterpriseAccountToId(fsEa), cloudMessageProxyProto);
            } else {
                SendResult sendResult = enterpriseAccountBindSender.send(message, enterpriseMapping.getOutEa());
                log.info("QyweixinAccountBindServiceImpl.updateEnterpriseBindStatus,sendResult={},message={}.", sendResult, message);
            }
        }
        return new Result<>(count);
    }

    @Deprecated
    @Override
    public Result<Integer> batchUpdateFsDepBindStatus(String fsEa, List<String> fsDepIdList, int status,String appId) {
        return batchUpdateFsDepBindStatus2(fsEa, fsDepIdList, status, appId, null);
    }

    @Override
    public Result<Integer> batchUpdateFsDepBindStatus2(String fsEa, List<String> fsDepIdList, int status, String appId, String outEa) {
        return new Result<>(accountDepartmentBindDao.batchUpdateFsDepBindStatus(fsEa,fsDepIdList,status,appId,outEa));
    }

    @Override
    public Result<Integer> batchUpdateOutDepBindStatus(String fsEa, List<String> outDepIdList, int status, String appId) {
        return new Result<>(accountDepartmentBindDao.changeDepartmentStatus(SourceTypeEnum.QYWX.getSourceType(),fsEa,appId,outDepIdList,status));
    }

    @Override
    public Result<Integer> updateEmployeeBindStatus(String fsAccount, int status) {
        //查询之前的状态，如果是100，证明是创建企业后的更新状态，需要发送mq
        List<QyweixinAccountEmployeeMapping> mappings = accountEmployeeBindDao.queryMappingFromFsAccount("qywx", 100, Lists.newArrayList(fsAccount),null);
        String outEa = CollectionUtils.isNotEmpty(mappings) ? mappings.get(0).getOutEa() : null;
        Integer count = accountEmployeeBindDao.updateEmployeeBindStatus(fsAccount, status, outEa);
        if(CollectionUtils.isNotEmpty(mappings)) {
            List<String> fsList = Splitter.on(".").splitToList(fsAccount);
            String ea = fsList.get(1);
            String fsUserId = fsList.get(2);
            Message message = new Message();
            message.setTags(ConfigCenter.EMPLOYEE_BIND);
            QyweixinEmpBindArg body = new QyweixinEmpBindArg();
            body.setEa(ea);
            body.setCorpId(mappings.get(0).getOutEa());
            body.setFsUserId(fsUserId);
            body.setQwUserId(mappings.get(0).getOutAccount());
            message.setBody(body.toProto());
            if(ConfigCenter.TEM_CLOUD_EA.contains(ea)) {
                CloudMessageProxyProto cloudMessageProxyProto = new CloudMessageProxyProto();
                cloudMessageProxyProto.setType(CloudProxyEnum.employeeAccountBindSender.name());
                cloudMessageProxyProto.setCorpId(mappings.get(0).getOutEa());
                cloudMessageProxyProto.setFsEa(ea);
                cloudMessageProxyProto.setMessage(message);
                //跨云
                mqSender.sendCloudProxyMQ(eieaConverter.enterpriseAccountToId(ea), cloudMessageProxyProto);
            } else {
                SendResult sendResult = employeeAccountBindSender.send(message, mappings.get(0).getOutEa());
                log.info("QyweixinAccountBindServiceImpl.updateEmployeeBindStatus,sendResult={},message={}.", sendResult, message);
            }
        }
        return new Result<>(count);
    }

    @Override
    public Result<Integer> batchUpdateFsEmpBindStatus(List<String> fsAccountList,
                                                      int status,
                                                      String appId) {
        return batchUpdateFsEmpBindStatus2(fsAccountList, status, appId, null);
    }

    @Override
    public Result<Integer> batchUpdateFsEmpBindStatus2(List<String> fsAccountList, int status, String appId, String outEa) {
        return new Result<>(accountEmployeeBindDao.batchUpdateFsEmpBindStatus(fsAccountList, status,appId,outEa));
    }

    @Override
    public Result<Integer> batchUpdateOutEmpBindStatus(String fsEa, List<String> outAccountList, int status, String appId) {
        return new Result<>(accountEmployeeBindDao.changeEmployeeStatus(SourceTypeEnum.QYWX.getSourceType(),fsEa,appId,outAccountList, status));
    }

    @Override
    public Result<List<QyweixinAccountEmployeeMapping>> findEmployeeBinds(List<QyweixinAccountEmployeeMapping> employeeMappings) {
        List<QyweixinAccountEmployeeMapping> allEmployeeMappings = new LinkedList<>();
        for(QyweixinAccountEmployeeMapping mapping : employeeMappings) {
            List<QyweixinAccountEmployeeMapping> employeeMappingList = accountEmployeeBindDao.findByEntity(mapping);
            if(CollectionUtils.isNotEmpty(employeeMappingList)) {
                allEmployeeMappings.addAll(employeeMappingList);
            }
        }
        return new Result<>(allEmployeeMappings.stream().distinct().collect(Collectors.toList()));
    }

    @Override
    public Result<Integer> saveEmployeeBinds(List<QyweixinAccountEmployeeMapping> employeeMappings) {
        int sum = 0;
        for(QyweixinAccountEmployeeMapping mapping : employeeMappings) {
            mapping.setIsvAccount(mapping.getOutAccount());
            int count = accountEmployeeBindDao.save(mapping);
            sum = sum + count;
        }
        return new Result<>(sum);
    }

    @Override
    public Result<Integer> updateEmployeeBinds(List<QyweixinAccountEmployeeMapping> employeeMappings) {
        int sum = 0;
        for(QyweixinAccountEmployeeMapping mapping : employeeMappings) {
            mapping.setIsvAccount(mapping.getOutAccount());
            int count = accountEmployeeBindDao.updateEmployeeBindMapping(mapping);
            sum = sum + count;
        }
        return new Result<>(sum);
    }

    @Override
    public Result<List<QyweixinAccountDepartmentMapping>> findDepartmentBinds(List<QyweixinAccountDepartmentMapping> departmentMappings) {
        List<QyweixinAccountDepartmentMapping> allDepartmentMappings = new LinkedList<>();
        for(QyweixinAccountDepartmentMapping mapping : departmentMappings) {
            List<QyweixinAccountDepartmentMapping> departmentMappingList = accountDepartmentBindDao.findByEntity(mapping);
            if(CollectionUtils.isNotEmpty(departmentMappingList)) {
                allDepartmentMappings.addAll(departmentMappingList);
            }
        }
        return new Result<>(allDepartmentMappings.stream().distinct().collect(Collectors.toList()));
    }

    @Override
    public Result<Integer> saveDepartmentBinds(List<QyweixinAccountDepartmentMapping> departmentMappings) {
        int sum = 0;
        for(QyweixinAccountDepartmentMapping mapping : departmentMappings) {
            int count = accountDepartmentBindDao.save(mapping);
            sum = sum + count;
        }
        return new Result<>(sum);
    }

    @Override
    public Result<Integer> updateDepartmentBinds(List<QyweixinAccountDepartmentMapping> departmentMappings) {
        int sum = 0;
        for(QyweixinAccountDepartmentMapping mapping : departmentMappings) {
            int count = accountDepartmentBindDao.updateDepartmentBindMapping(mapping);
            sum = sum + count;
        }
        return new Result<>(sum);
    }

    @Override
    public Result<List<QyweixinEmployeeAccountModel>> queryRepeatEmployees() {
        return new Result<>(accountEmployeeBindDao.queryRepeatEmployees());
    }
}
