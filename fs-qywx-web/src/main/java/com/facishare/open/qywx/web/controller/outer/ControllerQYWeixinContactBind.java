package com.facishare.open.qywx.web.controller.outer;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.feishu.syncapi.model.EmployeeBindModel;
import com.facishare.open.feishu.syncapi.model.PageModel;
import com.facishare.open.order.contacts.proxy.api.network.ProxyHttpClient;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountbind.utils.TraceUtil;
import com.facishare.open.qywx.accountinner.arg.QueryBindArg;
import com.facishare.open.qywx.accountinner.arg.QueryOutUnbindArg;
import com.facishare.open.qywx.accountinner.arg.QueryUnBindArg;
import com.facishare.open.qywx.accountinner.model.QYWXConnectParam;
import com.facishare.open.qywx.accountinner.result.ExportDataResult;
import com.facishare.open.qywx.accountinner.service.ContactBindInnerService;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountsync.excel.BuildExcelFile;
import com.facishare.open.qywx.accountsync.excel.ImportExcelFile;
import com.facishare.open.qywx.accountsync.model.qyweixin.AutoContactBindArg;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinCorpBindBo;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.service.ExcelFileService;
import com.facishare.open.qywx.web.arg.GetTemplateArg;
import com.facishare.open.qywx.web.arg.QueryConnectInfoArg;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.web.enums.UserContextSingleton;
import com.facishare.open.qywx.web.utils.ParallelUtils;
import com.facishare.open.outer.oa.connector.i18n.qywx.I18NStringEnum;
import com.facishare.open.outer.oa.connector.i18n.qywx.I18NStringManager;
import com.facishare.uc.api.model.usertoken.User;
import com.fxiaoke.common.Pair;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 企业微信绑定已有纷享账号（管理后台使用）
 * Created by liuwei on 2018/09/20
 */
@RestController
@Slf4j
@RequestMapping("/open/qyweixin")
public class ControllerQYWeixinContactBind {

    @Autowired
    private ContactBindInnerService contactBindInnerService;

    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;

    @Autowired
    private QyweixinGatewayInnerService qyweixinGatewayService;

    @Autowired
    private ExcelFileService excelFileService;

    @Resource
    private ProxyHttpClient proxyHttpClient;
    @Resource(name = "qywxI18NStringManager")
    private I18NStringManager i18NStringManager;

//    @RequestMapping(value = "/getWXAccounts", method = RequestMethod.GET)
//    public Result<List<SimpleContactBindInfo>> getWXAccounts(@RequestParam(required = false) String type,
//                                                             @RequestHeader(value = "dcInfo", required = false) String dcInfo,
//                                                             HttpServletRequest request){
//        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
//        log.info("getWXAccounts,dcInfo={},connectParam={}",dcInfo,connectParam);
//
//        Stopwatch stopwatch = Stopwatch.createStarted();
//        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
//        String outEa = QYWXConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();
//
//        Result<Boolean> result = redirectRequest(connectParam.getOutEa());
//        log.info("automaticAccountMactch,redirectRequest,result={}",result);
//
//        Result<List<SimpleContactBindInfo>> result2 = contactBindInnerService.getWXAccounts(fsEa, type, outEa);
//        log.info("ControllerQYWeixinContactBind.getWXAccounts,time={}s",stopwatch.elapsed(TimeUnit.MILLISECONDS));
//        stopwatch.stop();
//        return result2;
//    }

//    @RequestMapping(value = "/automaticAccountMactch", method = RequestMethod.GET)
//    public AdminSeviceResult<List<ContactBindInfo>> automaticAccountMactch(@RequestHeader(value = "dcInfo", required = false) String dcInfo,
//                                                                           HttpServletRequest request){
//        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
//        log.info("automaticAccountMactch,dcInfo={},connectParam={}",dcInfo,connectParam);
//
//        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
//        Integer userId = UserContextSingleton.INSTANCE.getUserContext().getEmployeeId();
//        String outEa = QYWXConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();
//
//        Result<Boolean> result = redirectRequest(connectParam.getOutEa());
//        log.info("automaticAccountMactch,redirectRequest,result={}",result);
//
//        Result<List<ContactBindInfo>> automaticAccountMactchResult = contactBindInnerService.automaticAccountMactch(fsEa, userId, outEa);
//        return new AdminSeviceResult<List<ContactBindInfo>>().result(automaticAccountMactchResult.getData(),
//                automaticAccountMactchResult.getErrorCode(), automaticAccountMactchResult.getErrorMsg());
//    }

    @RequestMapping(value = "/saveAccountBind", method = RequestMethod.POST)
    public Result<String> saveAccountBind(@RequestBody Map<String, List<String>> req,
                                          @RequestHeader(value = "dcInfo", required = false) String dcInfo,
                                          HttpServletRequest request){
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("saveAccountBind,dcInfo={},connectParam={}",dcInfo,connectParam);

        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        Integer userId = UserContextSingleton.INSTANCE.getUserContext().getEmployeeId();
        String outEa = QYWXConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        List<String> bindList = req.get("bindList");
        log.info("saveAccountBind,bindList={}",bindList);
        if(CollectionUtils.isNotEmpty(bindList)) {
            for(String item : bindList) {
                List<String> list = Splitter.on("&").splitToList(item);
                if(CollectionUtils.isEmpty(list) || list.size()!=2) {
                    return Result.newInstance(ErrorRefer.PARAM_ERROR);
                }
                String first = list.get(0);
                try {
                    Integer.valueOf(first);
                } catch (Exception e) {
                    return Result.newInstance(ErrorRefer.PARAM_ERROR);
                }
            }
        }

        return contactBindInnerService.saveAccountBind(fsEa, bindList, outEa);
    }

    @RequestMapping(value = "/deleteAccountBind", method = RequestMethod.POST)
    public Result<String> deleteAccountBind(@RequestBody Map<String, List<String>> req,
                                            @RequestHeader(value = "dcInfo", required = false) String dcInfo,
                                            HttpServletRequest request){
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("deleteAccountBind,dcInfo={},connectParam={}",dcInfo,connectParam);

        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        Integer userId = UserContextSingleton.INSTANCE.getUserContext().getEmployeeId();
        String outEa = QYWXConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        return contactBindInnerService.deleteAccountBind(fsEa, req.get("fsEmployeeAccountList"), outEa);
    }

//    @RequestMapping(value = "/updateAccountBind", method = RequestMethod.POST)
//    public Result<String> updateAccountBind(@RequestBody Map<String, List<String>> req,
//                                            @RequestHeader(value = "dcInfo", required = false) String dcInfo,
//                                            HttpServletRequest request){
//        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
//        log.info("updateAccountBind,dcInfo={},connectParam={}",dcInfo,connectParam);
//
//        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
//        Integer userId = UserContextSingleton.INSTANCE.getUserContext().getEmployeeId();
//        String outEa = QYWXConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();
//
//        return contactBindInnerService.updateAccountBind(fsEa, req.get("bindList"), outEa);
//    }

//    @RequestMapping(value = "/getAccountBind", method = RequestMethod.POST)
//    public AdminSeviceResult<List<ContactBindInfo>> getAccountBind(@RequestBody Map<String, Integer> req,
//                                                                   @RequestHeader(value = "dcInfo", required = false) String dcInfo,
//                                                                   HttpServletRequest request){
//        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
//        log.info("getAccountBind,dcInfo={},connectParam={}",dcInfo,connectParam);
//
//        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
//        Integer userId = UserContextSingleton.INSTANCE.getUserContext().getEmployeeId();
//        String outEa = QYWXConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();
//        String outDepId =  QYWXConnectParam.isInvalid(connectParam) ? "1" : connectParam.getOutDepId();
//
//        Stopwatch stopwatch = Stopwatch.createStarted();
//
//        Result<Boolean> result = redirectRequest(connectParam.getOutEa());
//        log.info("getAccountBind,redirectRequest,result={}",result);
//
//        Result<List<ContactBindInfo>> getAccountBindResult = contactBindInnerService.getAccountBind(fsEa,
//                req.get("bindType"),
//                userId,
//                outEa,
//                outDepId);
//        log.info("ControllerQYWeixinContactBind.getAccountBind,time={}s",stopwatch.elapsed(TimeUnit.MILLISECONDS));
//        stopwatch.stop();
//        return new AdminSeviceResult<List<ContactBindInfo>>().result(getAccountBindResult.getData()
//                , getAccountBindResult.getErrorCode(), getAccountBindResult.getErrorMsg());
//    }

    @RequestMapping(value = "/translate/jobId", method = RequestMethod.GET)
    @ResponseBody
    public Result<String> getJobId() {
        String ea = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        if (StringUtils.isBlank(ea)) {
            log.warn("没有解析到用户身份");
            new Result<>().Result(ErrorRefer.QUERRY_EMPTY.getCode(),"没有解析到用户身份",null);
        }
        return contactBindInnerService.getJobId(ea);
    }

    @RequestMapping(value = "/translate/url", method = RequestMethod.GET)
    @ResponseBody
    public Result<Map<String, Object>> getTranslateUrl(@RequestParam String jobId) {
        String ea = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        if (StringUtils.isBlank(ea) || StringUtils.isBlank(jobId)) {
            log.error("getTranslateUrl failed. ea:{}, jobId:{}", ea, jobId);
            return Result.newInstance(ErrorRefer.QUERRY_EMPTY);
        }
        Pair<Integer, String> pair = contactBindInnerService.getTranslateUrl(ea, jobId).getData();
        Map<String, Object> result = Maps.newHashMap();
        result.put("status", pair.first);
        result.put("url", pair.second);
        return new Result<>(result);
    }

    /**
     * 跳转到企业微信工具集页面
     * @param response
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/tools",method = RequestMethod.GET)
    public ModelAndView tools(HttpServletResponse response,
                              HttpServletRequest request) throws IOException, ServletException {
        //进行身份校验
        User user = UserContextSingleton.INSTANCE.getUserContext();
        String lang = TraceUtil.getLocale();
        i18NStringManager.setDefaultRequestScope(request,lang);
        if(ObjectUtils.isEmpty(user) || StringUtils.isEmpty(user.getEnterpriseAccount()) || user.getEmployeeId() == null) {
            request.setAttribute("errorMsg", "没有登陆纷享crm");
            request.setAttribute("propose", "请登录纷享crm，再刷新页面");
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return null;
        }
        Map<String, List<Integer>> useToolsAccountMap = new Gson().fromJson(ConfigCenter.USE_TOOLS_ACCOUNT, new TypeToken<Map<String, List<Integer>>>() {
        }.getType());
        String ea = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        Integer userId = UserContextSingleton.INSTANCE.getUserContext().getEmployeeId();
        if(useToolsAccountMap.containsKey(ea) && useToolsAccountMap.get(ea).contains(userId)) {
            ModelAndView mv = new ModelAndView("/WEB-INF/tools/index.html");
            return mv;
        }
        request.setAttribute("errorMsg", "登陆的纷享crm账号没有权限");
        request.setAttribute("propose", "请登录有权限的纷享crm，再刷新页面，或者向管理员申请工具使用的权限");
        request.getRequestDispatcher("/index.jsp").forward(request, response);
        return null;
    }

    //得到通讯录自动绑定开关信息
    @RequestMapping(value = "/getAutoBind",method =RequestMethod.GET)
    public Result<Integer> getAutoBind(@RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("getAutoBind,dcInfo={},connectParam={}",dcInfo,connectParam);

        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        Integer userId = UserContextSingleton.INSTANCE.getUserContext().getEmployeeId();
        String outEa = QYWXConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();
        String outDepId =  QYWXConnectParam.isInvalid(connectParam) ? "1" : connectParam.getOutDepId();

        Result<Integer> aut = contactBindInnerService.getAutoBind(fsEa,outEa);
        return new Result<>(aut.getData());
    }

    //开启授权
    @RequestMapping(value = "/enableAutoBind",method =RequestMethod.POST)
    public Result<Map<String, String>> enableAutoBind(@RequestBody AutoContactBindArg autoContactBindArg,
                                                      @RequestHeader(value = "dcInfo", required = false) String dcInfo) {
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("enableAutoBind,dcInfo={},connectParam={}",dcInfo,connectParam);

        String outEa = QYWXConnectParam.isInvalid(connectParam) ? null : connectParam.getOutEa();

        log.info("ControllerQYWeixinContactBind.enableAutoBind autoContactBindArg={}, flag={}", autoContactBindArg, autoContactBindArg.getFlag());
        String fs_ea = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        int employeeId = UserContextSingleton.INSTANCE.getUserContext().getEmployeeId();
        log.info("ControllerQYWeixinContactBind.enableAutoBind fs_ea={}, employeeId={}", fs_ea, employeeId);
        Map<String, String> resultMap = new HashMap<>();
        if(!"0".equals(autoContactBindArg.getFlag()) && !"1".equals(autoContactBindArg.getFlag())) {
            resultMap.put("status", "0");
            resultMap.put("autoBindStatus", "0");
            return Result.newInstance2(ErrorRefer.CONTACT_AUTO_ATTACHMENTS,resultMap);
        }
        int autoBindFlag;
        if("1".equals(autoContactBindArg.getFlag())) {
            autoBindFlag = 1;
        } else {
            autoBindFlag = 0;
        }
        log.info("ControllerQYWeixinContactBind.enableAutoBind autoBindFlag={}", autoBindFlag);
        Result<Integer> aut = contactBindInnerService.saveAutoBind(fs_ea, outEa, autoBindFlag);
        log.info("ControllerQYWeixinContactBind.enableAutoBind aut={}", aut);
        //每次开启都会执行自动绑定
        if("1".equals(autoContactBindArg.getFlag())) {
            Result<Integer> result = contactBindInnerService.saveAutoContactBind(fs_ea, employeeId, outEa);
            log.info("ControllerQYWeixinContactBind.enableAutoBind result={}", result);
            if(!ErrorRefer.SUCC.getCode().equals(result.getErrorCode())) {
                resultMap.put("status", "1");
                resultMap.put("autoBindStatus", "0");
                return Result.newInstance2(ErrorRefer.CONTACT_AUTO_ATTACHMENTS,resultMap);
            }
        }
        resultMap.put("status", "1");
        resultMap.put("autoBindStatus", "1");
        return Result.newInstance2(ErrorRefer.SUCC,resultMap);
    }

    /**
     *兼容老企业，查询企业的名称
     */
    @Deprecated
    @RequestMapping(value = "/queryCorpName", method = RequestMethod.GET)
    public Result<String> queryCorpName (@RequestHeader(value = "dcInfo", required = false) String dcInfo,
                                         HttpServletRequest request) {
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("queryCorpName,dcInfo={},connectParam={}",dcInfo,connectParam);
        String fsEa = null;
        String outEa = null;
        String outDepId = null;
        if(QYWXConnectParam.isInvalid(connectParam)) {
            fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
            com.facishare.open.qywx.accountbind.result.Result<String> qyweixinCorpIdInfoResult = qyweixinAccountBindService.fsEaToOutEa("qywx", fsEa);
            if(ObjectUtils.isEmpty(qyweixinCorpIdInfoResult.getData())) {
                return Result.newInstance(ErrorRefer.CORP_ACCOUNT_NOT_BIND);
            }
            outEa = qyweixinCorpIdInfoResult.getData();
        } else {
            fsEa = connectParam.getFsEa();
            outEa = connectParam.getOutEa();
            outDepId = connectParam.getOutDepId();
        }

        return contactBindInnerService.queryCorpName2(fsEa, outEa, outDepId);
    }

    /**
     * 查询未绑定的纷享员工列表
     */
    @RequestMapping(value = "/employee/queryFsUnbind", method = RequestMethod.POST)
    @ResponseBody
    public Result<PageModel<List<EmployeeBindModel.FsEmployee>>> queryFsUnbind(@RequestBody QueryUnBindArg arg,
                                                                               @RequestHeader(value = "dcInfo", required = false) String dcInfo,
                                                                               HttpServletRequest request) {
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("queryFsUnbind,dcInfo={},connectParam={}",dcInfo,connectParam);
        if(ObjectUtils.isEmpty(arg)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        String ea = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        arg.setEa(ea);

        arg.setConnectParam(connectParam);

        return contactBindInnerService.queryFsUnbind(arg);
    }

    /**
     * 查询纷享和飞书员工绑定信息
     */
    @RequestMapping(value = "/employee/queryBind", method = RequestMethod.POST)
    @ResponseBody
    public Result<PageModel<List<EmployeeBindModel>>> queryBind(@RequestBody QueryBindArg arg,
                                                                @RequestHeader(value = "dcInfo", required = false) String dcInfo,
                                                                HttpServletRequest request) {
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("queryBind,dcInfo={},connectParam={}",dcInfo,connectParam);
        if(ObjectUtils.isEmpty(arg)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        String ea = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        arg.setEa(ea);

        arg.setConnectParam(connectParam);
        log.info("queryBind,arg={}",JSONObject.toJSONString(arg));

        return contactBindInnerService.queryBind(arg);
    }

    /**
     * 查询纷享和飞书员工绑定信息
     */
    @RequestMapping(value = "/employee/queryOutUnbind", method = RequestMethod.POST)
    @ResponseBody
    public Result<List<EmployeeBindModel.OutEmployee>> queryOutUnbind(@RequestBody QueryOutUnbindArg arg,
                                                                      @RequestHeader(value = "dcInfo", required = false) String dcInfo,
                                                                      HttpServletRequest request) {
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("queryOutUnbind,dcInfo={},connectParam={}",dcInfo,connectParam);
        if(ObjectUtils.isEmpty(arg)) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        String ea = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        arg.setEa(ea);

        arg.setConnectParam(connectParam);

        return contactBindInnerService.queryOutUnbind(arg);
    }

    /**
     * 导出绑定关系
     */
    @PostMapping("/employee/exportEmployeeBind")
    public Result<ExportDataResult> exportEmployeeBind(@RequestHeader(value = "dcInfo", required = false) String dcInfo,
                                                       HttpServletRequest request) {
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("exportEmployeeBind,dcInfo={},connectParam={}", dcInfo, connectParam);
        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        Integer fsUserId = UserContextSingleton.INSTANCE.getUserContext().getEmployeeId();
        String lang = TraceUtil.getLocale();
        log.info("ControllerQYWeixinContactBind.exportEmployeeBind,lang={}",lang);

        ParallelUtils.createBackgroundTask().submit(() -> {
            log.info("ControllerQYWeixinContactBind.exportEmployeeBind,start");

            Result<ExportDataResult> result2 = contactBindInnerService.exportEmployeeBind(fsEa, connectParam.getOutEa(), fsUserId, lang);
            log.info("ControllerQYWeixinContactBind.exportEmployeeBind,result2.2={}",result2);
        }).run();

        return Result.newInstance(ErrorRefer.EXPORTING);
    }

    /**
     * 下载导入模板
     */
    @PostMapping("/getTemplate")
    public Result<BuildExcelFile.Result> getTemplate(@RequestBody GetTemplateArg arg) {
        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        Integer fsUserId = UserContextSingleton.INSTANCE.getUserContext().getEmployeeId();
        if (ObjectUtils.isEmpty(arg) || arg.getTemplateType()==null) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        return excelFileService.buildExcelTemplate(fsEa,arg.getTemplateType());
    }

    /**
     * 导入员工绑定关系
     */
    @PostMapping("/employee/importDepAndEmpMapping")
    public Result<ImportExcelFile.Result> importDepAndEmpMapping(@RequestBody ImportExcelFile.MappingDataArg arg,
                                                                 @RequestHeader(value = "dcInfo", required = false) String dcInfo,
                                                                 HttpServletRequest request) {
        QYWXConnectParam connectParam = QYWXConnectParam.parse(dcInfo);
        log.info("importDepAndEmpMapping,dcInfo={},connectParam={}", dcInfo, connectParam);
        if (ObjectUtils.isEmpty(arg) || arg.getTemplateType()==null) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        Integer fsUserId = UserContextSingleton.INSTANCE.getUserContext().getEmployeeId();

        arg.setFsEa(fsEa);
        arg.setOutEa(connectParam.getOutEa());
        arg.setFsUserId(fsUserId);

        String traceId = TraceUtil.get();
        String lang = TraceUtil.getLocale();
        log.info("importDepAndEmpMapping,lang={}", lang);

        Result<QyweixinCorpBindBo> corpBindInfo = qyweixinGatewayService.getCorpBindInfo(connectParam.getOutEa(),
                ConfigCenter.crmAppId);
        log.info("importDepAndEmpMapping,getCorpBindInfo,corpBindInfo={}",corpBindInfo);
        if(corpBindInfo.getData()==null) {
            return Result.newInstance(ErrorRefer.IMPORT_EMP_ONLY_SUPPORT_THIRD_CRM_APP);
        }

        ParallelUtils.createBackgroundTask().submit(() -> {
            TraceUtil.initTrace(traceId);
            try {
                Result<ImportExcelFile.Result> result2 = excelFileService.importExcelFile(arg,lang);
                log.info("importDepAndEmpMapping,importExcelFile,result2={}", result2);
            } catch (IOException e) {
                log.error("importDepAndEmpMapping get error", e);
            }
        }).run();

        ImportExcelFile.Result result3 = new ImportExcelFile.Result();
        result3.setPrintMsg(i18NStringManager.get(I18NStringEnum.s188,lang,null));
        return new Result<>(result3);
    }

    /**
     * 查询企微连接器连接信息
     *
     */
    @RequestMapping(value = "/queryConnectInfo", method = RequestMethod.POST)
    @ResponseBody
    public Result<QYWXConnectParam> queryConnectInfo(@RequestBody QueryConnectInfoArg arg) {
        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        log.info("ControllerQYWeixinContactBind.queryConnectInfo,arg={}",arg);
        if(arg == null) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }

        Result<QYWXConnectParam> result = qyweixinGatewayService.queryConnectInfo(fsEa,arg.getDataCenterId());
        log.info("ControllerQYWeixinContactBind.queryConnectInfo,result={}",result);
        return result;
    }

    /**
     * 纷享和企微绑定
     *
     */
    @RequestMapping(value = "/fsBindWithQywx", method = RequestMethod.POST)
    @ResponseBody
    public Result<QYWXConnectParam> fsBindWithQywx(@RequestBody QYWXConnectParam connectParam,
                                                   HttpServletRequest request) {
        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        log.info("ControllerQYWeixinContactBind.fsBindWithQywx,fsEa={}",fsEa);

        connectParam.setFsEa(fsEa);
        if(QYWXConnectParam.isInvalid(connectParam) || StringUtils.isEmpty(connectParam.getDataCenterName())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }

        if(StringUtils.isEmpty(connectParam.getOutDepId())) {
            connectParam.setOutDepId("1");
        }

        Result<Void> result = qyweixinGatewayService.fsBindWithQywx(connectParam,true);
        log.info("ControllerQYWeixinContactBind.fsBindWithQywx,result={}",result);
        if(result.isSuccess()) {
            return new Result<>(connectParam);
        }
        return new Result<>(result.getErrorCode(),result.getErrorMsg(),result.getI18nKey());
    }

    /**
     * 纷享和企微解绑
     *
     */
    @RequestMapping(value = "/fsUnBindWithQywx", method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> fsUnBindWithQywx(@RequestBody QYWXConnectParam connectParam,
                                         HttpServletRequest request) {
        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        log.info("ControllerQYWeixinContactBind.fsUnBindWithQywx,fsEa={}",fsEa);

        connectParam.setFsEa(fsEa);

        if(StringUtils.isEmpty(connectParam.getFsEa())
                || StringUtils.isEmpty(connectParam.getDataCenterId())
                || StringUtils.isEmpty(connectParam.getOutEa())) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }

        Result<Void> result = qyweixinGatewayService.fsUnBindWithQywx(connectParam);
        log.info("ControllerQYWeixinContactBind.fsUnBindWithQywx,result={}",result);
        return result;
    }

    /**
     * 纷享和企微解绑
     *
     */
    @RequestMapping(value = "/fsUnBindWithQywx2", method = RequestMethod.POST)
    @ResponseBody
    public Result<Void> fsUnBindWithQywx2(@RequestBody QYWXConnectParam connectParam,
                                          HttpServletRequest request) {
        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
        log.info("ControllerQYWeixinContactBind.fsUnBindWithQywx2,fsEa={}",fsEa);

        Result<Void> result = qyweixinGatewayService.fsUnBindWithQywx2(connectParam.getOutEa(),connectParam.getOutDepId());
        log.info("ControllerQYWeixinContactBind.fsUnBindWithQywx2,result={}",result);
        return result;
    }
}
