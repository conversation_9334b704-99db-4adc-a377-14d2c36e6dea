package com.facishare.open.qywx.web.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.order.contacts.proxy.api.network.ProxyHttpClient;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.result.EmployeeAccountMatchResult;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountbind.utils.TraceUtil;
import com.facishare.open.qywx.accountinner.model.QywxAccessTokenInfo;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.messagesend.enums.ErrorRefer;
import com.facishare.open.qywx.messagesend.enums.QyWeixinMsgType;
import com.facishare.open.qywx.web.manager.SfaApiManager;
import com.facishare.open.qywx.messagesend.model.*;
import com.facishare.open.qywx.messagesend.result.Result;
import com.facishare.open.qywx.messagesend.service.QYWeixinMessageSendService;
import com.fxiaoke.otherrestapi.function.arg.FunctionServiceExecuteArg;
import com.fxiaoke.otherrestapi.function.data.FunctionServiceParameterData;
import com.fxiaoke.otherrestapi.function.data.HeaderObj;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Splitter;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URI;
import java.rmi.RemoteException;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Created by fengyh on 2018/7/23.
 */
@Slf4j
@Service("qYWeixinMessageSendService")
public class QYWeixinMessageSendServiceImpl implements QYWeixinMessageSendService {

    @Resource
    QyweixinGatewayInnerService qyweixinGatewayInnerService;

    @Resource
    QyweixinAccountBindService qyweixinAccountBindService;

    @Resource
    private SfaApiManager sfaApiManager;

    @Autowired
    private EIEAConverter eieaConverter;

    @ReloadableProperty("async.send.msg.to.qywx")
    private boolean asyncSendMsgToQywx;

    ThreadPoolExecutor executor = new ThreadPoolExecutor(50,200,60, TimeUnit.SECONDS,new LinkedBlockingQueue<>());

    @Autowired
    private ProxyHttpClient proxyHttpClient;

    @Override
    public Result<SendQyWeixinMsgRsp> sendQyWeixinMsg(SendQyWeixinMsgReq<?> QyWeixinMsgReq) {
        QyWeixinMsgReq.setAppId(ConfigCenter.crmAppId);
        String traceId = TraceUtil.get();
        log.info("sendQyWeixinMsg,traceId={}",traceId);
        if(StringUtils.isEmpty(traceId)) {
            traceId = UUID.randomUUID().toString();
            TraceUtil.initTrace(traceId);
        }
        log.info("QYWeixinMessageSendServiceImpl.sendQyWeixinMsg,asyncSendMsgToQywx={}",asyncSendMsgToQywx);

        //针对企业，过滤消息
        Map<String, String> filterMessagesEaMap = new Gson().fromJson(ConfigCenter.FILTER_MESSAGES_EA, new TypeToken<Map<String, String>>() {
        });
        if(filterMessagesEaMap.containsKey(QyWeixinMsgReq.getFsEnterpriseAccount())) {
            HeaderObj headerObj = new HeaderObj(eieaConverter.enterpriseAccountToId(QyWeixinMsgReq.getFsEnterpriseAccount()),-10000);
            FunctionServiceExecuteArg arg = new FunctionServiceExecuteArg();
            arg.setApiName(filterMessagesEaMap.get(QyWeixinMsgReq.getFsEnterpriseAccount()));
            arg.setBindingObjectAPIName("NONE");
            arg.setNameSpace("controller");
            if(!QyWeixinMsgType.MSG_TEXT.equals(QyWeixinMsgReq.getType())) {
                log.info("QYWeixinMessageSendServiceImpl.sendQyWeixinMsg,type={},sourceMsg={}", QyWeixinMsgReq.getType(), QyWeixinMsgReq.getSourceMsg());
                Map<String, Object> form = new Gson().fromJson(QyWeixinMsgReq.getSourceMsg(), new TypeToken<Map<String, Object>>() {
                });
                List<FunctionServiceParameterData> parameters = new LinkedList<>();
                FunctionServiceParameterData<Map> data = new FunctionServiceParameterData();
                data.setName("sourceMsgMap");
                data.setType("Map");
                data.setValue(form);
                parameters.add(data);
                arg.setParameters(parameters);
                com.facishare.open.qywx.accountsync.result.Result<Object> objectResult = null;
                try {
                    objectResult = sfaApiManager.executeCustomFunction(headerObj, arg);
                } catch (RemoteException e) {
                    e.printStackTrace();
                }
                if(objectResult == null || !objectResult.isSuccess() || ObjectUtils.isEmpty(objectResult.getData())) {
                    log.info("QYWeixinMessageSendServiceImpl.sendQyWeixinMsg,objectResult={}", objectResult);
                    return new Result<SendQyWeixinMsgRsp>().addError(ErrorRefer.FUNCTION_ERROR.getCode(), objectResult.getErrorMsg());
                }
                //判断是否可以发送
                Map<String, Object> objectMap = new Gson().fromJson(objectResult.getData().toString(), new TypeToken<Map<String, Object>>() {
                });
                boolean isContinue = Boolean.parseBoolean(objectMap.get("continue").toString());
                log.info("QYWeixinMessageSendServiceImpl.sendQyWeixinMsg,isContinue={}", isContinue);
                if(!isContinue) {
                    SendQyWeixinMsgRsp rsp = new SendQyWeixinMsgRsp();
                    rsp.setErrcode("0");
                    rsp.setErrmsg("success in async mode");
                    return new Result(rsp);
                }
            }
        }
        if(asyncSendMsgToQywx) {
            String traceId2 = TraceUtil.get();
            executor.submit(()->{
                TraceUtil.initTrace(traceId2);
                sendQyWeixinMsg2(QyWeixinMsgReq);
            });
            SendQyWeixinMsgRsp rsp = new SendQyWeixinMsgRsp();
            rsp.setErrcode("0");
            rsp.setErrmsg("success in async mode");

            log.info("QYWeixinMessageSendServiceImpl.sendQyWeixinMsg,res={},executor={}",rsp,executor.toString());
            return new Result(rsp);
        } else {
            log.info("QYWeixinMessageSendServiceImpl.sendQyWeixinMsg,sendQyWeixinMsg2=true");
            return sendQyWeixinMsg2(QyWeixinMsgReq);
        }
    }

    private Result<SendQyWeixinMsgRsp> sendQyWeixinMsg2(SendQyWeixinMsgReq<?> QyWeixinMsgReq) {
        log.info("QYWeixinMessageSendServiceImpl.sendQyWeixinMsg2,QyWeixinMsgReq={}",QyWeixinMsgReq);
        if(CollectionUtils.isEmpty(QyWeixinMsgReq.getToUserList())) {
            log.info("QYWeixinMessageSendServiceImpl.sendQyWeixinMsg2,QyWeixinMsgReq={}",QyWeixinMsgReq.getToUserList());
            return new Result<>(ErrorRefer.MSG_RECEIVER_CANNOT_BE_EMPTY);
        }

        //CRM往企微发送消息，如果出现一个CRM对多个企微，并且CRM人员同时和多个企微人员绑定，允许同时把消息发送到多个企微用户
        List<String> fsAccountList = QyWeixinMsgReq.getToUserList()
                .stream()
                .map(v -> String.format("E.%S.%S", QyWeixinMsgReq.getFsEnterpriseAccount(), v))
                .collect(Collectors.toList());
        com.facishare.open.qywx.accountbind.result.Result<List<EmployeeAccountMatchResult>> userMap =
                qyweixinAccountBindService.fsAccountToOutAccountBatch2("qywx", null, fsAccountList ,null);
        log.info("QYWeixinMessageSendServiceImpl.sendQyWeixinMsg2,userMap={}",userMap);
        if(userMap.getData()==null || userMap.getData().isEmpty()) {
            return new Result<>(ErrorRefer.NO_BIND_EMPLOYEE);
        }
        Map<String,List<EmployeeAccountMatchResult>> userMapList = new HashMap<>();
        for(EmployeeAccountMatchResult matchResult : userMap.getData()) {
            String outEa = matchResult.getOutEa();
            if(!userMapList.containsKey(outEa)) {
                userMapList.put(outEa,new ArrayList<>());
            }
            userMapList.get(outEa).add(matchResult);
        }
        log.info("QYWeixinMessageSendServiceImpl.sendQyWeixinMsg2,userMapList={}",userMapList);

        for(String outEa : userMapList.keySet()) {
            if(ConfigCenter.MAIN_ENV) {
                com.facishare.open.qywx.accountbind.result.Result<QyweixinAccountEnterpriseMapping> enterpriseMappingResult = qyweixinAccountBindService.fsEaToOutEaResult2("qywx", QyWeixinMsgReq.getFsEnterpriseAccount(), outEa);
                if(enterpriseMappingResult.isSuccess() && ObjectUtils.isEmpty(enterpriseMappingResult.getData())) {
                    log.info("QYWeixinMessageSendServiceImpl.sendQyWeixinMsg2,enterpriseMappingResult={}",enterpriseMappingResult.getData());
                    continue;
                }
            }

            String appId = userMapList.get(outEa).get(0).getAppId();
            com.facishare.open.qywx.accountsync.result.Result<QywxAccessTokenInfo> result = qyweixinGatewayInnerService.getAccessTokenInfo2(outEa,
                    appId);
            log.info("QYWeixinMessageSendServiceImpl.sendQyWeixinMsg2,getAccessTokenInfo2,result={}",result);
            if(!result.isSuccess()){
                log.info("QYWeixinMessageSendServiceImpl.sendQyWeixinMsg2,getAccessTokenInfo2,failed");
                continue;
            }
            List<String> outAccountList = userMapList.get(outEa)
                    .stream()
                    .map(item->item.getOutAccount())
                    .collect(Collectors.toList());
            log.info("QYWeixinMessageSendServiceImpl.sendQyWeixinMsg2,outEa={},outAccountList={}",outEa,outAccountList);
            Result<SendQyWeixinMsgRsp> sendMsgResult = sendMsg(QyWeixinMsgReq.getType(),
                    QyWeixinMsgReq.getMsgContent(),
                    result.getData().getCorpAccessToken(),
                    result.getData().getAgentId(),
                    outAccountList,
                    outEa,
                    appId);
            log.info("QYWeixinMessageSendServiceImpl.sendQyWeixinMsg2,sendMsgResult={}",sendMsgResult);
        }
        return new Result<>();
    }

    public Result<SendQyWeixinMsgRsp> sendMsg(String corpId,
                                              String appId,
                                              QyWeixinMsgType msgType,
                                              Object msgContent,
                                              List<String> outUserIdList) {
        com.facishare.open.qywx.accountsync.result.Result<QywxAccessTokenInfo> result = qyweixinGatewayInnerService.getAccessTokenInfo2(corpId,
                appId);
        if (!result.isSuccess()) {
            return new Result<SendQyWeixinMsgRsp>(ErrorRefer.FIELD).addError("微信授权信息获取异常！");
        }
        return sendMsg(msgType, msgContent, result.getData().getCorpAccessToken(), result.getData().getAgentId(), outUserIdList, corpId, appId);
    }

    private Result<SendQyWeixinMsgRsp> sendMsg(QyWeixinMsgType msgType,
                                               Object msgContent,
                                               String corpAccessToken,
                                               String agentId,
                                               List<String> outUserIdList,
                                               String outEa,
                                               String appId) {
        String url ="https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token="+corpAccessToken;

        StringBuffer toUser = new StringBuffer();
        for (String outUserId : outUserIdList) {
            toUser.append(outUserId).append("|");
        }

        Map<String, Object> form = new HashMap<>();
        form.put("touser", toUser.toString());
        form.put("toparty", "");
        form.put("totag", "");
        form.put("msgtype", msgType.getType());
        form.put("agentid", agentId);
        form.put("enable_id_trans", 1);
        String replaceMsg = replaceMsg(JSONObject.toJSONString(msgContent));
        log.info("QYWeixinMessageSendServiceImpl.sendMsg.replaceMsg={}", replaceMsg);
        //判断类型
        if (QyWeixinMsgType.MSG_TEXT.equals(msgType)) {
            TextMsgContent content = new Gson().fromJson(replaceMsg, TextMsgContent.class);
            form.put(QyWeixinMsgType.MSG_TEXT.getType(), content);
        } else if (QyWeixinMsgType.MSG_TEXT_CARD.equals(msgType)) {
            TextCardMsgContent content = new Gson().fromJson(replaceMsg, TextCardMsgContent.class);
            log.info("QYWeixinMessageSendServiceImpl.sendMsg,content={}", content);

            URI uri = URI.create(content.getUrl());
            StringBuilder sb = new StringBuilder();
            List<String> queryList = Splitter.on("&").splitToList(uri.getQuery());
            if(CollectionUtils.isNotEmpty(queryList)) {
                for(String item : queryList) {
                    if(StringUtils.containsIgnoreCase(item,"appID")) {
                        sb.append("appID="+appId+"&");
                    } else {
                        sb.append(item+"&");
                    }
                }
                String query = StringUtils.removeEnd(sb.toString(),"&");
                String newUrl = uri.getScheme()+"://"+uri.getHost() + uri.getRawPath()+"?"+query;
                log.info("QYWeixinMessageSendServiceImpl.sendMsg,newUrl={}",newUrl);
                content.setUrl(newUrl);
            }

            if(StringUtils.startsWith(appId,"dk")) {
                String jumpUrl = content.getUrl().replace("/doFunction?","/doRepFunction?")
                        .replace("appID=","appId=");
                jumpUrl=jumpUrl+"&corpId=" + outEa + "&go2Crm=true";
                content.setUrl(jumpUrl);
                log.info("QYWeixinMessageSendServiceImpl.sendMsg,content2={}", content);
            }
            form.put(QyWeixinMsgType.MSG_TEXT_CARD.getType(), content);
        } else if (QyWeixinMsgType.MSG_NEWS_CARD.equals(msgType)) {
            NewsMsgContent content = new Gson().fromJson(replaceMsg, NewsMsgContent.class);
            form.put(QyWeixinMsgType.MSG_NEWS_CARD.getType(), content);
        } else if (QyWeixinMsgType.MINIPROGRAM_NOTICE.equals(msgType)) {
            MiniprogramNoticeMsgContent content = new Gson().fromJson(replaceMsg, MiniprogramNoticeMsgContent.class);
            form.put(QyWeixinMsgType.MINIPROGRAM_NOTICE.getType(), content);
        }

        try{
            log.info("trace sendMsg set:{}", form);
            String httpRsp = proxyHttpClient.postUrl(url, form, new HashMap<>());
            Gson gson = new Gson();
            SendQyWeixinMsgRsp sendQyWeixinMsgRsp = gson.fromJson(httpRsp, new TypeToken<SendQyWeixinMsgRsp>(){}.getType());
            log.info("trace sendMsg httpRsp:{}", httpRsp);
            return new Result<>(sendQyWeixinMsgRsp);
        } catch (Exception e){
            log.error("tarce sendMsg get exception, ", e);
            return new Result<>(ErrorRefer.FIELD);
        }
    }

    private String replaceMsg(String msg) {
        String userNamePrefix = "U-FSQYWX-";
        Pattern pattern = Pattern.compile(userNamePrefix);
        Matcher matcher = pattern.matcher(msg);
        Set<String> userAccountSet = new HashSet<>();
        Set<String> deptAccountSet = new HashSet<>();
        while (matcher.find()){
            //字串的索引
            String account = msg.substring(matcher.start(), matcher.end() + 32);
            userAccountSet.add(account);
        }
        String deptNamePrefix = "D-FSQYWX-";
        Pattern pattern1 = Pattern.compile(deptNamePrefix);
        Matcher matcher1 = pattern1.matcher(msg);
        while (matcher1.find()){
            System.out.println(matcher1.start());
            //字串的索引
            int sum = 0;
            int index = matcher1.end();
            char c = msg.charAt(index);
            while (StringUtils.isNumeric(String.valueOf(c))){
                sum ++;
                c = msg.charAt(index ++);
            }
            String account = msg.substring(matcher1.start(), matcher1.end() + sum - 1);
            deptAccountSet.add(account);
        }
        if(CollectionUtils.isNotEmpty(userAccountSet)) {
            for(String account : userAccountSet) {
                msg = msg.replace(account, "$userName="+ account.replace(userNamePrefix, "") +"$");
            }
        }
        if(CollectionUtils.isNotEmpty(deptAccountSet)) {
            for(String account : deptAccountSet) {
                msg = msg.replace(account, "$departmentName="+ account.replace(deptNamePrefix, "") +"$");
            }
        }
        return msg;
    }

    public static void main(String[] args) {
        String url = "https://open.ceshi112.com/qyweixin/doFunction?appID=dk3ff8a65e707ca3c2&param=aHR0cHM6Ly93d3cuY2VzaGkxMTIuY29tL2hjcm0vd2VjaGF0L2Z1bmN0aW9uL3RvZG8/YXBpbmFtZT1BY2NvdW50T2JqJmlkPTY3MWIxM2NiNGM0NjgzMDAwMWM2Y2U2OCZlYT04MTI0Mw==";
        String appId = "wx88a141937dd6f838";
        URI uri = URI.create(url);
        StringBuilder sb = new StringBuilder();
        List<String> queryList = Splitter.on("&").splitToList(uri.getQuery());
        if(CollectionUtils.isNotEmpty(queryList)) {
            for(String item : queryList) {
                if(StringUtils.containsIgnoreCase(item,"appID")) {
                    sb.append("appID="+appId+"&");
                } else {
                    sb.append(item+"&");
                }
            }
            String query = StringUtils.removeEnd(sb.toString(),"&");
            url = uri.getScheme()+"://"+uri.getHost() + uri.getRawPath()+"?"+query;
        }
        System.out.println(url);
    }
}
