package com.facishare.open.qywx.accountsync.model;

import com.facishare.common.fsi.ProtoBase;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinAgentPrivilege;
import io.protostuff.Tag;
import lombok.Data;

import java.util.List;

/**
 * 事件开通发送
 * Created by <PERSON><PERSON><PERSON> on 2018/07/13
 */
@Data
public class QyweixinEnterpriseOrder extends ProtoBase {

    @Tag(1)
    private String corpId;   //企业微信id
    @Tag(2)
    private String appId;  //应用id
    @Tag(3)
    private String corpName; //企业名称
    @Tag(4)
    private String userId;   //管理员userId(创建者)
    @Tag(5)
    private String userName; //管理员名称
    @Tag(6)
    private String avatar;   //管理员的头像url
    @Tag(7)
    private QyweixinAgentPrivilege privilege;  //应用对应的权限(应用可见范围 部门、标签、成员)
    @Tag(8)
    private List<String> adminList; //应用的管理员列表(不包含创建者userId)
    @Tag(9)
    private Integer orderType; //订单类型。0-普通订单，1-扩容订单，2-续期，3-版本变更，4-试用（若是试用则下面字段都为空）
    @Tag(10)
    private String operatorId;  //订单操作者userid
    @Tag(11)
    private String beginTime; //服务开始日期 (格式yyyy/MM/dd)
    @Tag(12)
    private String endTime; //服务终止日期 (格式yyyy/MM/dd)
    @Tag(13)
    private Integer userCount; //购买的人数
    @Tag(14)
    private String orderId; //订单号
    @Tag(15)
    private Long price; //实付款金额price，单位分
    @Tag(16)
    private String editionId; //购买版本id
}
