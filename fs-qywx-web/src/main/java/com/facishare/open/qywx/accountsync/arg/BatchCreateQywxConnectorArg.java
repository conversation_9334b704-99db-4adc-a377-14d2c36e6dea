package com.facishare.open.qywx.accountsync.arg;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import lombok.Data;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class BatchCreateQywxConnectorArg implements Serializable {
    @Data
    public static class EnterpriseMapping implements Serializable {
        private String fsEa;
        private String outEa;
        private String outEn;

        public static void main(String[] args)  {
            try {
                File file = new File("C:\\Users\\<USER>\\Desktop\\美孚1+N支持\\美孚1+N企业账号绑定表模板1026.csv");
                List<String> lineList = FileUtils.readLines(file);
                List<EnterpriseMapping> enterpriseMappingList = new ArrayList<>();
                for(int i=1;i<lineList.size();i++) {
                    String line = lineList.get(i);
                    List<String> items = Splitter.on(",").splitToList(line);
                    EnterpriseMapping mapping = new EnterpriseMapping();
                    mapping.setOutEn(items.get(1));
                    mapping.setFsEa(items.get(2));
                    enterpriseMappingList.add(mapping);
                }
                String json = JSONObject.toJSONString(enterpriseMappingList);
                System.out.println(json);
            } catch (Exception e) {
                System.out.println(e.getMessage());
            }
        }
    }

    private String upstreamCorpId;
    private String upstreamAppId;
    private String upstreamAgentId;

    private String dataCenterName;

    private List<EnterpriseMapping> bindList;

    public static void main(String[] args) {
        BatchCreateQywxConnectorArg arg = new BatchCreateQywxConnectorArg();
        arg.setDataCenterName("企微连接器");

        arg.setBindList(new ArrayList<>());
        EnterpriseMapping mapping = new EnterpriseMapping();
        mapping.setFsEa("82777");
        mapping.setOutEa("wwceff72a7c333e882");
        mapping.setOutEn("一对多测试企业112（拨测用勿删除）");
        arg.getBindList().add(mapping);
        String jsonString = JSONObject.toJSONString(arg);
        System.out.println(jsonString);
    }
}
