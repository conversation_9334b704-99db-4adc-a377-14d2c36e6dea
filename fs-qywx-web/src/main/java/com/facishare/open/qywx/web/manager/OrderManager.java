package com.facishare.open.qywx.web.manager;

import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.order.contacts.proxy.api.arg.CreateCrmOrderArg;
import com.facishare.open.order.contacts.proxy.api.arg.CreateCustomerArg;
import com.facishare.open.order.contacts.proxy.api.enums.CustomerSourceEnum;
import com.facishare.open.order.contacts.proxy.api.model.CrmOrderMappingModel;
import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.facishare.open.order.contacts.proxy.api.service.FsOrderServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.EnterpriseUtils;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountinner.model.QyweixinGetAuthInfoRsp;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinAgentRsp;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinOrderInfoRsp;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.web.core.enums.OrderTypeEnum;
import com.facishare.open.qywx.web.core.enums.ProcessingStatusEnum;
import com.facishare.open.qywx.accountsync.core.enums.SourceTypeEnum;
import com.facishare.open.qywx.web.db.dao.QyweixinCorpInfoDao;
import com.facishare.open.qywx.web.db.dao.QyweixinOrderInfoDao;
import com.facishare.open.qywx.accountsync.model.ProductDefine;
import com.facishare.open.qywx.accountsync.model.QyweixinEnterpriseOrder;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinAgentPrivilege;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinUserDetailInfoRsp;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinCorpInfoBo;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinOrderInfoBo;
import com.facishare.open.qywx.web.model.qyweixin.QyweixinGetCorptokenRsp;
import com.facishare.open.qywx.web.utils.DateUtils;
import com.facishare.organization.adapter.api.model.biz.employee.arg.GetEmployeeDtoArg;
import com.facishare.organization.adapter.api.model.biz.employee.result.GetEmployeeDtoResult;
import com.facishare.organization.adapter.api.service.EmployeeService;
import com.facishare.paas.license.Result.LicenseVersionResult;
import com.facishare.paas.license.arg.JudgeModuleArg;
import com.facishare.paas.license.arg.QueryProductArg;
import com.facishare.paas.license.common.LicenseContext;
import com.facishare.paas.license.exception.PaasMessage;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.license.pojo.JudgeModulePojo;
import com.facishare.paas.license.pojo.ModuleFlag;
import com.facishare.paas.license.pojo.ProductVersionPojo;
import com.facishare.uc.api.model.fscore.arg.GetSimpleEnterpriseArg;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Created on 2019/2/15
 * 处理企业微信在线订单事件
 */
@Slf4j
@Component
public class OrderManager {
    @Autowired
    private CorpManager corpManager;

    @Resource(name = "licenseClient")
    private LicenseClient licenseClient;

    @Autowired
    private QYWeixinManager qyWeixinManager;

    @Autowired
    private QyweixinOrderInfoDao qyweixinOrderInfoDao;

    @Autowired
    QyweixinCorpInfoDao qyweixinCorpInfoDao;

    @Autowired
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;

    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;

    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private FsOrderServiceProxy fsOrderServiceProxy;

    @Autowired
    private EnterpriseEditionService enterpriseEditionService;

    @ReloadableProperty("editionMapping")
    private String editionMapping;
    @ReloadableProperty("createOrderMapping")
    private String createOrderMapping;

    /**
     * 标识试用版本枚举的key
     */
    private static final String TRY_EDITION_ENUM_NAME = "tryEditionEnumName";

    public static Map<String, ProductDefine> productDefineMapping = Maps.newConcurrentMap();

    static {
        productDefineMapping.put("QYWX_TRY", ProductDefine.QYWX_TRY);
        productDefineMapping.put("QYWX_BASE", ProductDefine.QYWX_BASE);
        productDefineMapping.put("QYWX_PRO", ProductDefine.QYWX_PRO);
        productDefineMapping.put("QYWX_ESERVICE_TRY", ProductDefine.QYWX_ESERVICE_TRY);
        productDefineMapping.put("QYWX_ESERVICE_BASE", ProductDefine.QYWX_ESERVICE_BASE);
        productDefineMapping.put("STRENGTHEN_EDITION_UPGRADE_ENTERPRISE_EDITION", ProductDefine.STRENGTHEN_EDITION_UPGRADE_ENTERPRISE_EDITION);
        productDefineMapping.put("INTERCONNECT_APP_BASIC", ProductDefine.INTERCONNECT_APP_BASIC);
        productDefineMapping.put("ADVANCED_OUTWORK_APP", ProductDefine.ADVANCED_OUTWORK_APP);
        productDefineMapping.put("QYWX_ORDER_PLUS_TRY", ProductDefine.QYWX_ORDER_PLUS_TRY);
        productDefineMapping.put("QYWX_ORDER_PLUS_BASE", ProductDefine.QYWX_ORDER_PLUS_BASE);
        productDefineMapping.put("PARTNER_USER_FIRM_LIMIT_TRY", ProductDefine.PARTNER_USER_FIRM_LIMIT_TRY);
        productDefineMapping.put("PARTNER_USER_FIRM_LIMIT", ProductDefine.PARTNER_USER_FIRM_LIMIT);

    }


    /**
     * 下单成功时件处理，拉取订单信息并储存
     * @param appId 应用id
     * @param orderId 订单号
     * @return
     */
    public void openOrderEvent(String appId, String orderId){
        com.facishare.open.qywx.accountinner.result.Result<QyweixinOrderInfoRsp> qyweixinOrderInfoResult = qyWeixinManager.getOrderInfo(appId, orderId);
        if (!qyweixinOrderInfoResult.isSuccess()) {
            log.info("trace openOrderEvent error errormsg:{} ", qyweixinOrderInfoResult);
            return;
        }
        QyweixinOrderInfoRsp qyweixinOrderInfo = qyweixinOrderInfoResult.getData();
        log.info("trace openOrderEvent OrderInfo:{} ", qyweixinOrderInfo);
        QyweixinOrderInfoBo qyweixinOrderInfoBo = setQyweixinOrderInfoBo(qyweixinOrderInfo,
                ProcessingStatusEnum.NO_NEED_TO_PUSH_PROCESSED.getCode(), appId);
        //保存订单信息（存在则会更新）
        qyweixinOrderInfoDao.saveOrUpdateOrder(qyweixinOrderInfoBo);
    }

    /**
     * 修改订单事件处理，拉取新订单信息并储存，更新旧订单状态
     * @param appId 应用id
     * @param newOrderId 新订单号
     * @param oldOrderId 旧订单号
     * @return
     */
    public void changeOrderEvent(String appId, String newOrderId, String oldOrderId){
        //拉取新订单信息
        com.facishare.open.qywx.accountinner.result.Result<QyweixinOrderInfoRsp> qyweixinNewOrderInfoResult = qyWeixinManager.getOrderInfo(appId, newOrderId);
        if (!qyweixinNewOrderInfoResult.isSuccess()) {
            log.info("trace changeOrderEvent newOrderInfo error errormsg:{} ", qyweixinNewOrderInfoResult);
            return;
        }
        QyweixinOrderInfoRsp qyweixinNewOrderInfo = qyweixinNewOrderInfoResult.getData();
        log.info("trace changeOrderEvent newOrderInfo{} ", qyweixinNewOrderInfo);
        QyweixinOrderInfoBo newOrderInfoBo = setQyweixinOrderInfoBo(qyweixinNewOrderInfo,
                ProcessingStatusEnum.NO_NEED_TO_PUSH_PROCESSED.getCode(), appId);
        //保存新订单信息（存在则会更新）
        qyweixinOrderInfoDao.saveOrUpdateOrder(newOrderInfoBo);

        //拉取旧订单信息
        com.facishare.open.qywx.accountinner.result.Result<QyweixinOrderInfoRsp> qyweixinOldOrderInfoResult = qyWeixinManager.getOrderInfo(appId,oldOrderId);
        if (!qyweixinOldOrderInfoResult.isSuccess()) {
            log.error("trace changeOrderEvent oldOrderInfo error errormsg:{} ", qyweixinOldOrderInfoResult);
            return;
        }
        QyweixinOrderInfoRsp qyweixinOldOrderInfo = qyweixinNewOrderInfoResult.getData();
        log.info("trace changeOrderEvent oldOrderInfo:{} ", qyweixinOldOrderInfo);
        QyweixinOrderInfoBo oldOrderInfoBo = setQyweixinOrderInfoBo(qyweixinNewOrderInfo,
                ProcessingStatusEnum.NO_NEED_TO_PUSH_PROCESSED.getCode(), appId);
        //更新旧订单信息
        qyweixinOrderInfoDao.saveOrUpdateOrder(oldOrderInfoBo);
    }

    /**
     * 支付成功事件处理
     * @param appId 应用id
     * @param orderId 订单号
     * @return
     */
    public void payForAppSuccessEvent(String appId, String orderId){
        com.facishare.open.qywx.accountinner.result.Result<QyweixinOrderInfoRsp> qyweixinOrderInfoResult = qyWeixinManager.getOrderInfo(appId,orderId);
        if (!qyweixinOrderInfoResult.isSuccess()) {
            log.info("trace payForAppSuccessEvent error errormsg:{} ", qyweixinOrderInfoResult);
            return;
        }
        QyweixinOrderInfoRsp qyweixinOrderInfo = qyweixinOrderInfoResult.getData();
        //查询是否存在企业微信信息，若存在则存储并推送订单信息
        log.info("trace payForAppSuccessEvent OrderInfo{} ", qyweixinOrderInfo);
        QyweixinCorpInfoBo qyweixinCorpInfoBo =new QyweixinCorpInfoBo();
        qyweixinCorpInfoBo.setCorpId(qyweixinOrderInfo.getPaid_corpid());
        List<QyweixinCorpInfoBo> qyweixinCorpInfo = qyweixinCorpInfoDao.findByEntity(qyweixinCorpInfoBo);

        if (null == qyweixinCorpInfo || qyweixinCorpInfo.isEmpty()) {
            log.info("trace payForAppSuccessEvent getFsEaByOutEa warn corp info not exist, corpId:{} ", qyweixinOrderInfo.getPaid_corpid());

            //未存在企业微信信息更新订单信息，置处理状态为  未推送未处理
            QyweixinOrderInfoBo qyweixinOrderInfoBo = setQyweixinOrderInfoBo(qyweixinOrderInfo,
                    ProcessingStatusEnum.NOT_PUSHED_UNPROCESSED.getCode(), appId);
            qyweixinOrderInfoDao.saveOrUpdateOrder(qyweixinOrderInfoBo);
            log.info("trace payForAppSuccessEvent save success, orderId:{} ", qyweixinOrderInfoBo.getOrderId());
        } else {
            //old:有这么一种场景，客户的是原生版企业，但是进行了代客下单，我们也需要推送订单到crm
            //new:原生版企业代客下单，不需要推送到crm
            if(qyweixinOrderInfo.getOrder_from() !=null && qyweixinOrderInfo.getOrder_from() != 0) {
                //代付订单，不用传递到CRM中
                //此类订单统一设为无需推送已处理
                QyweixinOrderInfoBo qyweixinOrderInfoBo = setQyweixinOrderInfoBo(qyweixinOrderInfo,
                        ProcessingStatusEnum.NO_NEED_TO_PUSH_PROCESSED.getCode(), appId);
                int updateOrder = qyweixinOrderInfoDao.saveOrUpdateOrder(qyweixinOrderInfoBo);
                log.info("trace payForAppSuccessEvent to saveOrUpdateOrder,qyweixinOrderInfoBo={},updateOrder={}", qyweixinOrderInfoBo, updateOrder);
                //代付订单，需要下一个【企微接口代缴费】订单，主要起提醒作用
                this.createInterfaceOrder(qyweixinOrderInfo);
                return;
            }

            //若原来库中已有企业信息后来取消授权了则不能拉取企业信息，也更新置订单处理状态为  未推送未处理
            com.facishare.open.qywx.accountinner.result.Result<QyweixinGetCorptokenRsp> corpAccessTokenRspResult = qyWeixinManager.getCorpAccessTokenForCheck(appId, qyweixinOrderInfo.getPaid_corpid());
            if(!corpAccessTokenRspResult.isSuccess() && corpAccessTokenRspResult.getCode().equals("40084")) {
                QyweixinOrderInfoBo qyweixinOrderInfoBo = setQyweixinOrderInfoBo(qyweixinOrderInfo,
                        ProcessingStatusEnum.NOT_PUSHED_UNPROCESSED.getCode(), appId);
                qyweixinOrderInfoDao.saveOrUpdateOrder(qyweixinOrderInfoBo);
                log.info("trace payForAppSuccessEvent invalid authorizer permanent code and update order, orderId:{} ", qyweixinOrderInfoBo.getOrderId());
                return;
            }

            //获取企业授权信息，获取可见范围等信息
            com.facishare.open.qywx.accountinner.result.Result<QyweixinGetAuthInfoRsp> corpAuthInfoResult = qyWeixinManager.getCorpInfo(qyweixinCorpInfo.get(0).getCorpId(), appId);
            QyweixinGetAuthInfoRsp corpAuthInfo = corpAuthInfoResult.getData();
            //获取管理员信息
            com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> userInfoResult = qyWeixinManager.getUserInfo(appId,
                    qyweixinCorpInfo.get(0).getCorpId(),
                    qyweixinCorpInfo.get(0).getUserId());

            String userId = null;
            String userName = null;
            String avatar = null;

            if(!userInfoResult.isSuccess() || ObjectUtils.isEmpty(userInfoResult.getData())) {
                userInfoResult = qyWeixinManager.getUserInfo(appId,
                        qyweixinCorpInfo.get(0).getCorpId(),
                        qyweixinOrderInfo.getOperator_id());
                if(!userInfoResult.isSuccess() || ObjectUtils.isEmpty(userInfoResult.getData())) {
                    List<QyweixinAccountEmployeeMapping> employeeMappingList = qyweixinAccountBindInnerService.queryAccountBindByOutEa(
                            qyweixinOrderInfo.getPaid_corpid(),
                            appId,0);
                    if(employeeMappingList!=null && employeeMappingList.size()>0) {
                        //从员工映射表中取一条员工状态正常的员工数据作为订单
                        QyweixinAccountEmployeeMapping employeeMapping = employeeMappingList.get(0);

                        List<String> splitList = Splitter.on(".").splitToList(employeeMapping.getFsAccount());
                        int employeeId = Integer.valueOf(splitList.get(2));


                        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(
                                qyweixinOrderInfo.getPaid_corpid());

                        if(CollectionUtils.isNotEmpty(enterpriseMappingList)) {
                            QyweixinAccountEnterpriseMapping enterpriseMapping = enterpriseMappingList.get(0);
                            GetEmployeeDtoArg employeeDtoArg = new GetEmployeeDtoArg();
                            employeeDtoArg.setEnterpriseId(eieaConverter.enterpriseAccountToId(enterpriseMapping.getFsEa()));
                            employeeDtoArg.setEmployeeId(employeeId);

                            GetEmployeeDtoResult employeeDto = employeeService.getEmployeeDto(employeeDtoArg);

                            userId = employeeDto.getEmployee().getEmployeeId()+"";
                            userName = employeeDto.getEmployee().getName();
                            //这种情况，下需要头像
                            avatar = null;
                        }
                    } else {
                        throw new RuntimeException("应用可见范围内没有正常状态的员工");
                    }
                } else {
                    QyweixinUserDetailInfoRsp userInfo = userInfoResult.getData();
                    userId = userInfo.getUserid();
                    userName = userInfo.getName();
                    avatar = userInfo.getAvatar();
                }
            } else {
                QyweixinUserDetailInfoRsp userInfo = userInfoResult.getData();
                userId = userInfo.getUserid();
                userName = userInfo.getName();
                avatar = userInfo.getAvatar();
            }

            log.info("OrderManager.payForAppSuccessEvent,userId={},userName={},avatar={}", userId,userName,avatar);

            //合并发送开通企业通知消息
            QyweixinEnterpriseOrder qyweixinAddEnterprise = new QyweixinEnterpriseOrder();
            qyweixinAddEnterprise.setUserId(userId);
            qyweixinAddEnterprise.setUserName(userName);
            qyweixinAddEnterprise.setAvatar(avatar);
            qyweixinAddEnterprise.setCorpId(corpAuthInfo.getAuth_corp_info().getCorpid());
            qyweixinAddEnterprise.setCorpName(corpAuthInfo.getAuth_corp_info().getCorp_name());
            qyweixinAddEnterprise.setAppId(appId);

            //设置可见范围 （通讯录应用没有可见范围，做判空处理）
            QyweixinAgentPrivilege qyweixinAgentPrivilege = new QyweixinAgentPrivilege();
            if(null != corpAuthInfo.getAuth_info().getAgent() && ! corpAuthInfo.getAuth_info().getAgent().isEmpty()){
                QyweixinAgentRsp qyweixinAgentRsp = corpAuthInfo.getAuth_info().getAgent().get(0);
                qyweixinAgentPrivilege.setAllowParty(qyweixinAgentRsp.getPrivilege().getAllow_party());
                qyweixinAgentPrivilege.setAllowUser(qyweixinAgentRsp.getPrivilege().getAllow_user());
                qyweixinAgentPrivilege.setAllowTag(qyweixinAgentRsp.getPrivilege().getAllow_tag());
            }
            qyweixinAddEnterprise.setPrivilege(qyweixinAgentPrivilege);

            //存在企业微信信息则合并订单和企业信息推送并置订单处理状态为 已推送已处理
            QyweixinOrderInfoBo qyweixinOrderInfoBo = setQyweixinOrderInfoBo(qyweixinOrderInfo,
                    ProcessingStatusEnum.PUSHED_PROCESSED.getCode(), appId);

            //置订单处理状态为 已推送已处理
            qyweixinOrderInfoDao.saveOrUpdateOrder(qyweixinOrderInfoBo);
            log.info("trace payForAppSuccessEvent save success, qyweixinOrderInfoBo:{} ", qyweixinOrderInfoBo);
            // Q的消息和DB存储顺序不能改变

            QyweixinEnterpriseOrder qyweixinEnterpriseOrderEvent = convertQyweixinOrderEvent(qyweixinAddEnterprise, qyweixinOrderInfoBo);
            openEnterprise(qyweixinEnterpriseOrderEvent);

//            String TAG_ENTERPRISE = "qywx_enterprise_order";
//
//            Message msg = new Message();
//            msg.setTags(TAG_ENTERPRISE);
//            msg.setBody(qyweixinEnterpriseOrderEvent.toProto());
//            SendResult sendResult = qywxOrderEventNotifyMQSender.send(msg);
//            log.info("trace payForAppSuccessEvent from qywx send mq msg sendResult:{}, message:{}, body:{}", sendResult, msg, JSONObject.toJSONString(qyweixinEnterpriseOrderEvent));
        }
    }

    public void openEnterprise(QyweixinEnterpriseOrder enterpriseOrder) {
        LogUtils.info("OrderManager.openEnterprise,enterpriseOrder={}", JSONObject.toJSONString(enterpriseOrder));
//        String TAG_ENTERPRISE = "qywx_enterprise_order";
//
//        Message msg = new Message();
//        msg.setTags(TAG_ENTERPRISE);
//        msg.setBody(enterpriseOrder.toProto());
//        SendResult sendResult = qywxOrderEventNotifyMQSender.send(msg);
//        log.info("OrderManager.openEnterprise,sendResult={}, message={}, body={}",
//                sendResult,
//                msg,
//                JSONObject.toJSONString(enterpriseOrder));

//        com.facishare.open.qywx.accountbind.result.Result<String> accountBindResult =
//                qyweixinAccountBindService.outEaToFsEa(SourceTypeEnum.QYWX.getSourceType(), enterpriseOrder.getCorpId());

        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEnterpriseMapping>> accountBindResult =
                qyweixinAccountBindService.selectEnterpriseBind(SourceTypeEnum.QYWX.getSourceType(), enterpriseOrder.getCorpId());
        LogUtils.info("OrderManager.openEnterprise,outEaToFsEa,accountBindResult={}", accountBindResult);

        String fsEa;
        if(CollectionUtils.isEmpty(accountBindResult.getData()) || accountBindResult.getData().get(0).getStatus() == 100) {
            //查询客户是否存在
            Result<List<ObjectData>> listResult = fsOrderServiceProxy.queryCustomer(ConfigCenter.MASTER_EA, enterpriseOrder.getCorpId());
            if(CollectionUtils.isNotEmpty(listResult.getData())) {
                if(CollectionUtils.isEmpty(accountBindResult.getData())) {
                    //有客户，没有绑定记录，查询企业是否创建成功
                    fsEa = listResult.getData().get(0).get(ConfigCenter.CRM_CUSTOM_ENTERPRISE_ID_FILE).toString();
                    try {
                        GetSimpleEnterpriseArg arg = new GetSimpleEnterpriseArg();
                        arg.setEnterpriseAccount(fsEa);
                        enterpriseEditionService.getSimpleEnterprise(arg);
                        corpManager.enterpriseBind(enterpriseOrder.getCorpId(),fsEa,0);
                        corpManager.employeeBind(enterpriseOrder.getCorpId(),fsEa,enterpriseOrder.getAppId(),enterpriseOrder.getUserId(),0);
                    } catch(Exception e) {
                        corpManager.enterpriseBind(enterpriseOrder.getCorpId(),fsEa,100);
                        corpManager.employeeBind(enterpriseOrder.getCorpId(),fsEa,enterpriseOrder.getAppId(),enterpriseOrder.getUserId(),100);
                    }
                } else {
                    fsEa = accountBindResult.getData().get(0).getFsEa();
                }
            } else {
                //创建客户
                String customerName = enterpriseOrder.getCorpName();
                fsEa = EnterpriseUtils.genEA(customerName,"qywx");
                LogUtils.info("OrderManager.openEnterprise,fsEa={}", fsEa);

                CreateCustomerArg customerArg = CreateCustomerArg.builder()
                        .source(CustomerSourceEnum.QYWX.getSource())
                        .outEid(enterpriseOrder.getCorpId())
                        .managerName(enterpriseOrder.getUserName())
                        .managerMobile(null)//获取不到手机号
                        .enterpriseName(customerName)
                        .enterpriseAccount(fsEa)
                        .build();
                Result<Void> createCustomerResult = fsOrderServiceProxy.createCustomer(customerArg);
                LogUtils.info("OrderManager.openEnterprise,createCustomer,customerArg={},createCustomerResult={}", customerArg,createCustomerResult);
                if(!createCustomerResult.isSuccess()) {
                    throw new RuntimeException("创建客户失败，客户名称="+customerName+",outEid="+customerArg.getOutEid());
                }
                corpManager.enterpriseBind(enterpriseOrder.getCorpId(),fsEa,100);
                corpManager.employeeBind(enterpriseOrder.getCorpId(),fsEa,enterpriseOrder.getAppId(),enterpriseOrder.getUserId(),100);
            }
        } else {
            fsEa = accountBindResult.getData().get(0).getFsEa();
        }

        //客户不存在，先创建客户
//        if(StringUtils.isEmpty(fsEa)) {
//            String customerName = enterpriseOrder.getCorpName();
//            fsEa = EnterpriseUtils.genEA(customerName,"qywx");
//            LogUtils.info("OrderManager.openEnterprise,fsEa={}", fsEa);
//
//            CreateCustomerArg customerArg = CreateCustomerArg.builder()
//                    .source(CustomerSourceEnum.QYWX.getSource())
//                    .outEid(enterpriseOrder.getCorpId())
//                    .managerName(enterpriseOrder.getUserName())
//                    .managerMobile(null)//获取不到手机号
//                    .enterpriseName(customerName)
//                    .enterpriseAccount(fsEa)
//                    .build();
//            Result<Void> createCustomerResult = fsOrderServiceProxy.createCustomer(customerArg);
//            LogUtils.info("OrderManager.openEnterprise,createCustomer,customerArg={},createCustomerResult={}", customerArg,createCustomerResult);
//            if(!createCustomerResult.isSuccess()) {
//                throw new RuntimeException("创建客户失败，客户名称="+customerName+",outEid="+customerArg.getOutEid());
//            }
//            corpManager.enterpriseBind(enterpriseOrder.getCorpId(),fsEa,100);
//            corpManager.employeeBind(enterpriseOrder.getCorpId(),fsEa,enterpriseOrder.getAppId(),enterpriseOrder.getUserId(),100);
//        }

        Integer allResourceCount = enterpriseOrder.getUserCount();
        Long beginTime = null;
        Long endTime = null;
        Long price = enterpriseOrder.getPrice();
        String orderId = enterpriseOrder.getOrderId();
        ProductDefine productDefine = null;
        Integer orderType = CreateCrmOrderArg.CrmOrderDetailInfo.ORDER_TYPE_BUY;
        if(enterpriseOrder.getOrderType() == OrderTypeEnum.TRYOUT.getCode()) {
            productDefine = getProductDefine(enterpriseOrder.getAppId(), TRY_EDITION_ENUM_NAME);
            //试用版创建10000个CRM用户
            allResourceCount = ConfigCenter.createCrmAccount;
            orderType = CreateCrmOrderArg.CrmOrderDetailInfo.ORDER_TYPE_TRY;
            //试用时，开始begin time为空
            beginTime = DateUtils.date2Timestamp(DateUtils.nowDate());
            endTime = DateUtils.date2Timestamp(enterpriseOrder.getEndTime());
            //试用版orderId字段为空，这里用一个组合ID作为订单ID
            orderId = "qywx_" + enterpriseOrder.getCorpId() + "_" + enterpriseOrder.getAppId();
            price = 0L;
        } else {
            productDefine = getProductDefine(enterpriseOrder.getAppId(), enterpriseOrder.getEditionId());

            if(StringUtils.isEmpty(enterpriseOrder.getBeginTime())) {
                beginTime = DateUtils.date2Timestamp(DateUtils.nowDate());
            } else {
                beginTime = DateUtils.date2Timestamp(enterpriseOrder.getBeginTime());
            }
            if(StringUtils.isEmpty(enterpriseOrder.getEndTime())) {
                //默认再给7天
                LogUtils.info("OrderManager.openEnterprise,createCustomer,set endTime,outEa={}", enterpriseOrder.getCorpId());
                endTime = LocalDateTime.now().plusDays(7).toInstant(ZoneOffset.of("+8")).getEpochSecond();
            } else {
                endTime = DateUtils.date2Timestamp(enterpriseOrder.getEndTime());
            }
        }
        LogUtils.info("OrderManager.openEnterprise,productDefine={}", productDefine);

        Integer quality = 1;
        // 企业微信标准版，单套资源数是5个配额，个数需要除以5
        if (Objects.equals(ProductDefine.QYWX_TRY, productDefine) || Objects.equals(ProductDefine.QYWX_BASE, productDefine)) {
            quality = allResourceCount / 5;
        } else {
            //其他版本需要跟allResourceCount数量一致
            quality = allResourceCount;
        }

        LogUtils.info("OrderManager.openEnterprise,quality={},allResourceCount={}", quality,allResourceCount);

        List<String> productIdList = getProductIdList(enterpriseOrder.getAppId(),enterpriseOrder.getEditionId());
        List<CreateCrmOrderArg> orderArgList = new ArrayList<>();
        for(String productId : productIdList) {
            CreateCrmOrderArg.CrmOrderDetailInfo orderDetailInfo = CreateCrmOrderArg.CrmOrderDetailInfo.builder()
                    .enterpriseAccount(fsEa)
                    .orderId(orderId)
                    .orderTime(beginTime)
                    .orderTpye(orderType)
                    .build();

            //订单金额单位是 分，需要转换成 元
            BigDecimal orderAmount = BigDecimal.valueOf(price / 100.0);
            CreateCrmOrderArg.CrmOrderProductInfo orderProductInfo = CreateCrmOrderArg.CrmOrderProductInfo.builder()
                    .beginTime(beginTime)
                    .endTime(endTime)
                    .allResourceCount(allResourceCount)
                    .quantity(quality)
                    .orderAmount(orderAmount + "")
                    .productId(productId)
                    .build();

            CreateCrmOrderArg orderArg = CreateCrmOrderArg.builder()
                    .crmOrderDetailInfo(orderDetailInfo)
                    .crmOrderProductInfo(orderProductInfo)
                    .build();
            orderArgList.add(orderArg);
        }

        LogUtils.info("OrderManager.openEnterprise,batchCreateCrmOrder,orderArgList={}", orderArgList);
        Result<List<CrmOrderMappingModel>> batchCreateCrmOrderResult = fsOrderServiceProxy.batchCreateCrmOrder(orderArgList);
        LogUtils.info("OrderManager.openEnterprise,batchCreateCrmOrder,batchCreateCrmOrderResult={}", batchCreateCrmOrderResult);
        for(CrmOrderMappingModel orderMappingModel : batchCreateCrmOrderResult.getData()) {
            if(StringUtils.isEmpty(orderMappingModel.getOrderId())) {
                //批量创建订单，有可能部分订单会创建失败，实际上应该不会发生这种情况
                LogUtils.info("OrderManager.openEnterprise,batchCreateCrmOrder,order create failed,productId={}", orderMappingModel.getProductId());
            }
        }
    }

    public ProductDefine getProductDefine(String appId, String editionId) {
        Map<String, Map<String, String>> editionMappingMap = new Gson().fromJson(editionMapping, new TypeToken<Map<String, Map<String, String>>>() {
        }.getType());
        Map<String, String> versionMapping = editionMappingMap.get(appId);
        if (MapUtils.isEmpty(versionMapping)) {
            log.error("get versionMapping failed. appId:{}, editionId:{}, editionMappingMap:{}", appId, editionId, editionMappingMap);
            return null;
        }
        String productDefineName = versionMapping.get(editionId);
        if (StringUtils.isBlank(productDefineName)) {
            log.error("get productDefineName failed. appId:{}, editionId:{}, editionMappingMap:{}", appId, editionId, editionMappingMap);
            return null;
        }
        log.info("getProductDefine success. appId:{}, editionId:{}", appId, editionId);
        return productDefineMapping.get(productDefineName);
    }

    /**
     * 从配置中心动态获取订单和版本的关系
     * 比如：createOrderMapping={"wxdeb7e0658a828754":{"sp913697ec091cbc88":["QYWX_TRY"],"sp3be0379998a25e17":["QYWX_BASE"],"sp2ffe2d52bc5036be":["QYWX_PRO"],"spde46aa0359b3f455": ["615fb46c22c52f0001d4e5ee"]},"wx3f3c789235f952ce":{"sp4a69ed6fa6c1e622":["QYWX_TRY","QYWX_ESERVICE_TRY","INTERCONNECT_APP_BASIC","ADVANCED_OUTWORK_APP"],"sp66c2a22f80bd86e4":["QYWX_BASE","QYWX_ESERVICE_BASE","INTERCONNECT_APP_BASIC","ADVANCED_OUTWORK_APP"]}}
     */
    public List<String> getProductIdList(String appId, String editionId) {
        List<String> productIdList = Lists.newArrayList();
        Map<String, Map<String, List<String>>> createOrderMappingMap = new Gson().fromJson(createOrderMapping, new TypeToken<Map<String, Map<String, List<String>>>>() {
        }.getType());
        Map<String, List<String>> editionMap = createOrderMappingMap.get(appId);
        if (MapUtils.isEmpty(editionMap)) {
            return Lists.newArrayList();
        }
        List<String> productDefineNameList = editionMap.get(editionId);
        if (CollectionUtils.isEmpty(productDefineNameList)) {
            return Lists.newArrayList();
        }
        productDefineNameList.forEach(name -> { //name可以 @Class {ProductDefine} 的变量名也可以是产品ID，比如旗舰版的产品ID
            ProductDefine productDefine = productDefineMapping.get(name);
            if (productDefine != null) {
                productIdList.add(productDefine.getProductId());
            } else {
                productIdList.add(name);//name = 产品ID
            }
        });
        return productIdList;
    }

    /**
     * 订单退款事件处理
     * 注：暂不支持退款，消息不会向外推送，只会保存订单状态为 未推送已处理，逻辑先保留以便日后之需
     * @param appId
     * @param orderId
     * @return
     */
    public void refundEvent(String appId, String orderId){
        com.facishare.open.qywx.accountinner.result.Result<QyweixinOrderInfoRsp> qyweixinOrderInfoResult = qyWeixinManager.getOrderInfo(appId,orderId);
        if (!qyweixinOrderInfoResult.isSuccess()) {
            log.info("trace refundEvent error errormsg{} ", qyweixinOrderInfoResult);
            return;
        }
        QyweixinOrderInfoRsp qyweixinOrderInfo = qyweixinOrderInfoResult.getData();
        log.info("trace refundEvent orderInfo{} ", qyweixinOrderInfo);

        //查询是否存在企业微信信息，若不存在则只更新退款的订单信息
        QyweixinCorpInfoBo qyweixinCorpInfoBo =new QyweixinCorpInfoBo();
        qyweixinCorpInfoBo.setCorpId(qyweixinOrderInfo.getPaid_corpid());
        List<QyweixinCorpInfoBo> qyweixinCorpInfo = qyweixinCorpInfoDao.findByEntity(qyweixinCorpInfoBo);

        if (null == qyweixinCorpInfo || qyweixinCorpInfo.isEmpty()) {
            log.info("trace refundEvent getFsEaByOutEa warn corp info not exist, corpId:{} ", qyweixinOrderInfo.getPaid_corpid());

            //不存在企业信息 更新退款的订单信息，置处理状态为  未推送已处理
            QyweixinOrderInfoBo qyweixinOrderInfoBo = setQyweixinOrderInfoBo(qyweixinOrderInfo,
                    ProcessingStatusEnum.NOT_PUSHED_PROCESSED.getCode(), appId);
            qyweixinOrderInfoDao.saveOrUpdateOrder(qyweixinOrderInfoBo);
            log.info("trace refundEvent save success, orderId{} ", qyweixinOrderInfoBo.getOrderId());
        } else {
            //更新退款的订单信息,置订单处理状态为 未推送已处理
            QyweixinOrderInfoBo qyweixinOrderInfoBo = setQyweixinOrderInfoBo(qyweixinOrderInfo,
                    ProcessingStatusEnum.NOT_PUSHED_PROCESSED.getCode(), appId);
            qyweixinOrderInfoDao.saveOrUpdateOrder(qyweixinOrderInfoBo);
            log.info("trace refundEvent save success, orderId{}: ", qyweixinOrderInfoBo.getOrderId());
        }

    }


    /**
     * 将从企业微信端取回的数据进行转换
     * @param qyweixinOrderInfo
     * @param processingStatus 处理状态
     * @return
     */
    public QyweixinOrderInfoBo setQyweixinOrderInfoBo(QyweixinOrderInfoRsp qyweixinOrderInfo, int processingStatus, String appId){
        QyweixinOrderInfoBo qyweixinOrderInfoBo =new QyweixinOrderInfoBo();
        qyweixinOrderInfoBo.setOrderId(qyweixinOrderInfo.getOrderid());
        qyweixinOrderInfoBo.setAppId(appId);
        qyweixinOrderInfoBo.setOrderType(qyweixinOrderInfo.getOrder_type());
        qyweixinOrderInfoBo.setEditionId(qyweixinOrderInfo.getEdition_id());
        qyweixinOrderInfoBo.setEditionName(qyweixinOrderInfo.getEdition_name());
        qyweixinOrderInfoBo.setPaidCorpid(qyweixinOrderInfo.getPaid_corpid());
        qyweixinOrderInfoBo.setOperatorId(qyweixinOrderInfo.getOperator_id());
        qyweixinOrderInfoBo.setOrderStatus(qyweixinOrderInfo.getOrder_status());
        qyweixinOrderInfoBo.setOrderPeriod(qyweixinOrderInfo.getOrder_period());
        qyweixinOrderInfoBo.setPrice(qyweixinOrderInfo.getPrice());
        qyweixinOrderInfoBo.setPaidTime(qyweixinOrderInfo.getPaid_time());
        qyweixinOrderInfoBo.setBeginTime(qyweixinOrderInfo.getBegin_time());
        qyweixinOrderInfoBo.setEndTime(qyweixinOrderInfo.getEnd_time());
        qyweixinOrderInfoBo.setOrderTime(qyweixinOrderInfo.getOrder_time());
        qyweixinOrderInfoBo.setSuiteId(qyweixinOrderInfo.getSuiteid());
        qyweixinOrderInfoBo.setUserCount(qyweixinOrderInfo.getUser_count());
        qyweixinOrderInfoBo.setOrderFrom(qyweixinOrderInfo.getOrder_from());
        qyweixinOrderInfoBo.setOperatorCorpid(qyweixinOrderInfo.getOperator_corpid());
        qyweixinOrderInfoBo.setProcessingStatus(processingStatus);

        return qyweixinOrderInfoBo;
    }

    /**
     * 合并企业和订单信息事件推送参数
     * @param qyweixinOrderInfoBo
     * @return
     */
    public QyweixinEnterpriseOrder convertQyweixinOrderEvent(QyweixinEnterpriseOrder qyweixinAddEnterprise, QyweixinOrderInfoBo qyweixinOrderInfoBo){
        //换算时间并格式化为yyyy/MM/dd
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");

        String beginTime = formatter.format(LocalDateTime.ofInstant(Instant.ofEpochSecond(qyweixinOrderInfoBo.getBeginTime()), ZoneId.of("Asia/Shanghai")));
        String endTime = formatter.format(LocalDateTime.ofInstant(Instant.ofEpochSecond(qyweixinOrderInfoBo.getEndTime()), ZoneId.of("Asia/Shanghai")));

        qyweixinAddEnterprise.setOrderId(qyweixinOrderInfoBo.getOrderId());
        qyweixinAddEnterprise.setOperatorId(qyweixinOrderInfoBo.getOperatorId());
        qyweixinAddEnterprise.setOrderType(qyweixinOrderInfoBo.getOrderType());
        qyweixinAddEnterprise.setBeginTime(beginTime);
        qyweixinAddEnterprise.setEndTime(endTime);
        qyweixinAddEnterprise.setUserCount(getUserCount(qyweixinOrderInfoBo));
        qyweixinAddEnterprise.setPrice(qyweixinOrderInfoBo.getPrice());
        qyweixinAddEnterprise.setEditionId(qyweixinOrderInfoBo.getEditionId());

        return qyweixinAddEnterprise;
    }

    /**
     * 处理购买人数。分为'增购'和'其他'
     *
     * @param qyweixinOrderInfoBo qyweixinOrderInfo
     * @return 购买人数
     */
    private int getUserCount(QyweixinOrderInfoBo qyweixinOrderInfoBo) {
        int userCount = qyweixinOrderInfoBo.getUserCount();
        String appId = qyweixinOrderInfoBo.getAppId();
        log.info("getUserCount start. qyweixinOrderInfoBo:{}", qyweixinOrderInfoBo);
        // 扩容订单增购人数需要特殊处理
        if (OrderTypeEnum.EXPANSION_ORDER.getCode().equals(qyweixinOrderInfoBo.getOrderType())) {
            String paidCorpId = qyweixinOrderInfoBo.getPaidCorpid();
            String orderId = qyweixinOrderInfoBo.getOrderId();
            return getExpansionUserCount(paidCorpId, orderId, appId, userCount);
        }
        return userCount;
    }

    /**
     * 订单类型为“扩容”时，增购人数为：此次人数 - 上次订单购买人数
     *
     * @param paidCorpId     paidCorpId
     * @param currentOrderId 当前订单ID
     * @param userCount      当前人数
     * @return 增购的数量
     */
    private int getExpansionUserCount(String paidCorpId, String currentOrderId, String appId, int userCount) {
        QyweixinOrderInfoBo latestOrderInfo = qyweixinOrderInfoDao.getLatestPaidOrder(paidCorpId, appId, currentOrderId);
        log.info("getExpansionUserCount start. paidCorpId:{}. currentOrderId:{}. latestOrderInfo:{}", paidCorpId, currentOrderId, latestOrderInfo);
        if (Objects.isNull(latestOrderInfo)) {
            log.warn("找不到已付费订单，认为是直接扩容订单");
            return userCount;
        }
        return userCount - latestOrderInfo.getUserCount();
    }

    private void createInterfaceOrder(QyweixinOrderInfoRsp orderInfo){
        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(
                orderInfo.getPaid_corpid());
        if(CollectionUtils.isEmpty(enterpriseMappingList)) {
            log.info("OrderManager.createInterfaceOrder,corpId={} is null.", orderInfo.getPaid_corpid());
            return;
        }
        CreateCrmOrderArg.CrmOrderDetailInfo orderDetailInfo = CreateCrmOrderArg.CrmOrderDetailInfo.builder()
                .enterpriseAccount(enterpriseMappingList.get(0).getFsEa())
                .orderId(orderInfo.getOrderid())
                .orderTime(orderInfo.getPaid_time() * 1000L)
                .orderTpye(3)
                .build();

        CreateCrmOrderArg.CrmOrderProductInfo orderProductInfo = CreateCrmOrderArg.CrmOrderProductInfo.builder()
                .beginTime(orderInfo.getBegin_time() * 1000L)
                .endTime(orderInfo.getEnd_time() * 1000L)
                .quantity(1)
                .allResourceCount(orderInfo.getUser_count())
                .orderAmount("0")
                .productId(ConfigCenter.INTERFACE_PRODUCT_ID)
                .build();

        CreateCrmOrderArg orderArg = CreateCrmOrderArg.builder()
                .crmOrderDetailInfo(orderDetailInfo)
                .crmOrderProductInfo(orderProductInfo)
                .build();
        Result<Void> crmInterfaceOrder = fsOrderServiceProxy.createCrmOrder(orderArg);
        log.info("OrderManager.createInterfaceOrder,orderArg={},crmInterfaceOrder={}.", orderArg, crmInterfaceOrder);
    }

    public Result<Void> buyConnector(String fsEa,
                                                                                        String orderAmount,
                                                                                        Long beginTime,
                                                                                        Long endTime) {
        long orderTime = System.currentTimeMillis();
        if(orderTime >= endTime) {
            return Result.newError(com.facishare.open.order.contacts.proxy.api.result.ResultCodeEnum.PARAM_ILLEGAL);
        }
        BigDecimal amount = BigDecimal.valueOf(Double.valueOf(orderAmount));
        CreateCrmOrderArg.CrmOrderProductInfo orderProductInfo = new CreateCrmOrderArg.CrmOrderProductInfo();
        orderProductInfo.setProductId(ConfigCenter.qywxConnectorProductId);
        orderProductInfo.setQuantity(1);
        orderProductInfo.setOrderAmount(orderAmount);
        orderProductInfo.setAllResourceCount(10000);
        orderProductInfo.setBeginTime(beginTime);
        orderProductInfo.setEndTime(endTime);

        CreateCrmOrderArg.CrmOrderDetailInfo orderDetailInfo = new CreateCrmOrderArg.CrmOrderDetailInfo();
        orderDetailInfo.setEnterpriseAccount(fsEa);
        orderDetailInfo.setOrderTime(orderTime);
        orderDetailInfo.setOrderTpye(amount.compareTo(BigDecimal.ZERO)==0 ? 3 : 1);
        orderDetailInfo.setOrderId("erpdss_qywx_connector_"+System.currentTimeMillis());

        CreateCrmOrderArg orderArg = new CreateCrmOrderArg();
        orderArg.setCrmOrderProductInfo(orderProductInfo);
        orderArg.setCrmOrderDetailInfo(orderDetailInfo);

        LogUtils.info("OrderManager.buyConnector,orderArg={}", JSONObject.toJSONString(orderArg));
        return fsOrderServiceProxy.createCrmOrder(orderArg);
    }

    public ModuleFlag judgeQywxConnectorModule(String tenantId) {
        JudgeModuleArg judgeModuleArg = new JudgeModuleArg();
        LicenseContext context = new LicenseContext();
        context.setAppId("CRM");
        context.setTenantId(tenantId);
        judgeModuleArg.setContext(context);
        judgeModuleArg.setModuleCodes(Lists.newArrayList("wecom_data_sync_app"));

        ModuleFlag moduleFlag = null;
        try {
            com.facishare.paas.license.common.Result result = licenseClient.judgeModule(judgeModuleArg);
            log.info("OrderManager.judgeQywxConnectorModule,arg:{}.result:{}", judgeModuleArg, result);
            if (result.getErrCode() == PaasMessage.SUCCESS.getCode()) {
                List<ModuleFlag> collect = ((JudgeModulePojo) result.getResult()).getModuleFlags();
                if(CollectionUtils.isNotEmpty(collect)) {
                    moduleFlag = collect.get(0);
                }
            }
        } catch (Exception e) {
            log.error("OrderManager.judgeQywxConnectorModule,exception", e.getMessage());
        }

        log.info("OrderManager.judgeQywxConnectorModule,moduleFlag={}",moduleFlag);

        return moduleFlag;
    }

    public ProductVersionPojo queryFirstValidSCRMLicense(String tenantId) {
        LicenseContext context = new LicenseContext();
        context.setAppId("CRM");
        context.setTenantId(tenantId);
        context.setUserId("-10000");

        QueryProductArg queryProductArg = new QueryProductArg();
        queryProductArg.setLicenseContext(context);
        LicenseVersionResult result = licenseClient.queryProductVersion(queryProductArg);
        List<ProductVersionPojo> productVersionPojoList = new ArrayList<>();
        if(result!=null && result.getErrCode()==0 && CollectionUtils.isNotEmpty(result.getResult())) {
            for(ProductVersionPojo pojo : result.getResult()) {
                if(StringUtils.startsWithIgnoreCase(pojo.getCurrentVersion(),"scrm")) {
                    if(pojo.getExpiredTime() > System.currentTimeMillis()) {
                        productVersionPojoList.add(pojo);
                    }
                }
            }
        }
        if(CollectionUtils.isNotEmpty(productVersionPojoList)) {
            productVersionPojoList = productVersionPojoList.stream()
                    .sorted(Comparator.comparing(ProductVersionPojo::getExpiredTime).reversed())
                    .collect(Collectors.toList());
            return productVersionPojoList.get(0);
        }
        return null;
    }
}
