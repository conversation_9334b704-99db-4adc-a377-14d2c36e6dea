package com.facishare.open.qywx.web.template.outer.recvCmd.event.app;

import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.event.app.AppEventTemplate;
import com.facishare.open.feishu.syncapi.enums.ChannelEnum;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEnterpriseMapping;
import com.facishare.open.qywx.accountinner.model.QyweixinGetAuthInfoRsp;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountsync.constant.EnterpriseWeChatEventTag;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinCorpBindBo;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.web.core.enums.QyweixinBindStatusEnum;
import com.facishare.open.qywx.web.db.dao.QyweixinCorpBindDao;
import com.facishare.open.qywx.web.manager.CorpManager;
import com.facishare.open.qywx.web.manager.EventCloudProxyManager;
import com.facishare.open.qywx.web.manager.QYWeixinManager;
import com.facishare.open.qywx.web.model.QyweixinEventCreateAuthModel;
import com.facishare.open.qywx.web.model.qyweixin.QyweixinGetPermenantCodeRsp;
import com.facishare.open.qywx.web.template.outer.recvCmd.event.order.QyweixinOpenEnterpriseHandlerTemplate;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 飞书应用事件处理器
 * <AUTHOR>
 * @date 2024-08-20
 */

@Component
@Slf4j
public class QyweixinAppEventTemplate extends AppEventTemplate {
    @Resource
    private QYWeixinManager qyWeixinManager;
    @Resource
    private EventCloudProxyManager eventCloudProxyManager;
    @Resource
    private CorpManager corpManager;
    @Resource
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;
    @Resource
    private QyweixinOpenEnterpriseHandlerTemplate qyweixinOpenEnterpriseHandlerTemplate;
    @Resource
    private QyweixinCorpBindDao qyweixinCorpBindDao;

    @Override
    public void onAppOpen(MethodContext context) {
        LogUtils.info("QyweixinAppEventTemplate.onAppOpen,context={}",context);
        QyweixinEventCreateAuthModel createAuthModel = context.getData();

        String authCode = createAuthModel.getAuthCode();
        String fsEa = createAuthModel.getFsEa();
        String appId = createAuthModel.getAppId();

        //获取永久授权码、企业信息
        com.facishare.open.qywx.accountinner.result.Result<QyweixinGetPermenantCodeRsp> corpAuthInfoResult = qyWeixinManager.getPermanentCode(authCode, appId);
        log.info("QyweixinAppEventTemplate.getAuthInfoDoInitCorp,corpAuthInfo,corpAuthInfo={}", corpAuthInfoResult);
        if(!corpAuthInfoResult.isSuccess()){
            context.setResult(TemplateResult.newError(ErrorRefer.INTERNAL_ERROR.getQywxCode()));
            return;
        }
        QyweixinGetPermenantCodeRsp corpAuthInfo = corpAuthInfoResult.getData();
        //保存应用信息
        //投递
        eventCloudProxyManager.eventCloudProxy(ChannelEnum.qywx, appId, EnterpriseWeChatEventTag.TAG_ENTERPRISE_WECHAT_CMD_EVENT_4_THIRD, null, "create_auth", new Gson().toJson(corpAuthInfo), null);
        corpManager.saveCorpInfoTask(corpAuthInfo,appId);

        Map<String, Object> appOpenMap = new HashMap<>();
        appOpenMap.put("corpAuthInfo", corpAuthInfo);
        appOpenMap.put("appId", appId);
        appOpenMap.put("authCode", authCode);

        //真正安装应用开通逻辑
        qyweixinOpenEnterpriseHandlerTemplate.execute(appOpenMap);

        context.setResult(TemplateResult.newSuccess("success"));
    }

    @Override
    public void onAppStatusChange(MethodContext context) {
        Map<String, Object> data = context.getData();
        String corpId = (String) data.get("corpId");
        String suiteId = (String) data.get("suiteId");

        updateCorpInfo(corpId, suiteId);

        context.setResult(TemplateResult.newSuccess());
    }

    @Override
    public void onAppVisibleRangeChange(MethodContext context) {
        LogUtils.info("QyweixinAppEventTemplate.onAppVisibleRangeChange,context={}",context);

        Map<String, Object> data = context.getData();
        String finalPlainMsg = (String) data.get("finalPlainMsg");
        String changeType = (String) data.get("changeType");

        corpManager.changeContacts(finalPlainMsg, changeType);

        context.setResult(TemplateResult.newSuccess());

        LogUtils.info("QyweixinAppEventTemplate.onAppVisibleRangeChange,end,context={}",context);
    }

    @Override
    public void onAppStop(MethodContext context) {
        Map<String, Object> data = context.getData();
        String corpId = (String) data.get("corpId");
        String suiteId = (String) data.get("suiteId");
        cancelCorpAuth(corpId, suiteId);
        context.setResult(TemplateResult.newSuccess());
    }

    public void updateCorpInfo(String corpId, String appId) {
        log.info("QyweixinAppEventTemplate.updateCorpInfo,corpId={},appId={}",corpId,appId);
        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(corpId);
        if(CollectionUtils.isEmpty(enterpriseMappingList)) {
           return;
        }
        //更新企业信息
        com.facishare.open.qywx.accountinner.result.Result<QyweixinGetAuthInfoRsp> authInfoRspResult = qyWeixinManager.getCorpInfo(corpId, appId);
        if(!authInfoRspResult.isSuccess() || ObjectUtils.isEmpty(authInfoRspResult.getData())) {
            //目前发现已过期的应用也会收到change_auth事件，这时候调用获取企业信息接口就会返回错误，这里做特殊处理
            log.info("QyweixinAppEventTemplate.updateCorpInfo,getCorpInfo exception = {}",authInfoRspResult.getMsg());
            log.info("QyweixinAppEventTemplate.updateCorpInfo,调用企业微信获取企业信息接口失败");
            return ;
        }
        //保存应用信息
        corpManager.updateCorpInfo(authInfoRspResult.getData(), corpId, appId);
    }

    public void cancelCorpAuth(String corpId, String appId) {
        //保存绑定关系或更新
        QyweixinCorpBindBo qyweixinCorpBindBo = qyweixinCorpBindDao.queryQyweixinCorpBind(corpId, appId);
        log.info("QyweixinAppEventTemplate.cancelCorpAuth,qyweixinCorpBindBo={}",qyweixinCorpBindBo);

        qyweixinCorpBindBo.setStatus(QyweixinBindStatusEnum.DELETE_BIND.getCode());
        int count = qyweixinCorpBindDao.update(qyweixinCorpBindBo);
        log.info("QyweixinAppEventTemplate.cancelCorpAuth,count={}",count);
        //使用代开发授权会话留存，删除代开发应用的时候，会话留存的corpSecret和agentId也要删除
        corpManager.updateCorpMessageGenerating(corpId, qyweixinCorpBindBo);
    }
}
