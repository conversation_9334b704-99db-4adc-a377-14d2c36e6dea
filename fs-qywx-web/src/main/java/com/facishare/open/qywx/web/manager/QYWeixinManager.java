package com.facishare.open.qywx.web.manager;

import com.alibaba.fastjson.*;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaAppInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaConfigInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaAppInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaConfigInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaAppInfoParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaConfigInfoParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEmployeeBindParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.oa.base.dbproxy.utils.SecurityUtil;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import com.facishare.open.order.contacts.proxy.api.model.contacts.OutDepModel;
import com.facishare.open.order.contacts.proxy.api.model.contacts.OutEmpModel;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaAppInfoStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaConfigInfoTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.params.QyweixinAppInfoParams;
import com.facishare.open.qywx.accountinner.model.*;
import com.facishare.open.qywx.accountinner.result.Result;
import com.facishare.open.qywx.accountinner.result.ResultCodeEnum;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountsync.arg.FileUploadArg;
import com.facishare.open.qywx.accountsync.constant.Constant;
import com.facishare.open.qywx.accountsync.utils.GsonUtil;
import com.facishare.open.qywx.messagesend.enums.AbilityIdEnum;
import com.facishare.open.qywx.save.arg.MessageStorageArg;
import com.facishare.open.qywx.save.result.QywxMessageDataResult;
import com.facishare.open.qywx.save.result.QywxResult;
import com.facishare.open.qywx.save.service.MessageGeneratingService;
import com.facishare.open.qywx.web.arg.GetHitMsgListArg;
import com.facishare.open.qywx.web.arg.GetRuleListArg;
import com.facishare.open.qywx.web.arg.QywxActviceCodeArg;
import com.facishare.open.qywx.web.arg.RuleModel;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.accountsync.core.enums.QYWXDataTypeEnum;
import com.facishare.open.qywx.accountsync.core.enums.QyweixinErrorCodeEnum;
import com.facishare.open.qywx.accountsync.core.enums.SourceTypeEnum;
import com.facishare.open.qywx.web.crm.CrmUrlUtils;
import com.facishare.open.qywx.web.info.ConversationArchiveInfo;
import com.facishare.open.qywx.web.model.TagDetailModel;
import com.facishare.open.qywx.web.model.TranslateFileType;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.web.model.qyweixin.*;
import com.facishare.open.qywx.web.model.RuleId;
import com.facishare.open.qywx.web.model.result.MsgListResult;
import com.facishare.open.qywx.web.model.result.QywxTransferResult;
import com.facishare.open.qywx.web.model.result.RuleListResult;
import com.facishare.open.qywx.web.network.ProxyOkHttpClient;
import com.facishare.open.qywx.web.notification.QyweixinMsgNotification;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.web.result.accountsync.UploadResult;
import com.facishare.open.qywx.web.upload.FsInputStreamBody;
import com.facishare.open.qywx.web.utils.*;
import com.facishare.open.qywx.accountsync.utils.xml.SuiteAuthXml;
import com.fxiaoke.common.Pair;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.base.Stopwatch;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Created by liuwei on 2018/06/15
 */
@Slf4j
@Data
@Component
public class QYWeixinManager {

    @Autowired
    private RedisDataSource redisDataSource;

    @Autowired
    private CorpManager corpManager;

    private HttpHelper httpHelper = new HttpHelper();

//    @Autowired
//    private QyweixinCorpBindDao qyweixinCorpBindDao;
//
//    @Autowired
//    private QyweixinContactBindDao qyweixinContactBindDao;

    @Autowired
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;

//    @Autowired
//    private QyweixinExternalContactDao qyweixinExternalContactDao;

//    @Autowired
//    private SendFsEaApi sendFsEaApi;

    @Autowired
    private QyweixinExternalManager qyweixinExternalManager;

    @ReloadableProperty("appMetaInfoStr2")
    private String appMetaInfoCmsStr;

    /**
     * 授权类型：0 正式授权， 1 测试授权。 默认值为0。注意，请确保应用在正式发布后的授权类型为“正式授权”
     */
    @ReloadableProperty("appAuthType")
    private Integer appAuthType;

    /**
     * 通过onlineAppIds设置appAuthType为0, 否则设置为1  之后下线appAuthType配置
     */
    @ReloadableProperty("onlineAppIds")
    private String onlineAppIds;

    /**
     * corpid、provider_secret（获取方法为：登录服务商管理后台->标准应用服务->通用开发参数，可以看到）
     */
    @ReloadableProperty("providerCorpId")
    private String providerCorpId;

    @ReloadableProperty("providerSecret")
    private String providerSecret;

    @ReloadableProperty("token")
    private String token;

    @ReloadableProperty("encodingAESKey")
    private String encodingAESKey;

    @ReloadableProperty("eserviceAppId")
    private String eserviceAppId;
//    @ReloadableProperty("repAppId")
//    private String repAppId;

    //按企业进行的带宽配置，默认每秒1MB
    @ReloadableProperty("bandwidthConfig")
    private String bandwidthConfig;

    Map<String, AppConfigInfo> appMetaInfo = Maps.newHashMap();

    private String getTokenUrl = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=%s&corpsecret=%s";

    @Autowired
    private QyweixinMsgNotification qyweixinMsgNotification;

    @Autowired
    private OAConnectorDataManager oaConnectorDataManager;

    @Autowired
    private ProxyOkHttpClient proxyOkHttpClient;

    @Autowired
    private EventCloudProxyManager eventCloudProxyManager;
    @Autowired
    private MessageGeneratingService messageGeneratingService;
    @Autowired
    private EIEAConverter eieaConverter;
    @Autowired
    private OuterOaAppInfoManager outerOaAppInfoManager;
    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Autowired
    private OuterOaConfigInfoManager outerOaConfigInfoManager;

    @PostConstruct
    public void initAppMeta() {
        //在cms上的格式配置格式 appMetaInfoStr=source,appid,appsecret   保存的时候key使用source_appid
        ArrayList<AppConfigInfo> appMetaComponent = new Gson().fromJson(appMetaInfoCmsStr, new TypeToken<ArrayList<AppConfigInfo>>() {
        }.getType());
        log.info("trace QYWeixinAdapter app meta, appMetaInfoStr:{}, appMetaComponent:{} ", appMetaInfoCmsStr, appMetaComponent);
        appMetaComponent.stream().forEach(v -> {
            v.setEncodingAESKeyBase64(Base64.decodeBase64(v.getEncodingAESKey() + "="));
            v.setToken(SecurityUtil.decryptStr(v.getToken()));
            v.setSecret(SecurityUtil.decryptStr(v.getSecret()));
            appMetaInfo.put(v.getAppId(), v);
        });
        httpHelper.setQyweixinMsgNotification(qyweixinMsgNotification);
    }

    private LoadingCache<String, String> cache = Caffeine.newBuilder().maximumSize(10000).expireAfterWrite(80, TimeUnit.MINUTES).refreshAfterWrite(60, TimeUnit.MINUTES).build(new CacheLoader<String, String>() {
        @Nullable
        @Override
        public String load(@NonNull String key) throws Exception {
            return getAppAccessToken(key).getData();
        }
    });


    private String getAppInfoKey(String source, String appId) {
        return new StringBuilder().append(source).append("_").append(appId).toString();
    }

    /**
     * 不同的应用，对应不同的企业token
     *
     * @param source
     * @param appId 应用id
     * @param corpId  企业微信开放平台上的corpid
     * @param forceUpdate: 是否强制刷新redis 缓存的token.
     * forceUpdate参数设置的原因： 这块代码是复用云之家开放平台的。在访问云之家开放平台时，发现就算是缓存了有效期内的token,
     * 都有可能云之家OpenAPI被拒绝。解决方法是：检查openapi接口的错误码，然后强制刷新accesstoken。
     * <p>
     * 企业微信openapi有没有这类问题，目前还不知道。
     * @return
     */
    private static String COPR_ACCESS_TOKEN_KEY = "qyweixin_corp_accesstoken_key_";

    public Result<String> getCorpAccessToken(String appId, String corpId, Boolean forceUpdate) {
        String oldCorpId = corpId;
        if(StringUtils.isEmpty(corpId)) return Result.newError(ResultCodeEnum.MISSING_PARAMETER);

        if(StringUtils.startsWithIgnoreCase(appId,"dk")) {
            OuterOaAppInfoEntity oaAppInfoEntity = outerOaAppInfoManager.getEntity(ChannelEnum.qywx, corpId, appId);
            log.info("CorpManager.getPermannentCodeFromDB,qyweixinCorpBindBo={}",oaAppInfoEntity);
            if(ObjectUtils.isEmpty(oaAppInfoEntity)) {
                //兼容获取不到 corpSecretCode 场景
                corpId = corpId2OpenCorpId(corpId).getData();
                oaAppInfoEntity = outerOaAppInfoManager.getEntity(ChannelEnum.qywx, corpId, appId);
            }
            QyweixinAppInfoParams qyweixinAppInfoParams = JSON.parseObject(oaAppInfoEntity.getAppInfo(), QyweixinAppInfoParams.class);
            return getToken(qyweixinAppInfoParams.getPermanentCode(), corpId);
        }

        corpId = corpManager.getValidCorpId(corpId, appId);
        log.info("QYWeixinManager.getCorpAccessToken,corpId={},appId={},validCorpId={}",oldCorpId,appId,corpId);

        if (isCancelAuth(corpId, appId).getData()) {
            return Result.newError(ResultCodeEnum.APP_NOT_BIND);
        }
        if (null == forceUpdate) {
            forceUpdate = false;
        }
        String corpAccessTokenKey = COPR_ACCESS_TOKEN_KEY + corpId + "_" + appId;
        String corpAccessToken = redisDataSource.getRedisClient().get(corpAccessTokenKey);

        log.info("QYWeixinManager.getCorpAccessToken,from redis,corpAccessToken={},corpAccessTokenKey={}", XorUtils.EncodeByXor(corpAccessToken, ConfigCenter.XOR_SECRET_KEY), corpAccessTokenKey);
        if (forceUpdate || StringUtils.isBlank(corpAccessToken)) {
            Result<String> accessTokenFromApiResult = getCorpAccessTokenFromApi(appId, corpId);
            if(!accessTokenFromApiResult.isSuccess() || StringUtils.isEmpty(accessTokenFromApiResult.getData())) {
                return Result.newError(accessTokenFromApiResult.getCode(), accessTokenFromApiResult.getMsg());
            }
            corpAccessToken = accessTokenFromApiResult.getData();
            //微信access_token 过期与新申请的可同时存在
            redisDataSource.getRedisClient().set(corpAccessTokenKey, corpAccessToken);
            redisDataSource.getRedisClient().expire(corpAccessTokenKey, 7200);
            log.info("QYWeixinManager.getCorpAccessToken,corpAccessToken={}", XorUtils.EncodeByXor(corpAccessToken, ConfigCenter.XOR_SECRET_KEY));
        }

        return Result.newSuccess(corpAccessToken);
    }

    private Result<String> getCorpAccessTokenFromApi(String appId, String corpId) {
        if(StringUtils.isEmpty(corpId)) return Result.newError(ResultCodeEnum.MISSING_PARAMETER);
        String suiteAccessToken = getSuiteAccessTokenFromRedis(appId);

        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/get_corp_token?suite_access_token=" + suiteAccessToken;
        Map<String, String> form = new HashMap<>();
        form.put("auth_corpid", corpId);
        form.put("permanent_code", corpManager.getPermannentCodeFromDB(corpId, appId));

        log.info("QYWeixinManager.getCorpAccessTokenFromApi,corpId={},appId={},form={}",
                corpId,appId,form);
        String httpRsp = proxyOkHttpClient.postUrl(url, form, new HashMap<>());
        log.info("QYWeixinManager.getCorpAccessTokenFromApi,corpId={},appId={},httpRsp={}",
                corpId,appId,XorUtils.EncodeByXor(httpRsp, ConfigCenter.XOR_SECRET_KEY));
        QyweixinGetCorptokenRsp qyweixinGetCorptokenRsp = new Gson().fromJson(httpRsp, QyweixinGetCorptokenRsp.class);

        if(qyweixinGetCorptokenRsp.isSuccess()) {
            return Result.newSuccess(qyweixinGetCorptokenRsp.getAccess_token());
        }
        return Result.newError(String.valueOf(qyweixinGetCorptokenRsp.getErrcode()), qyweixinGetCorptokenRsp.getErrmsg());
    }

    /**
     * 通过通讯录id来获取企业的授权码来判断是否取消了通讯录同步，为了不混淆原来正常的获取而分开请求
     *
     * @return
     */
    public Result<QyweixinGetCorptokenRsp> getCorpAccessTokenForCheck(String appId, String corpId) {
        String suiteAccessToken = getSuiteAccessTokenFromRedis(appId);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/get_corp_token?suite_access_token=" + suiteAccessToken;
        Map<String, String> form = new HashMap<>();
        form.put("auth_corpid", corpId);
        form.put("permanent_code", corpManager.getPermannentCodeFromDB(corpId, appId));
        log.info("trace getCorpAccessTokenForCheck get :{} ", form);
        String httpRsp = proxyOkHttpClient.postUrl(url, form, new HashMap<>());
        log.info("trace getCorpAccessTokenForCheck httpRsp: {}", XorUtils.EncodeByXor(httpRsp, ConfigCenter.XOR_SECRET_KEY));
        QyweixinGetCorptokenRsp qyweixinGetCorptokenRsp = new Gson().fromJson(httpRsp, QyweixinGetCorptokenRsp.class);
        if(qyweixinGetCorptokenRsp.isSuccess()) {
            return Result.newSuccess(qyweixinGetCorptokenRsp);
        }
        return Result.newError(String.valueOf(qyweixinGetCorptokenRsp.getErrcode()), qyweixinGetCorptokenRsp.getErrmsg());
    }


    /**
     * 服务商的token
     *
     * @return
     */
    public Result<String> getProviderAccessToken(Boolean forceUpdate) {
        if (null == forceUpdate) {
            forceUpdate = false;
        }
        String providerAccessTokenKey = "qyweixin_provider_accesstoken_key_" + providerCorpId;
        String providerAccessToken = redisDataSource.getRedisClient().get(providerAccessTokenKey);

        log.info("from getProviderAccesstoken:{} from redis", XorUtils.EncodeByXor(providerAccessToken, ConfigCenter.XOR_SECRET_KEY));

        if (forceUpdate || StringUtils.isBlank(providerAccessToken)) {
            String url = "https://qyapi.weixin.qq.com/cgi-bin/service/get_provider_token";
            Map<String, String> form = new HashMap<>();
            form.put("corpid", providerCorpId);
            form.put("provider_secret", SecurityUtil.decryptStr(providerSecret));
            log.info("trace getProviderAccessToken : {}", form);
            String httpRsp = proxyOkHttpClient.postUrl(url, form, new HashMap<>());
            log.info("trace getProviderAccessToken httpRsp : {}", XorUtils.EncodeByXor(httpRsp, ConfigCenter.XOR_SECRET_KEY));
            QyweixinGetProviderAccessTokenRsp providerAccessTokenRsp = new Gson().fromJson(httpRsp, QyweixinGetProviderAccessTokenRsp.class);

            if (providerAccessTokenRsp.isSuccess()) {
                redisDataSource.getRedisClient().set(providerAccessTokenKey, providerAccessTokenRsp.getProvider_access_token());
                redisDataSource.getRedisClient().expire(providerAccessTokenKey, providerAccessTokenRsp.getExpires_in());

                providerAccessToken = providerAccessTokenRsp.getProvider_access_token();
                return Result.newSuccess(providerAccessToken);
            }
            return Result.newError(String.valueOf(providerAccessTokenRsp.getErrcode()), providerAccessTokenRsp.getErrmsg());
        }

        return Result.newSuccess(providerAccessToken);
    }

    /**
     * 获取缓存accessToken
     *
     * @param appId
     * @return
     */
    public String getSuiteAccessTokenFromRedis(String appId) {
        log.info("trace getSuiteAccessTokenFromRedis appId:{}", appId);
        String suiteAccessToken = redisDataSource.getRedisClient().get("qyweixin_suite_ticket_key_" + appId);
        log.info("trace getSuiteAccessTokenFromRedis suiteAccessToken:{} appId:{}", XorUtils.EncodeByXor(suiteAccessToken, ConfigCenter.XOR_SECRET_KEY), appId);
        return suiteAccessToken;
    }

    /**
     * 调用公费电话（测试）
     *
     * @param caller
     * @param authCorpId
     * @return
     */
    public Result<String> getDial(String caller, String authCorpId) {
        Result<String> getProviderAccessTokenResult = getProviderAccessToken(null);   //获取服务商token
        if(!getProviderAccessTokenResult.isSuccess() || StringUtils.isEmpty(getProviderAccessTokenResult.getData())) {
            return Result.newError(getProviderAccessTokenResult.getCode(), getProviderAccessTokenResult.getMsg());
        }
        String callee = corpManager.getAdminUserId(authCorpId);  //获取管理员userid
        log.info("trace getAdminUserId : {}", callee);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/dial?provider_access_token=" + getProviderAccessTokenResult.getData();
        Map<String, String> form = new HashMap<>();

        form.put("caller", caller);
        form.put("auth_corpid", authCorpId);
        form.put("callee", callee);
        log.info("trace getDial get form: {}", form);
        String httpRsp = proxyOkHttpClient.postUrl(url, form, new HashMap<>());
        log.info("trace httpRsp : {}", httpRsp);
        return Result.newSuccess(httpRsp);

    }

    /**
     * 1.通过suite_ticket获取suite_access_token
     * 2.suite_access_token保存到redis
     *
     * @param suiteAuthXml 企业微信服务器会定时（每十分钟）推送ticket。ticket会实时变更，并用于后续接口的调用。
     */
    public Result<Void> saveQyweixinTicketToken(SuiteAuthXml suiteAuthXml, Boolean isProxy) {
        String suiteTicketKey = "qyweixin_suite_ticket_key_" + suiteAuthXml.getSuiteId();
        String suiteAccessToken = getSuiteAccessToken(suiteAuthXml.getSuiteId(), suiteAuthXml.getSuiteTicket());
        if (!StringUtils.isBlank(suiteAccessToken)) {
            //投递
            if(isProxy) {
                eventCloudProxyManager.eventCloudProxy(ChannelEnum.qywx, suiteAuthXml.getSuiteId(), null, null, suiteAuthXml.getInfoType(), suiteAccessToken, null);
            }

            String result = redisDataSource.getRedisClient().set(suiteTicketKey, suiteAccessToken);
            log.info("trace saveQyweixinTicketToken result:{} suiteId:{}", result, suiteAuthXml.getSuiteId());
        } else {
            log.error("trace saveQyweixinTicketToken error empty suiteAccessToken suiteId:{}", suiteAuthXml.getSuiteId());
        }
        return Result.newSuccess();
    }

    public Result<Void> saveQyweixinCloudTicketToken(String suiteId, String suiteAccessToken) {
        String suiteTicketKey = "qyweixin_suite_ticket_key_" + suiteId;
        String result = redisDataSource.getRedisClient().set(suiteTicketKey, suiteAccessToken);
        log.info("trace savQyweixinCloudTicketToken result:{} suiteId:{}", result, suiteId);
        return Result.newSuccess();
    }

    private String getSuiteAccessToken(String appId, String suiteTicket) {
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/get_suite_token";
        Map<String, String> form = new HashMap<>();

        form.put("suite_id", appId);
        form.put("suite_secret", appMetaInfo.get(appId).getSecret());
        form.put("suite_ticket", suiteTicket);
        String httpRsp = proxyOkHttpClient.postUrl(url, form, new HashMap<>());
        log.info("trace getSuiteAccessToken httpRsp:{} ", XorUtils.EncodeByXor(httpRsp, ConfigCenter.XOR_SECRET_KEY));
        QyweixinGetSuittokenRsp qyweixinGetSuittokenRsp = new Gson().fromJson(httpRsp, QyweixinGetSuittokenRsp.class);

        //上报，无论成功失败都发送
        if (qyweixinGetSuittokenRsp.isSuccess()) {
            oaConnectorDataManager.send(null, null, SourceTypeEnum.QYWX.getSourceType(),
                    QYWXDataTypeEnum.SUITE_TICKET.getDataType(), null, null, suiteTicket, qyweixinGetSuittokenRsp.getSuite_access_token(), null, null);
            return qyweixinGetSuittokenRsp.getSuite_access_token();
        } else {
            oaConnectorDataManager.send(null, null, SourceTypeEnum.QYWX.getSourceType(),
                    QYWXDataTypeEnum.SUITE_TICKET.getDataType(), null, null, suiteTicket, null, String.valueOf(qyweixinGetSuittokenRsp.getErrcode()), qyweixinGetSuittokenRsp.getErrmsg());
            log.error("tarce getSuiteAccessToken get errmsg:{} ", qyweixinGetSuittokenRsp.getErrmsg());
            return null;
        }
    }

    /**
     * 获取永久授权码
     *
     * @param authTmpCode
     * @param appId
     * @return
     */
    public Result<QyweixinGetPermenantCodeRsp> getPermanentCode(String authTmpCode, String appId) {
        String suiteAccessToken = getSuiteAccessTokenFromRedis(appId);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/get_permanent_code?suite_access_token=" + suiteAccessToken;
        Map<String, String> form = new HashMap<>();
        form.put("auth_code", authTmpCode);

        log.info("trace getPermanentCode {}:", form);
        String httpRsp = proxyOkHttpClient.postUrl(url, form, new HashMap<>());
        log.info("trace getPermanentCode rsp:{} ", XorUtils.EncodeByXor(httpRsp, ConfigCenter.XOR_SECRET_KEY));
        QyweixinGetPermenantCodeRsp qyweixinGetPermenantCodeRsp = new Gson().fromJson(httpRsp, QyweixinGetPermenantCodeRsp.class);

        if (qyweixinGetPermenantCodeRsp.isSuccess()) {
            return Result.newSuccess(qyweixinGetPermenantCodeRsp);
        } else {
            log.error("trace getPermanentCode error: {}", qyweixinGetPermenantCodeRsp.getErrmsg());
            return Result.newError(String.valueOf(qyweixinGetPermenantCodeRsp.getErrcode()), qyweixinGetPermenantCodeRsp.getErrmsg());
        }
    }

    /**
     * 获取企业的js签名
     *
     * @param appId
     * @param corpId
     * @param forceUpdate
     * @return
     */
    public Result<String> getJsApiTicket(String appId, String corpId, Boolean forceUpdate) {
        String oldCorpId = corpId;
        log.info("QYWeixinManager.getJsApiTicket,appId={},corpId={},forceUpdate={}",appId,corpId,forceUpdate);
        String jsApiTicketKey = new StringBuilder().append("qyweixin_js_api_ticket_key_").append(corpId).append("_").append(appId).toString();

        String jsApiTicket = redisDataSource.getRedisClient().get(jsApiTicketKey);
        if (forceUpdate || StringUtils.isBlank(jsApiTicket)) {
            corpId = corpManager.getValidCorpId(corpId,appId);
            log.info("QYWeixinManager.getJsApiTicket,appId={},corpId={},validCorpId={}",appId,oldCorpId,corpId);
            Result<String> accessTokenResult = getCorpAccessToken(appId, corpId, false);
            if (Objects.equals(ConfigCenter.crmAppId, appId) && StringUtils.isBlank(accessTokenResult.getData())) {
                accessTokenResult = getCorpAccessToken(eserviceAppId, corpId, false);
            }
            String url = "https://qyapi.weixin.qq.com/cgi-bin/get_jsapi_ticket?access_token=" + accessTokenResult.getData();
            String httpRsp = proxyOkHttpClient.getUrl(url, new HashMap<>());
            log.info("trace getJsApiTicket httpRsp:{}", XorUtils.EncodeByXor(httpRsp, ConfigCenter.XOR_SECRET_KEY));
            QyweixinJsApiTicketRsp qyweixinJsApiTicketRsp = new Gson().fromJson(httpRsp, QyweixinJsApiTicketRsp.class);
            if(qyweixinJsApiTicketRsp.isSuccess()) {
                redisDataSource.getRedisClient().set(jsApiTicketKey, qyweixinJsApiTicketRsp.getTicket());
                redisDataSource.getRedisClient().expire(jsApiTicketKey, qyweixinJsApiTicketRsp.getExpires_in());
                jsApiTicket = qyweixinJsApiTicketRsp.getTicket();
            } else {
                return Result.newError(String.valueOf(qyweixinJsApiTicketRsp.getErrcode()), qyweixinJsApiTicketRsp.getErrmsg());
            }
        }

        return Result.newSuccess(jsApiTicket);
    }

    /**
     * 获取用户的手机号码
     */
    private Result<String> doGetUserPhone(String appId, String qywxCorpId, String userTicket) {
        Result<String> accessTokenResult = getCorpAccessToken(appId, qywxCorpId, false);
        if(!accessTokenResult.isSuccess() || StringUtils.isEmpty(accessTokenResult.getData())) {
            return accessTokenResult;
        }
        String cmdUrl = "https://qyapi.weixin.qq.com/cgi-bin/user/getuserdetail?access_token=" + accessTokenResult.getData();
        Map<String, String> param = Maps.newHashMap();
        param.put("user_ticket", userTicket);
        String httpRsp = proxyOkHttpClient.postUrl(cmdUrl, param, new HashMap<>());
        QyweixinUserDetailRsp qyweixinUserDetailRsp = new Gson().fromJson(httpRsp, QyweixinUserDetailRsp.class);

        log.info("trace doGetUserPhone qyweixinUserDetailRsp:{}", qyweixinUserDetailRsp);
        if(QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(qyweixinUserDetailRsp.getErrcode())) {
            return Result.newSuccess(qyweixinUserDetailRsp.getMobile());
        } else {
            return Result.newError(String.valueOf(qyweixinUserDetailRsp.getErrcode()), qyweixinUserDetailRsp.getErrmsg());
        }
    }

    public Result<Pair<String, String>> getUserPhone(String appId, String qywxCorpId, String code) {
        Result<String> accessTokenResult = getCorpAccessToken(appId, qywxCorpId, false);
        if (!accessTokenResult.isSuccess() || StringUtils.isEmpty(accessTokenResult.getData())) {
            accessTokenResult = getCorpAccessToken(appId, qywxCorpId, true);
        }
        String cmdUrl = "https://qyapi.weixin.qq.com/cgi-bin/user/getuserinfo?access_token=" + accessTokenResult.getData() + "&code=" + code;
        String httpRsp = proxyOkHttpClient.getUrl(cmdUrl, new HashMap<>());

        QyweixinUserInfoRsp qyweixinUserInfoRsp = new Gson().fromJson(httpRsp, QyweixinUserInfoRsp.class);
        log.info("trace getUserPhone qyweixinUserInfoRsp:{}", qyweixinUserInfoRsp);
        if(QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(qyweixinUserInfoRsp.getErrcode())) {
            String userId = qyweixinUserInfoRsp.getUserId();
            if (null == userId) {
                userId = qyweixinUserInfoRsp.getOpenId();
            }
            Result<String> userPhoneResult = doGetUserPhone(appId, qywxCorpId, qyweixinUserInfoRsp.getUser_ticket());
            if(userPhoneResult.isSuccess()) {
                String userPhone = userPhoneResult.getData();
                log.info("trace getUserPhone, userId:{}, userPhone:{}", userId, userPhone);
                return Result.newSuccess(new Pair<String, String>(userId, userPhone));
            } else {
                return Result.newError(userPhoneResult.getCode(), userPhoneResult.getMsg());
            }
        } else {
            return Result.newError(String.valueOf(qyweixinUserInfoRsp.getErrcode()), qyweixinUserInfoRsp.getErrmsg());
        }
    }

    public Result<String> getPreAuthCode(String appId) {

        String preAuthCodeKey = new StringBuilder().append("qyweixin_pre_auth_code_key_").append(appId).toString();

        String preAuthCode = redisDataSource.getRedisClient().get(preAuthCodeKey);

        log.info("trace getPreAuthCode preAuthCodeKey:{}, preAuthCode:{}", preAuthCodeKey, XorUtils.EncodeByXor(preAuthCode, ConfigCenter.XOR_SECRET_KEY));
        if (StringUtils.isBlank(preAuthCode)) {
            String suiteAccessToken = getSuiteAccessTokenFromRedis(appId);
            String url = "https://qyapi.weixin.qq.com/cgi-bin/service/get_pre_auth_code?suite_access_token=" + suiteAccessToken;
            String httpRsp = proxyOkHttpClient.getUrl(url, new HashMap<>());
            log.info("trace getPreAuthCode QyweixinPreAuthCodehttpRsp{}", XorUtils.EncodeByXor(httpRsp, ConfigCenter.XOR_SECRET_KEY));
            QyweixinPreAuthCodeRsp qyweixinPreAuthCodeRsp = new Gson().fromJson(httpRsp, QyweixinPreAuthCodeRsp.class);

            if(qyweixinPreAuthCodeRsp.isSuccess()) {
                redisDataSource.getRedisClient().set(preAuthCodeKey, qyweixinPreAuthCodeRsp.getPre_auth_code());
                redisDataSource.getRedisClient().expire(preAuthCodeKey, qyweixinPreAuthCodeRsp.getExpires_in());
                return Result.newSuccess(qyweixinPreAuthCodeRsp.getPre_auth_code());
            } else {
                log.info("trace getPreAuthCode get error, errorMsg:{}", qyweixinPreAuthCodeRsp.getErrmsg());
                return Result.newError(String.valueOf(qyweixinPreAuthCodeRsp.getErrcode()), qyweixinPreAuthCodeRsp.getErrmsg());
            }
        }

        return Result.newSuccess(preAuthCode);
    }

    /**
     * 设置授权配置
     *
     * @param appId
     * @param preAuthCode
     * @return
     */
    public Result<String> setSessionInfo(String appId, String preAuthCode) {
        String suitAccesstoken = getSuiteAccessTokenFromRedis(appId);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/set_session_info?suite_access_token=" + suitAccesstoken;
        Map<String, Object> form = new HashMap<>();
        form.put("pre_auth_code", preAuthCode);
        //授权类型：0 正式授权， 1 测试授权。 默认值为0。注意，请确保应用在正式发布后的授权类型为“正式授权”
        Map<String, Integer> sessionInfo = ImmutableMap.<String, Integer>builder()
                .put("auth_type", onlineAppIds.contains(appId) ? 0 : 1).build();

        form.put("session_info", sessionInfo);
        log.info("trace setSessionInfo set:{}", form);
        String httpRsp = proxyOkHttpClient.postUrl(url, form, new HashMap<>());
        log.info("trace setSessionInfo httpRsp:{}", httpRsp);
        return Result.newSuccess(httpRsp);
    }

    /**
     * 根据注册模板生成注册码
     *
     * @param templateId 注册模板id，最长为128个字节
     * @return
     */
    public Result<String> getRegisterCode(String templateId) {
        Result<String> providerAccessTokenResult = getProviderAccessToken(false);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/get_register_code?provider_access_token=" + providerAccessTokenResult.getData();
        Map<String, String> form = Maps.newHashMap();
        form.put("template_id", templateId);
        log.info("trace getRegisterCode set:{}", form);
        String httpRsp = proxyOkHttpClient.postUrl(url, form, new HashMap<>());
        QyweixinRegisterCodeRsp qyweixinRegisterCodeRsp = new Gson().fromJson(httpRsp, QyweixinRegisterCodeRsp.class);
        log.info("trace getRegisterCode httpRsp:{}", qyweixinRegisterCodeRsp);

        if(QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(qyweixinRegisterCodeRsp.getErrcode())) {
            return Result.newSuccess(qyweixinRegisterCodeRsp.getRegister_code());
        } else {
            return Result.newError(String.valueOf(qyweixinRegisterCodeRsp.getErrcode()), qyweixinRegisterCodeRsp.getErrmsg());
        }
    }

    /**
     * 获取登录用户信息
     *
     * @param authCode
     * @return
     */

    public Result<QyweixinLoginInfoRsp> getWebLoginUserInfo(String authCode) {
        Result<String> providerTokenResult = getProviderAccessToken(false);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/get_login_info?access_token=" + providerTokenResult.getData();
        Map<String, String> form = new HashMap<>();
        form.put("auth_code", authCode);
        log.info("QYWeixinManager.getWebLoginUserInfo,form={}", form);
        String httpRsp = proxyOkHttpClient.postUrl(url, form, new HashMap<>());
        log.info("QYWeixinManager.getWebLoginUserInfo,httpRsp={}", httpRsp);
        QyweixinLoginInfoRsp qyweixinLoginInfoRsp = new Gson().fromJson(httpRsp, QyweixinLoginInfoRsp.class);
        log.info("QYWeixinManager.getWebLoginUserInfo,qyweixinLoginInfoRsp={}", qyweixinLoginInfoRsp);

        if(QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(qyweixinLoginInfoRsp.getErrcode())) {
            return Result.newSuccess(qyweixinLoginInfoRsp);
        } else {
            return Result.newError(String.valueOf(qyweixinLoginInfoRsp.getErrcode()), qyweixinLoginInfoRsp.getErrmsg());
        }
    }

    public Result<QyweixinUserDetailInfoRsp> getUserInfo(String appId, String corpId, String userId) {
        String finalAppId = getFinalAppId(corpId, appId);

        Result<String> corpAccessTokenResult = getCorpAccessToken(finalAppId, corpId, false);
        if(!corpAccessTokenResult.isSuccess() || StringUtils.isEmpty(corpAccessTokenResult.getData())) {
            return Result.newError(corpAccessTokenResult.getCode(), corpAccessTokenResult.getMsg());
        }
        String url = "https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token=" + corpAccessTokenResult.getData() + "&userid=" + userId;
        String httpRsp = proxyOkHttpClient.getUrl(url, new HashMap<>());
        QyweixinUserDetailInfoRsp qyweixinUserDetailInfoRsp = new Gson().fromJson(httpRsp, QyweixinUserDetailInfoRsp.class);
        log.info("QYWeixinManager.getUserInfo,qyweixinUserDetailInfoRsp={}", qyweixinUserDetailInfoRsp);

        if(QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(qyweixinUserDetailInfoRsp.getErrcode())) {
            if(CollectionUtils.isNotEmpty(qyweixinUserDetailInfoRsp.getDepartment()) && StringUtils.isNotEmpty(qyweixinUserDetailInfoRsp.getMain_department())) {
                List<String> departments = this.swapDepartment(qyweixinUserDetailInfoRsp.getDepartment(), qyweixinUserDetailInfoRsp.getMain_department());
                qyweixinUserDetailInfoRsp.setDepartment(departments);
            }
            if(!StringUtils.startsWithIgnoreCase(finalAppId,"dk")) {
                //不是通过代开发获取的信息，需要对Name做特殊处理
                qyweixinUserDetailInfoRsp.setName(ContactsUtils.emp_prefix+qyweixinUserDetailInfoRsp.getName());
            }
            log.info("QYWeixinManager.getUserInfo,qyweixinUserDetailInfoRsp2={}", qyweixinUserDetailInfoRsp);
            return Result.newSuccess(qyweixinUserDetailInfoRsp);
        } else {
            log.warn("trace getUserInfo error errmsg:{}", qyweixinUserDetailInfoRsp.getErrmsg());
            return Result.newError(String.valueOf(qyweixinUserDetailInfoRsp.getErrcode()), qyweixinUserDetailInfoRsp.getErrmsg());
        }
    }

    /**
     * 该接口用于根据code获取成员信息，适用于自建应用与代开发应用
     * @param appId
     * @param corpId
     * @param code
     * @return
     */
    public Result<QyweixinRepUserDetailInfoRsp> getRepUserInfoByCode(String appId, String corpId, String code) {
        Result<String> corpAccessTokenResult = getCorpAccessToken(appId, corpId, false);
        if(!corpAccessTokenResult.isSuccess() || StringUtils.isEmpty(corpAccessTokenResult.getData())) {
            return Result.newError(corpAccessTokenResult.getCode(), corpAccessTokenResult.getMsg());
        }
        String url = "https://qyapi.weixin.qq.com/cgi-bin/auth/getuserinfo?access_token=" + corpAccessTokenResult.getData() + "&code=" + code;
        String httpRsp = proxyOkHttpClient.getUrl(url, new HashMap<>());
        QyweixinRepUserInfoRsp repUserInfoRsp = new Gson().fromJson(httpRsp, QyweixinRepUserInfoRsp.class);
        log.info("QYWeixinManager.getUserInfoByCode,repUserInfoRsp={}", repUserInfoRsp);
        if(QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(repUserInfoRsp.getErrcode())) {
            if(StringUtils.isNotBlank(repUserInfoRsp.getUser_ticket())){
                return getRepUserDetail(repUserInfoRsp.getUser_ticket(), corpAccessTokenResult.getData());
            }else{
                QyweixinRepUserDetailInfoRsp repUserDetailInfoRsp= QyweixinRepUserDetailInfoRsp.builder().build();
                repUserDetailInfoRsp.setUserid(repUserInfoRsp.getUserid());
                repUserDetailInfoRsp.setErrcode(repUserInfoRsp.getErrcode());
                repUserDetailInfoRsp.setErrmsg(repUserInfoRsp.getErrmsg());
                return Result.newSuccess(repUserDetailInfoRsp);
            }

        }
        return Result.newError(String.valueOf(repUserInfoRsp.getErrcode()), repUserInfoRsp.getErrmsg());
    }

    public Result<OutEmpModel> getUserModel(String appId, String corpId, String userId) {
//        Result<QyweixinUserDetailInfoRsp> userDetailInfoRspResult = null;
//        if(StringUtils.equalsIgnoreCase(appId,repAppId)==false && corpManager.isRepAppInstalled(corpId)) {
//            userDetailInfoRspResult = getUserInfo(repAppId, corpId, userId);
//        }
//        if(userDetailInfoRspResult==null || !userDetailInfoRspResult.isSuccess() || userDetailInfoRspResult.getData()==null) {
//            userDetailInfoRspResult = getUserInfo(appId, corpId, userId);
//        }
        Result<QyweixinUserDetailInfoRsp> userDetailInfoRspResult = getUserInfo(appId, corpId, userId);
        if(userDetailInfoRspResult==null || !userDetailInfoRspResult.isSuccess() || userDetailInfoRspResult.getData()==null) {
            return userDetailInfoRspResult==null ? Result.newSuccess() : Result.newError(userDetailInfoRspResult.getCode(), userDetailInfoRspResult.getMsg());

        }
        return Result.newSuccess(ContactsUtils.getOutEmpModel(userDetailInfoRspResult.getData()));
    }

    public Result<QyweixinUserDetailInfoRsp> getUserInfoFromSelf(String corpSecret, String corpId, String userId) {        String combineAccount = Joiner.on("|").join(corpId, corpSecret);
        String corpAccessToken = cache.get(combineAccount);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token=" + corpAccessToken + "&userid=" + userId;
        String httpRsp = proxyOkHttpClient.getUrl(url, new HashMap<>());
        QyweixinUserDetailInfoRsp qyweixinUserDetailInfoRsp = new Gson().fromJson(httpRsp, QyweixinUserDetailInfoRsp.class);
        log.info("trace getUserInfo rsp:{} ", qyweixinUserDetailInfoRsp);

        if(QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(qyweixinUserDetailInfoRsp.getErrcode())) {
            if(CollectionUtils.isNotEmpty(qyweixinUserDetailInfoRsp.getDepartment()) && StringUtils.isNotEmpty(qyweixinUserDetailInfoRsp.getMain_department())) {
                List<String> departments = this.swapDepartment(qyweixinUserDetailInfoRsp.getDepartment(), qyweixinUserDetailInfoRsp.getMain_department());
                qyweixinUserDetailInfoRsp.setDepartment(departments);
            }
            return Result.newSuccess(qyweixinUserDetailInfoRsp);
        } else {
            log.warn("trace getUserInfo error errmsg:{}", qyweixinUserDetailInfoRsp.getErrmsg());
            return Result.newError(String.valueOf(qyweixinUserDetailInfoRsp.getErrcode()), qyweixinUserDetailInfoRsp.getErrmsg());
        }
    }


//    /**
//     * 统一检测是否进行重试
//     *
//     * @param errcode
//     */
//    private void checkRetryMethod(int errcode) {
//        if (QyweixinErrorCodeEnum.ACCESS_TOKEN_INVALID.getErrCode().equals(errcode) ||
//                QyweixinErrorCodeEnum.CORP_ACCESS_TOKEN_INVALID.getErrCode().equals(errcode)) {
//            RetryException excetption = new RetryException();
//            excetption.setErrorCode(errcode);
//            throw new RetryException();
//        }
//    }


    public Result<Object> getAppLoginUserInfo(String code, String appId, String outEa) {
        log.info("QYWeixinManeger.getAppLoginUserInfo,code={},appId={},outEa={}",code,appId,outEa);
        String suitAccesstoken = getSuiteAccessTokenFromRedis(appId);
        String userTicket = null;
        QyweixinUserSimpleInfoRsp userSimpleInfoRsp = new QyweixinUserSimpleInfoRsp();
        if(StringUtils.startsWithIgnoreCase(appId,"dk")) {
            suitAccesstoken = getCorpAccessToken(appId,outEa,true).getData();
            Result<QyweixinRepUserInfoRsp> repUserSimpleInfo = getRepUserSimpleInfo(code, suitAccesstoken);
            log.info("QYWeixinManeger.getAppLoginUserInfo,repUserSimpleInfo={}",repUserSimpleInfo);
            if(!repUserSimpleInfo.isSuccess() || ObjectUtils.isEmpty(repUserSimpleInfo.getData())) {
                log.info("QYWeixinManeger.getAppLoginUserInfo,userInfo is null,appId={},suitAccesstoken={}", appId, XorUtils.EncodeByXor(suitAccesstoken, ConfigCenter.XOR_SECRET_KEY));
                return Result.newError(repUserSimpleInfo.getCode(), repUserSimpleInfo.getMsg());
            }
            userTicket = repUserSimpleInfo.getData().getUser_ticket();
//            com.facishare.open.qywx.accountsync.result.Result<List<QyweixinAccountEmployeeMapping>> accountBind = qyweixinAccountBindInnerService.queryAccountBind(Lists.newArrayList(repUserSimpleInfo.getData().getUserid()),
//                    appId);
//            log.info("QYWeixinManeger.getAppLoginUserInfo,accountBind={}",accountBind);
//            if(CollectionUtils.isEmpty(accountBind.getData())) {
//                return Result.newError(ErrorRefer.QYWX_EMP_MAPPING_NOT_EXIST.getCode(), ErrorRefer.QYWX_EMP_MAPPING_NOT_EXIST.getQywxCode());
//            }
            outEa = corpId2OpenCorpId(outEa).getData();
            userSimpleInfoRsp.setCorpId(outEa);
            userSimpleInfoRsp.setUserId(repUserSimpleInfo.getData().getUserid());
            userSimpleInfoRsp.setUser_ticket(userTicket);
        } else {
            Result<QyweixinUserSimpleInfoRsp> userInfoResult = getUserSimpleInfo3rd(code, suitAccesstoken);
            if(!userInfoResult.isSuccess() || ObjectUtils.isEmpty(userInfoResult.getData())) {
                log.info("QYWeixinManeger.getAppLoginUserInfo,userInfo is null,appId={},suitAccesstoken={}", appId, XorUtils.EncodeByXor(suitAccesstoken, ConfigCenter.XOR_SECRET_KEY));
                return Result.newError(userInfoResult.getCode(), userInfoResult.getMsg());
            }
            userTicket = userInfoResult.getData().getUser_ticket();
            userSimpleInfoRsp = userInfoResult.getData();
        }

        log.info("QYWeixinManeger.getAppLoginUserInfo,userSimpleInfoRsp={}",userSimpleInfoRsp);
        if(StringUtils.isNotEmpty(userSimpleInfoRsp.getCorpId()) && StringUtils.isNotEmpty(userSimpleInfoRsp.getUserId())) {
            return Result.newSuccess(userSimpleInfoRsp);
        }

        //scope为snsapi_userinfo或snsapi_privateinfo，且用户在应用可见范围之内时返回此参数。
        if(StringUtils.isEmpty(userTicket)) {
            return Result.newSuccess(userSimpleInfoRsp);
        } else {
            Result<QyweixinUserDetailInfoRsp> detailInfoRspResult = getUserDetail3rd(userTicket, suitAccesstoken);
            if(detailInfoRspResult.isSuccess() && ObjectUtils.isNotEmpty(detailInfoRspResult.getData())) {
                return Result.newSuccess(detailInfoRspResult.getData());
            } else {
                return Result.newError(detailInfoRspResult.getCode(), detailInfoRspResult.getMsg());
            }
        }
    }

    private Result<QyweixinUserDetailInfoRsp> getUserDetail3rd(String userTicket, String suitAccesstoken) {
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/getuserdetail3rd?access_token=" + suitAccesstoken;
        Map<String, String> form = new HashMap<>();
        form.put("user_ticket", userTicket);
        String httpRsp = proxyOkHttpClient.postUrl(url, form, new HashMap<>());
        QyweixinUserDetailInfoRsp qyweixinUserDetailInfoRsp = new Gson().fromJson(httpRsp, new TypeToken<QyweixinUserDetailInfoRsp>() {
        }.getType());
        log.info("trace getUserDetail3rd result:{}", qyweixinUserDetailInfoRsp);
        if(QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(qyweixinUserDetailInfoRsp.getErrcode())) {
            if(ObjectUtils.isNotEmpty(qyweixinUserDetailInfoRsp) && CollectionUtils.isNotEmpty(qyweixinUserDetailInfoRsp.getDepartment()) && StringUtils.isNotEmpty(qyweixinUserDetailInfoRsp.getMain_department())) {
                List<String> departments = this.swapDepartment(qyweixinUserDetailInfoRsp.getDepartment(), qyweixinUserDetailInfoRsp.getMain_department());
                qyweixinUserDetailInfoRsp.setDepartment(departments);
            }
            return Result.newSuccess(qyweixinUserDetailInfoRsp);
        } else {
            return Result.newError(String.valueOf(qyweixinUserDetailInfoRsp.getErrcode()), qyweixinUserDetailInfoRsp.getErrmsg());
        }
    }

    /**
     * 获取自建或代开发应用用户敏感信息
     * @param userTicket
     * @param suitAccesstoken
     * @return
     */
    private Result<QyweixinRepUserDetailInfoRsp> getRepUserDetail(String userTicket, String suitAccesstoken) {
        String url = "https://qyapi.weixin.qq.com/cgi-bin/auth/getuserdetail?access_token=" + suitAccesstoken;
        Map<String, String> form = new HashMap<>();
        form.put("user_ticket", userTicket);
        String httpRsp = proxyOkHttpClient.postUrl(url, form, new HashMap<>());
        log.info("trace getRepUserDetail httpRsp:{}", httpRsp);
        QyweixinRepUserDetailInfoRsp repUserDetailInfoRsp = new Gson().fromJson(httpRsp, new TypeToken<QyweixinRepUserDetailInfoRsp>() {
        }.getType());
        if(QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(repUserDetailInfoRsp.getErrcode())) {
            return Result.newSuccess(repUserDetailInfoRsp);
        } else {
            return Result.newError(String.valueOf(repUserDetailInfoRsp.getErrcode()), repUserDetailInfoRsp.getErrmsg());
        }
    }

    /**
     * 获取自建或代开发应用用户简单信息
     * @param code
     * @param suitAccesstoken
     * @return
     */
    private Result<QyweixinRepUserInfoRsp> getRepUserSimpleInfo(String code, String suitAccesstoken) {
        String url = "https://qyapi.weixin.qq.com/cgi-bin/auth/getuserinfo?access_token=" + suitAccesstoken + "&code=" + code;
        String httpRsp = proxyOkHttpClient.getUrl(url, new HashMap<>());
        QyweixinRepUserInfoRsp repUserInfoRsp = new Gson().fromJson(httpRsp, QyweixinRepUserInfoRsp.class);
        log.info("trace getRepUserSimpleInfo result:{}", repUserInfoRsp);
        if(QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(repUserInfoRsp.getErrcode())) {
            return Result.newSuccess(repUserInfoRsp);
        } else {
            return Result.newError(String.valueOf(repUserInfoRsp.getErrcode()), repUserInfoRsp.getErrmsg());
        }
    }

    private Result<QyweixinUserSimpleInfoRsp> getUserSimpleInfo3rd(String code, String suitAccesstoken) {
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/getuserinfo3rd?access_token=" + suitAccesstoken + "&code=" + code;
        String httpRsp = proxyOkHttpClient.getUrl(url, new HashMap<>());
        QyweixinUserSimpleInfoRsp qyweixinUserSimpleInfoRsp = new Gson().fromJson(httpRsp, QyweixinUserSimpleInfoRsp.class);
        log.info("trace getUserSimpleInfo3rd result:{}", qyweixinUserSimpleInfoRsp);
        if(qyweixinUserSimpleInfoRsp.isSuccess()) {
            return Result.newSuccess(qyweixinUserSimpleInfoRsp);
        } else {
            log.error("trace getUserSimpleInfo3rd error errmsg:{}", qyweixinUserSimpleInfoRsp.getErrmsg() + "_" + qyweixinUserSimpleInfoRsp.getErrcode());
            return Result.newError(String.valueOf(qyweixinUserSimpleInfoRsp.getErrcode()), qyweixinUserSimpleInfoRsp.getErrmsg());
        }
    }

    //获取企业授权信息
    public Result<QyweixinGetAuthInfoRsp> getCorpInfo(String corpId, String appId) {
        String permanentCode = corpManager.getPermannentCodeFromDB2(corpId, appId);
        if(StringUtils.isEmpty(permanentCode)) {
            return Result.newError(ErrorRefer.CRM_APP_NOT_INSTALL.getCode(),ErrorRefer.CRM_APP_NOT_INSTALL.getQywxCode());
        }
        String suiteAccessToken = getSuiteAccessTokenFromRedis(appId);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/get_auth_info?suite_access_token=" + suiteAccessToken;
        Map<String, String> form = Maps.newHashMap();
        form.put("auth_corpid", corpId);
        form.put("permanent_code", permanentCode);
        log.info("trace getCorpInfo get:{}", form);
        String httpRsp = proxyOkHttpClient.postUrl(url, form, new HashMap<>());
        QyweixinGetAuthInfoRsp qyweixinAuthCorpInfo = new Gson().fromJson(httpRsp, QyweixinGetAuthInfoRsp.class);
        if(qyweixinAuthCorpInfo.isSuccess()) {
            log.info("trace getCorpInfo rsp:{}", qyweixinAuthCorpInfo);
            qyweixinAuthCorpInfo.getAuth_corp_info().setCorpid(corpId2OpenCorpId(qyweixinAuthCorpInfo.getAuth_corp_info().getCorpid()).getData());
            return Result.newSuccess(qyweixinAuthCorpInfo);
        } else {
            return Result.newError(String.valueOf(qyweixinAuthCorpInfo.getErrcode()), qyweixinAuthCorpInfo.getErrmsg());
        }
    }

    public Result<QyweixinSuiteAdminListRsp> getSuiteAdminList(String corpId, String appId, Integer agentId) {
        String suiteAccessToken = getSuiteAccessTokenFromRedis(appId);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/get_admin_list?suite_access_token=" + suiteAccessToken;
        if(null == agentId) {
            Result<QyweixinGetAuthInfoRsp> corpInfoResult = getCorpInfo(corpId, appId);
            if(corpInfoResult.isSuccess()) {
                agentId = corpInfoResult.getData().getAuth_info().getAgent().get(0).getAgentid();
            }
        }
        Map<String, Object> form = Maps.newHashMap();
        form.put("auth_corpid", corpId);
        form.put("agentid", agentId);
        log.info("trace getSuiteAdminList get:{}", form);
        String httpRsp = proxyOkHttpClient.postUrl(url, form, new HashMap<>());
        QyweixinSuiteAdminListRsp suiteAdminListRsp = new Gson().fromJson(httpRsp, QyweixinSuiteAdminListRsp.class);
        log.info("trace getSuiteAdminList result:{}", suiteAdminListRsp);
        if(QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(suiteAdminListRsp.getErrcode())) {
            return Result.newSuccess(suiteAdminListRsp);
        } else {
            return Result.newError(String.valueOf(suiteAdminListRsp.getErrcode()), suiteAdminListRsp.getErrmsg());
        }
    }

    /**
     * 递归获取指定部门ID下的所有的子部门，拍平后返回
     * 成员授权模式下，应用无任何部门权限，调用时将返回60011错误
     * @param appId
     * @param corpId
     * @param departmentId 部门id。获取指定部门及其下的子部门（以及子部门的子部门等等，递归）。 如果不填，默认获取全量组织架构
     * @return
     */
    public Result<QyweixinDepartmentListRsp> getDepartmentInfoList(String appId, String corpId, String departmentId) {
        log.info("QYWeixinManager.getDepartmentInfoList,appId={},corpId={},departmentId={}",appId,corpId,departmentId);
        String finalAppId = getFinalAppId(corpId, appId);
        //优先选择代开发应用授权
        Result<String> corpAccessTokenResult = getCorpAccessToken(finalAppId, corpId, false);
        if(!corpAccessTokenResult.isSuccess() || StringUtils.isEmpty(corpAccessTokenResult.getData())) {
            return Result.newError(corpAccessTokenResult.getCode(), corpAccessTokenResult.getMsg());
        }
        departmentId = StringUtils.isBlank(departmentId) ? "" : departmentId;
        String url = "https://qyapi.weixin.qq.com/cgi-bin/department/list?access_token=" + corpAccessTokenResult.getData() + "&id=" + departmentId;
        String httpRsp = proxyOkHttpClient.getUrl(url, new HashMap<>());
        log.info("QYWeixinManager.getDepartmentInfoList,httpRsp={}", httpRsp);
        QyweixinDepartmentListRsp qyweixinDepartmentListRsp = new Gson().fromJson(httpRsp, QyweixinDepartmentListRsp.class);
        if(qyweixinDepartmentListRsp.isSuccess()) {
            //排序
            List<QyweixinDepartmentRsp> departmentRsps = sortDepList(null, qyweixinDepartmentListRsp.getDepartment());
            qyweixinDepartmentListRsp.getDepartment().clear();
            qyweixinDepartmentListRsp.setDepartment(departmentRsps);

            for(QyweixinDepartmentRsp departmentRsp : qyweixinDepartmentListRsp.getDepartment()) {
                if(!StringUtils.startsWithIgnoreCase(finalAppId,"dk")) {
                    String depName = null;
                    if(StringUtils.isEmpty(departmentRsp.getName())) {
                        depName = ContactsUtils.dep_prefix+departmentRsp.getId();
                    } else {
                        depName = ContactsUtils.dep_prefix+departmentRsp.getName();
                    }
                    departmentRsp.setName(depName);
                }
            }
            log.info("QYWeixinManager.getDepartmentInfoList,qyweixinDepartmentListRsp={}", qyweixinDepartmentListRsp);
            return Result.newSuccess(qyweixinDepartmentListRsp);
        } else {
            return Result.newError(String.valueOf(qyweixinDepartmentListRsp.getErrcode()), qyweixinDepartmentListRsp.getErrmsg());
        }
    }

    private List<QyweixinDepartmentRsp> sortDepList(String parentDepId, List<QyweixinDepartmentRsp> departmentRspList) {
        List<QyweixinDepartmentRsp> sortDepartmentList = new LinkedList<>();
        if(StringUtils.isEmpty(parentDepId)) {
            Set<String> parentDepIds = departmentRspList.stream().map(QyweixinDepartmentRsp::getParentid).collect(Collectors.toSet());
            Set<String> depIds = departmentRspList.stream().map(QyweixinDepartmentRsp::getId).collect(Collectors.toSet());
            for(String depId : parentDepIds) {
                if(depIds.contains(depId)) {
                    continue;
                }
                List<QyweixinDepartmentRsp> sortDepList = sortDepList(depId, departmentRspList);
                if(CollectionUtils.isNotEmpty(sortDepList)) {
                    sortDepartmentList.addAll(sortDepList);
                }
            }
        }
        List<QyweixinDepartmentRsp> sortParentDepList = departmentRspList.stream().filter(v -> v.getParentid().equals(parentDepId)).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(sortParentDepList)) {
            sortDepartmentList.addAll(sortParentDepList);
            for(QyweixinDepartmentRsp rsp : sortParentDepList) {
                List<QyweixinDepartmentRsp> sortDepList = sortDepList(rsp.getId(), departmentRspList);
                if(CollectionUtils.isNotEmpty(sortDepList)) {
                    sortDepartmentList.addAll(sortDepList);
                }
            }
        }
        return sortDepartmentList;
    }

    public Result<List<OutDepModel>> getDepInfoList(String appId, String corpId, String depId) {
        Result<QyweixinDepartmentListRsp> departmentListRspResult = getDepartmentInfoList(appId, corpId, depId);
        if(!departmentListRspResult.isSuccess() || ObjectUtils.isEmpty(departmentListRspResult.getData())) {
            return Result.newError(departmentListRspResult.getCode(), departmentListRspResult.getMsg());
        }

        return Result.newSuccess(ContactsUtils.getOutDepModelList(departmentListRspResult.getData().getDepartment()));
//        if(StringUtils.equalsIgnoreCase(appId,repAppId))
//            return Result.newSuccess(ContactsUtils.getOutDepModelList(departmentListRspResult.getData().getDepartment()));
//
//        List<QyweixinDepartmentRsp> depList = new ArrayList<>();
//        boolean repAppInstalled = corpManager.isRepAppInstalled(corpId);
//        log.info("QYWeixinManager.getDepInfoList,repAppInstalled={}",repAppInstalled);
//        for(QyweixinDepartmentRsp departmentRsp : departmentListRspResult.getData().getDepartment()) {
//            Result<QyweixinDepartmentInfoRsp> departmentInfoResult = null;
//            if(repAppInstalled) {
//                //尝试从代开发应用获取部门信息
//                departmentInfoResult = getDepartmentInfo(repAppId, corpId, departmentRsp.getId());
//                log.info("QYWeixinManager.getDepInfoList,departmentInfo={}",departmentInfoResult);
//            }
//            if(departmentInfoResult!=null && departmentInfoResult.isSuccess() && ObjectUtils.isNotEmpty(departmentInfoResult.getData())) {
//                depList.add(departmentInfoResult.getData().getDepartment());
//            } else {
//                depList.add(departmentRsp);
//            }
//        }
//        return Result.newSuccess(ContactsUtils.getOutDepModelList(depList));
    }

    /**
     * 获取单个部门详情
     * @param appId
     * @param corpId
     * @param departmentId
     * @return
     */
    public Result<QyweixinDepartmentInfoRsp> getDepartmentInfo(String appId, String corpId, String departmentId) {
        log.info("QYWeixinManager.getDepartmentInfo,appId={},corpId={},departmentId={}",appId,corpId,departmentId);
        String finalAppId = getFinalAppId(corpId, appId);
        //优先选择代开发应用授权
        Result<String> corpAccessTokenResult = getCorpAccessToken(finalAppId, corpId, false);
        if(!corpAccessTokenResult.isSuccess() || StringUtils.isEmpty(corpAccessTokenResult.getData())) {
            return Result.newError(corpAccessTokenResult.getCode(), corpAccessTokenResult.getMsg());
        }
        departmentId = StringUtils.isBlank(departmentId) ? "" : departmentId;
        String url = "https://qyapi.weixin.qq.com/cgi-bin/department/get?access_token=" + corpAccessTokenResult.getData() + "&id=" + departmentId;
        String httpRsp = proxyOkHttpClient.getUrl(url, new HashMap<>());
        log.info("QYWeixinManager.getDepartmentInfo,httpRsp={}",httpRsp);
        QyweixinDepartmentInfoRsp departmentInfoRsp = new Gson().fromJson(httpRsp, QyweixinDepartmentInfoRsp.class);
        log.info("QYWeixinManager.getDepartmentInfo,departmentInfoRsp={}",departmentInfoRsp);
        if(departmentInfoRsp.isSuccess()) {
            if(!StringUtils.startsWithIgnoreCase(finalAppId,"dk")) {
                String depName = null;
                if(StringUtils.isEmpty(departmentInfoRsp.getDepartment().getName())) {
                    depName = ContactsUtils.dep_prefix+departmentInfoRsp.getDepartment().getId();
                } else {
                    depName = ContactsUtils.dep_prefix+departmentInfoRsp.getDepartment().getName();
                }
                departmentInfoRsp.getDepartment().setName(depName);
            }
            log.info("QYWeixinManager.getDepartmentInfo,departmentInfoRsp2={}",departmentInfoRsp);
            return Result.newSuccess(departmentInfoRsp);
        } else {
            return Result.newError(String.valueOf(departmentInfoRsp.getErrcode()), departmentInfoRsp.getErrmsg());
        }
    }

    /**
     * 获取单个部门详情
     * @param appId
     * @param corpId
     * @param departmentId
     * @return
     */
    public Result<OutDepModel> getDepInfo(String appId, String corpId, String departmentId) {
//        Result<QyweixinDepartmentInfoRsp> departmentInfoRspResult = null;
//        if(StringUtils.equalsIgnoreCase(appId,repAppId)==false && corpManager.isRepAppInstalled(corpId)) {
//            departmentInfoRspResult = getDepartmentInfo(repAppId, corpId, departmentId);
//        }
//        if(departmentInfoRspResult==null || !departmentInfoRspResult.isSuccess() || departmentInfoRspResult.getData()==null) {
//            departmentInfoRspResult = getDepartmentInfo(appId, corpId, departmentId);
//        }
        Result<QyweixinDepartmentInfoRsp> departmentInfoRspResult = getDepartmentInfo(appId, corpId, departmentId);
        if(departmentInfoRspResult==null || !departmentInfoRspResult.isSuccess() || departmentInfoRspResult.getData()==null) {
            return departmentInfoRspResult==null ? Result.newSuccess() : Result.newError(departmentInfoRspResult.getCode(), departmentInfoRspResult.getMsg());
        }
        return Result.newSuccess(ContactsUtils.getOutDepModel(departmentInfoRspResult.getData().getDepartment()));
    }

    public Result<QyweixinTagListRsp> getTagInfoList(String appId, String corpId) {
        Result<String> corpAccessTokenResult = getCorpAccessToken(appId, corpId, false);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/tag/list?access_token=" + corpAccessTokenResult.getData();
        if(!corpAccessTokenResult.isSuccess() || StringUtils.isEmpty(corpAccessTokenResult.getData())) {
            return Result.newError(corpAccessTokenResult.getCode(), corpAccessTokenResult.getMsg());
        }
        String httpRsp = proxyOkHttpClient.getUrl(url, new HashMap<>());
        QyweixinTagListRsp qyweixinTagListRsp = new Gson().fromJson(httpRsp, QyweixinTagListRsp.class);
        log.info("trace getTagInfoList result:{}", httpRsp);

        if (qyweixinTagListRsp.isSuccess()) {
            return Result.newSuccess(qyweixinTagListRsp);
        } else {
            return Result.newError(String.valueOf(qyweixinTagListRsp.getErrcode()), qyweixinTagListRsp.getErrmsg());
        }
    }

    /**
     * 获取部门成员详情,获取当前部门下的员工列表，不包括子部门下的员工列表
     * 成员授权模式下，应用无任何部门权限，调用时将返回60011错误
     * @param appId
     * @param corpId
     * @param departmentId
     * @param fetchChild 1/0：是否递归获取子部门下面的成员
     * @return
     */
    public Result<QyweixinDepartmentEmployeeListRsp> getDepartmentEmployeeList(String appId, String corpId, String departmentId,int fetchChild) {
        log.info("QYWeixinManager.getDepartmentEmployeeList,appId={},corpId={},departmentId={}",appId,corpId,departmentId);
        String finalAppId = getFinalAppId(corpId, appId);
        Result<String> corpAccessTokenResult = getCorpAccessToken(finalAppId, corpId, false);
        if(!corpAccessTokenResult.isSuccess() || StringUtils.isEmpty(corpAccessTokenResult.getData())) {
            return Result.newError(corpAccessTokenResult.getCode(), corpAccessTokenResult.getMsg());
        }
        //fetch_child  1/0：是否递归获取子部门下面的成员
        String url = "https://qyapi.weixin.qq.com/cgi-bin/user/list?access_token=" + corpAccessTokenResult.getData() + "&department_id=" + departmentId + "&fetch_child=" + fetchChild;
        log.info("QYWeixinManager.getDepartmentEmployeeList,url={}",url);
        String httpRsp = proxyOkHttpClient.getUrl(url, new HashMap<>());
        log.info("QYWeixinManager.getDepartmentEmployeeList,httpRsp={}",httpRsp);
        QyweixinDepartmentEmployeeListRsp qyweixinDepartmentEmployeeListRsp = new Gson().fromJson(httpRsp, QyweixinDepartmentEmployeeListRsp.class);
        log.info("QYWeixinManager.getDepartmentEmployeeList,qyweixinDepartmentEmployeeListRsp={}",qyweixinDepartmentEmployeeListRsp);

        if (qyweixinDepartmentEmployeeListRsp.isSuccess() ) {
            if(CollectionUtils.isNotEmpty(qyweixinDepartmentEmployeeListRsp.getUserlist())) {
                for(QyweixinUserDetailInfoRsp qyweixinUserDetailInfoRsp : qyweixinDepartmentEmployeeListRsp.getUserlist()) {
                    if(ObjectUtils.isNotEmpty(qyweixinUserDetailInfoRsp)
                            && CollectionUtils.isNotEmpty(qyweixinUserDetailInfoRsp.getDepartment())) {

                        if(StringUtils.isNotEmpty(qyweixinUserDetailInfoRsp.getMain_department())) {
                            List<String> departments = this.swapDepartment(qyweixinUserDetailInfoRsp.getDepartment(), qyweixinUserDetailInfoRsp.getMain_department());
                            qyweixinUserDetailInfoRsp.setDepartment(departments);
                        }

                        if(!StringUtils.startsWithIgnoreCase(finalAppId,"dk")) {
                            //不是通过代开发获取的信息，需要对Name做特殊处理
                            qyweixinUserDetailInfoRsp.setName(ContactsUtils.emp_prefix+qyweixinUserDetailInfoRsp.getName());
                        }
                    }
                }
            }
            return Result.newSuccess(qyweixinDepartmentEmployeeListRsp);
        } else {
            log.warn("warn getDepartmentEmployeeList, this depId isn't in visible scope. msg:{}", qyweixinDepartmentEmployeeListRsp.getErrmsg());
            return Result.newError(String.valueOf(qyweixinDepartmentEmployeeListRsp.getErrcode()), qyweixinDepartmentEmployeeListRsp.getErrmsg());
        }
    }

    /**
     * 获取部门成员详情,获取当前部门下的员工列表，包括子部门下的员工列表
     * 成员授权模式下，应用无任何部门权限，调用时将返回60011错误
     * @param appId
     * @param corpId
     * @param departmentId
     * @return
     */
    public Result<QyweixinDepartmentEmployeeListRsp> getDepartmentEmployeeList(String appId, String corpId, String departmentId) {
        return getDepartmentEmployeeList(appId,corpId,departmentId,1);
    }

    public Result<List<OutEmpModel>> getDepEmpList(String appId, String corpId, String departmentId) {
//        Result<QyweixinDepartmentEmployeeListRsp> employeeListRspResult = null;
//        if(StringUtils.equalsIgnoreCase(appId,repAppId)==false && corpManager.isRepAppInstalled(corpId)) {
//            employeeListRspResult = getDepartmentEmployeeList(repAppId, corpId, departmentId,0);
//        }
//        if(employeeListRspResult==null || !employeeListRspResult.isSuccess() || employeeListRspResult.getData()==null) {
//            employeeListRspResult = getDepartmentEmployeeList(appId, corpId, departmentId,0);
//        }
        Result<QyweixinDepartmentEmployeeListRsp> employeeListRspResult = getDepartmentEmployeeList(appId, corpId, departmentId,0);
        if(employeeListRspResult==null || !employeeListRspResult.isSuccess() || employeeListRspResult.getData()==null) {
            return employeeListRspResult==null ? Result.newSuccess() : Result.newError(employeeListRspResult.getCode(), employeeListRspResult.getMsg());
        }
        return Result.newSuccess(ContactsUtils.getOutEmpModelList(employeeListRspResult.getData().getUserlist()));
    }

    public Result<QyweixinTagEmployeeListRsp> getTagEmployeeList(String appId, String corpId, String tagId) {
        String finalAppId = getFinalAppId(corpId, appId);
        Result<String> corpAccessTokenResult = getCorpAccessToken(finalAppId, corpId, false);
        if(!corpAccessTokenResult.isSuccess() || StringUtils.isEmpty(corpAccessTokenResult.getData())) {
            return Result.newError(corpAccessTokenResult.getCode(), corpAccessTokenResult.getMsg());
        }
        String url = "https://qyapi.weixin.qq.com/cgi-bin/tag/get?access_token=" + corpAccessTokenResult.getData() + "&tagid=" + tagId;
        String httpRsp = proxyOkHttpClient.getUrl(url, new HashMap<>());
        log.info("QYWeixinManager.getTagEmployeeList,httpRsp={}",httpRsp);
        QyweixinTagEmployeeListRsp qyweixinTagEmployeeListRsp = new Gson().fromJson(httpRsp, QyweixinTagEmployeeListRsp.class);

        if (qyweixinTagEmployeeListRsp.isSuccess()) {
            if(CollectionUtils.isNotEmpty(qyweixinTagEmployeeListRsp.getUserlist())) {
                for(QyweixinUserDetailInfoRsp userDetailInfoRsp : qyweixinTagEmployeeListRsp.getUserlist()) {
                    if(!StringUtils.startsWithIgnoreCase(finalAppId,"dk")) {
                        //不是通过代开发获取的信息，需要对Name做特殊处理
                        userDetailInfoRsp.setName(ContactsUtils.emp_prefix+userDetailInfoRsp.getName());
                    }
                }
            }
            return Result.newSuccess(qyweixinTagEmployeeListRsp);
        } else {
            log.error("trace getTagEmployeeList errmsg:{} ", qyweixinTagEmployeeListRsp.getErrmsg());
            return Result.newError(String.valueOf(qyweixinTagEmployeeListRsp.getErrcode()), qyweixinTagEmployeeListRsp.getErrmsg());
        }
    }

    public Result<TagDetailModel> getTagDetail(String appId, String corpId, String tagId) {
//        Result<QyweixinTagEmployeeListRsp> tagEmployeeListRspResult = null;
//        if(StringUtils.equalsIgnoreCase(appId,repAppId)==false && corpManager.isRepAppInstalled(corpId)) {
//            tagEmployeeListRspResult = getTagEmployeeList(repAppId, corpId, tagId);
//        }
//        if(tagEmployeeListRspResult==null || !tagEmployeeListRspResult.isSuccess() || tagEmployeeListRspResult.getData()==null) {
//            tagEmployeeListRspResult = getTagEmployeeList(appId, corpId, tagId);
//        }
        Result<QyweixinTagEmployeeListRsp> tagEmployeeListRspResult = getTagEmployeeList(appId, corpId, tagId);
        if(tagEmployeeListRspResult==null || !tagEmployeeListRspResult.isSuccess() || tagEmployeeListRspResult.getData()==null) {
            return tagEmployeeListRspResult==null ? Result.newSuccess() : Result.newError(tagEmployeeListRspResult.getCode(), tagEmployeeListRspResult.getMsg());
        }

        TagDetailModel model = new TagDetailModel();
        model.setTagName(tagEmployeeListRspResult.getData().getTagname());
        model.setDepList(tagEmployeeListRspResult.getData().getPartylist());
        model.setEmpList(ContactsUtils.getOutEmpModelList(tagEmployeeListRspResult.getData().getUserlist()));

        return Result.newSuccess(model);
    }

    private QyweixinExternalContactInfo convertExternalContactInfo(QyweixinExternalContactRsp rsp) {
        QyweixinExternalContact data = rsp.getExternal_contact();
        if (null == data) {
            return null;
        }

        QyweixinExternalContactInfo result = new QyweixinExternalContactInfo();
        result.setAvatar(data.getAvatar());
        result.setCorpName(data.getCorp_name());
        result.setCorpFullName(data.getCorp_full_name());
        result.setExternalUserid(data.getExternal_userid());
        result.setGender(data.getGender());
        result.setName(data.getName());
        result.setPosition(data.getPosition());
        result.setType(data.getType());
        result.setUnionid(data.getUnionid());
        result.setExternalProfileList(data.getExternal_profile());
        result.setFollowUserList(rsp.getFollow_user());
        return result;
    }

    public Result<QyweixinExternalContactInfo> getQyweixinExternalContact(String appId, String corpId, String externalUserId, String userId) {
        log.info("QYWeixinManager.getQyweixinExternalContact,appId={},corpId={},externalUserId={},userId={}",appId,corpId,externalUserId,userId);
        String realAppId = getFinalAppId(corpId, appId);
        Result<String> corpAccessTokenResult = getCorpAccessToken(realAppId, corpId, false);
        if(!corpAccessTokenResult.isSuccess() || StringUtils.isEmpty(corpAccessTokenResult.getData())) {
            return Result.newError(corpAccessTokenResult.getCode(), corpAccessTokenResult.getMsg());
        }
        String url = "https://qyapi.weixin.qq.com/cgi-bin/crm/get_external_contact?access_token=" + corpAccessTokenResult.getData() + "&external_userid=" + externalUserId;
        String httpRsp = proxyOkHttpClient.getUrl(url, new HashMap<>());
        log.info("QYWeixinManager.getQyweixinExternalContact,httpRsp={}",httpRsp);
        QyweixinExternalContactRsp qyweixinExternalContactRsp = JSONObject.parseObject(httpRsp, QyweixinExternalContactRsp.class);
        log.info("QYWeixinManager.getQyweixinExternalContact,qyweixinExternalContactRsp={}",qyweixinExternalContactRsp);

        if (qyweixinExternalContactRsp.isSuccess()) {
            qyweixinExternalContactRsp = convertFollowUserId(qyweixinExternalContactRsp,corpId,userId,appId);
            if(qyweixinExternalContactRsp!=null) {
                return Result.newSuccess(convertExternalContactInfo(qyweixinExternalContactRsp));
            } else {
                return Result.newError(ResultCodeEnum.EXTERNAL_FOLLOW_USER_EXCEPTION);
            }
        } else {
            return Result.newError(String.valueOf(qyweixinExternalContactRsp.getErrcode()), qyweixinExternalContactRsp.getErrmsg());
        }
    }

    /**
     * 获取企业微信外部联系人通过代开发应用
     * @param corpID
     * @param externalUserId
     * @return
     */
    public Result<QyweixinExternalContactInfo> getQyweixinExternalContact2(String corpID,String fsEa, String externalUserId, String userId, String appId) {
        log.info("QYWeixinManager.getQyweixinExternalContact,corpID={},externalUserId={}",corpID,externalUserId);
        OuterOaAppInfoEntity appInfoEntity = outerOaAppInfoManager.getEntity(ChannelEnum.qywx, corpID, appId);
        QyweixinAppInfoParams qyweixinAppInfoParams = JSON.parseObject(appInfoEntity.getAppInfo(), QyweixinAppInfoParams.class);
        Result<String> appAccessTokenResult = getToken(qyweixinAppInfoParams.getPermanentCode(),corpID);
        if(!appAccessTokenResult.isSuccess() || StringUtils.isEmpty(appAccessTokenResult.getData())) {
            return Result.newError(appAccessTokenResult.getCode(), appAccessTokenResult.getMsg());
        }
        String url = "https://qyapi.weixin.qq.com/cgi-bin/crm/get_external_contact?access_token=" + appAccessTokenResult.getData() + "&external_userid=" + externalUserId;
        String httpRsp = proxyOkHttpClient.getUrl(url, new HashMap<>());
        log.info("QYWeixinManager.getQyweixinExternalContact2,httpRsp={}",httpRsp);
        QyweixinExternalContactRsp qyweixinExternalContactRsp = JSONObject.parseObject(httpRsp, QyweixinExternalContactRsp.class);
        log.info("QYWeixinManager.getQyweixinExternalContact2,qyweixinExternalContactRsp={}",qyweixinExternalContactRsp);

        if (qyweixinExternalContactRsp.isSuccess()) {
            qyweixinExternalContactRsp = convertFollowUserId(qyweixinExternalContactRsp,corpID,userId,appId);
            if(qyweixinExternalContactRsp!=null) {
                return Result.newSuccess(convertExternalContactInfo(qyweixinExternalContactRsp));
            } else {
                return Result.newError(ResultCodeEnum.EXTERNAL_FOLLOW_USER_EXCEPTION);
            }
        } else {
            return Result.newError(String.valueOf(qyweixinExternalContactRsp.getErrcode()), qyweixinExternalContactRsp.getErrmsg());
        }
    }

    public Result<Void> getAllExternalContact(String corpId,String fsEa,String userId, OuterOaAppInfoEntity appInfoEntity) {
        log.info("QyweixinGatewayInnerServiceImpl.getAllExternalContact,corpId={},userId={}",corpId,userId);

//        QyweixinAppInfoParams qyweixinAppInfoParams = JSON.parseObject(appInfoEntity.getAppInfo(), QyweixinAppInfoParams.class);

        Result<List<QyweixinExternalContactRsp>> contactListResult = getContactList(appInfoEntity.getAppId(),
                corpId,fsEa, userId);
        log.info("QyweixinGatewayInnerServiceImpl.getAllExternalContact,contactList={}",contactListResult);
        if(contactListResult.isSuccess() && CollectionUtils.isNotEmpty(contactListResult.getData())) {
            qyweixinExternalManager.insertOrUpdateExternalContact(corpId, userId, contactListResult.getData());
        }
        return Result.newSuccess();
    }

    public Result<String> getDetailByExternalUserIds(String corpId, String appId, List<String> externalUserIdList) {
        OuterOaAppInfoEntity appInfoEntity = outerOaAppInfoManager.getEntity(ChannelEnum.qywx, corpId, appId);
        log.info("QyweixinGatewayInnerServiceImpl.getDetailByExternalUserIds,appInfoEntity={}",appInfoEntity);

        QyweixinAppInfoParams qyweixinAppInfoParams = JSON.parseObject(appInfoEntity.getAppInfo(), QyweixinAppInfoParams.class);
        for(String externalUserId : externalUserIdList) {
            Result<QyweixinExternalContactRsp> externalContactDetailResult = getExternalContactDetail(qyweixinAppInfoParams.getPermanentCode(),
                    corpId, externalUserId, null, appId);
            if(externalContactDetailResult.isSuccess()
                    && ObjectUtils.isNotEmpty(externalContactDetailResult.getData().getExternal_contact())
                    && CollectionUtils.isNotEmpty(externalContactDetailResult.getData().getFollow_user())) {
                QyweixinExternalContactRsp externalContactDetail = externalContactDetailResult.getData();
                log.info("QyweixinGatewayInnerServiceImpl.getDetailByExternalUserIds,externalContactDetail={}",externalContactDetail);

                for(QyweixinFollowUser user : externalContactDetail.getFollow_user()) {
                    qyweixinExternalManager.insertOrUpdateExternalContact(corpId, user.getUserid(), Lists.newArrayList(externalContactDetail));
                }
            }
        }
        return Result.newSuccess(corpId);
    }


    public Result<QyweixinJscode2sessionRsp> jscode2sessionService(String code, String appId) {

        String suiteAccessToken = getSuiteAccessTokenFromRedis(appId);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/miniprogram/jscode2session?suite_access_token=" + suiteAccessToken + "&js_code=" + code + "&grant_type=authorization_code";
        String httpRsp = proxyOkHttpClient.getUrl(url, new HashMap<>());

        Gson gson = new Gson();
        QyweixinJscode2sessionRsp qyweixinJscode2sessionRsp = gson.fromJson(httpRsp, QyweixinJscode2sessionRsp.class);

        log.info("trace jscode2sessionService QyweixinPreAuthCodeRsp: {}", httpRsp);
        if(qyweixinJscode2sessionRsp.isSuccess()) {
            return Result.newSuccess(qyweixinJscode2sessionRsp);
        } else {
            return Result.newError(String.valueOf(qyweixinJscode2sessionRsp.getErrcode()), qyweixinJscode2sessionRsp.getErrmsg());
        }
    }

    /**
     * 获取应用的 Js签名
     *
     * @param appId
     * @param corpId
     * @param forceUpdate
     * @return
     */
    public Result<String> getAgentJsApiTicket(String appId, String corpId, Boolean forceUpdate) {


        String agentJsApiTicketKey = new StringBuilder().append("qyweixin_agent_js_api_ticket_key_").append(corpId).append("_").append(appId).toString();

        String agentJsApiTicket = redisDataSource.getRedisClient().get(agentJsApiTicketKey);
        if (forceUpdate || StringUtils.isBlank(agentJsApiTicket)) {
            Result<String> accessTokenResult = getCorpAccessToken(appId, corpId, false);
            if (Objects.equals(ConfigCenter.crmAppId, appId) && StringUtils.isBlank(accessTokenResult.getData())) {
                accessTokenResult = getCorpAccessToken(eserviceAppId, corpId, false);
            }

            String url = "https://qyapi.weixin.qq.com/cgi-bin/ticket/get?access_token=" + accessTokenResult.getData() + "&type=agent_config";

            String httpRsp = proxyOkHttpClient.getUrl(url, new HashMap<>());
            QyweixinJsApiTicketRsp qyweixinAgentJsApiTicketRsp = new Gson().fromJson(httpRsp, QyweixinJsApiTicketRsp.class);
            log.info("trace getAgentJsApiTicket qyweixinAgentJsApiTicketRsp:{}", qyweixinAgentJsApiTicketRsp);
            if (qyweixinAgentJsApiTicketRsp.isSuccess()) {
                redisDataSource.getRedisClient().set(agentJsApiTicketKey, qyweixinAgentJsApiTicketRsp.getTicket());
                redisDataSource.getRedisClient().expire(agentJsApiTicketKey, qyweixinAgentJsApiTicketRsp.getExpires_in());
                agentJsApiTicket = qyweixinAgentJsApiTicketRsp.getTicket();
            } else {
                return Result.newError(String.valueOf(qyweixinAgentJsApiTicketRsp.getErrcode()), qyweixinAgentJsApiTicketRsp.getErrmsg());
            }
        }

        return Result.newSuccess(agentJsApiTicket);
    }

    /**
     * 查询订单详细信息
     *
     * @param appId
     * @param orderId
     * @return
     */
    public Result<QyweixinOrderInfoRsp> getOrderInfo(String appId, String orderId) {
        String suiteAccessToken = getSuiteAccessTokenFromRedis(appId);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/get_order?suite_access_token=" + suiteAccessToken;
        Map<String, String> form = Maps.newHashMap();
        form.put("orderid", orderId);
        log.info("trace getOrderInfo get:{}", form);
        String httpRsp = proxyOkHttpClient.postUrl(url, form, new HashMap<>());
        QyweixinOrderInfoRsp qyweixinOrderInfo = new Gson().fromJson(httpRsp, QyweixinOrderInfoRsp.class);
        if (qyweixinOrderInfo.isSuccess()) {
            log.info("trace getOrderInfo rsp:{}", qyweixinOrderInfo);
            qyweixinOrderInfo.setPaid_corpid(corpId2OpenCorpId(qyweixinOrderInfo.getPaid_corpid()).getData());
            if (StringUtils.isNotEmpty(qyweixinOrderInfo.getOperator_corpid())) {
                qyweixinOrderInfo.setOperator_corpid(corpId2OpenCorpId(qyweixinOrderInfo.getOperator_corpid()).getData());
            }
            return Result.newSuccess(qyweixinOrderInfo);
        } else {
            return Result.newError(String.valueOf(qyweixinOrderInfo.getErrcode()), qyweixinOrderInfo.getErrmsg());
        }
    }

    /**
     * 查询服务商所有订单信息
     *
     * @param appId
     * @param startTime
     * @param endTime
     * @param testMode
     * @return
     */
    public Result<QyweixinOrderListRsp> getOrderList(String appId, long startTime, long endTime, int testMode) {
        String suiteAccessToken = getSuiteAccessTokenFromRedis(appId);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/get_order_list?suite_access_token=" + suiteAccessToken;
        Map<String, Object> form = Maps.newHashMap();
        form.put("start_time", startTime);
        form.put("end_time", endTime);
        form.put("test_mode", testMode);
        log.info("trace getOrderList get:{}", form);
        String httpRsp = proxyOkHttpClient.postUrl(url, form, new HashMap<>());
        QyweixinOrderListRsp qyweixinOrderInfoList = new Gson().fromJson(httpRsp, QyweixinOrderListRsp.class);
        if (qyweixinOrderInfoList.isSuccess()) {
            log.info("trace getOrderList rsp:{}", qyweixinOrderInfoList);
            return Result.newSuccess(qyweixinOrderInfoList);
        } else {
            return Result.newError(String.valueOf(qyweixinOrderInfoList.getErrcode()), qyweixinOrderInfoList.getErrmsg());
        }
    }

    /**
     * 获取外部联系人列表
     */
    public Result<List<QyweixinExternalContactRsp>> getContactList(String appId, String corpId,String fsEa, String userId) {
        //查看是否有repAppId
        String finalAppId = getFinalAppId(corpId, appId);

        Result<String> corpAccessTokenResult = getCorpAccessToken(finalAppId, corpId, false);
        if(!corpAccessTokenResult.isSuccess() || StringUtils.isEmpty(corpAccessTokenResult.getData())) {
            return Result.newError(corpAccessTokenResult.getCode(), corpAccessTokenResult.getMsg());
        }
        String corpAccessToken = corpAccessTokenResult.getData();
        if(StringUtils.isEmpty(corpAccessToken)){
            return Result.newError(ResultCodeEnum.REP_ACCESS_TOKEN_ERROR);
        }
        String getContact = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/batch/get_by_user?access_token=" + corpAccessToken;
        Map<String, Object> argMaps = Maps.newHashMap();
        argMaps.put("userid", userId);
        argMaps.put("cursor", null);
        argMaps.put("limit", 100);
        List<QyweixinExternalContactRsp> resultLists = Lists.newArrayList();
        log.info("QYWeixinManager.getContactList,argMaps={}",argMaps);
        String result = proxyOkHttpClient.postUrl(getContact, argMaps, new HashMap<>());
        int errorCode = (int)JSONPath.read(result, "$.errcode");
        log.info("QYWeixinManager.getContactList,result={}",result);
//        if(errorCode!=0) {
//            com.facishare.open.qywx.accountsync.result.Result<List<QyweixinAccountEmployeeMapping>> employeeMappingResult = qyweixinAccountBindInnerService.getEmployeeMapping(corpId,userId,-1,fsEa);
//            log.info("QYWeixinManager.getContactList,employeeMappingResult={}",employeeMappingResult);
//            if(CollectionUtils.isNotEmpty(employeeMappingResult.getData())) {
//                argMaps.put("userid",employeeMappingResult.getData().get(0).getIsvAccount());
//
//                log.info("QYWeixinManager.getContactList,argMaps2={}",argMaps);
//                result = proxyOkHttpClient.postUrl(getContact, argMaps, new HashMap<>());
//                log.info("QYWeixinManager.getContactList,result2={}",result);
//            }
//        }
        if (JSONPath.read(result, "$.errcode").equals(0)) {
            JSONArray jsonArray = JSONArray.parseArray(JSONPath.read(result, "$.external_contact_list").toString());
            resultLists.addAll(jsonArray.toJavaList(QyweixinExternalContactRsp.class));
        }
        Object nextCursor = JSONPath.read(result, "$.next_cursor");
        if(nextCursor!=null) {
            String cursor = nextCursor.toString();
            while (StringUtils.isNotEmpty(cursor)) {
                //继续获取下一页
                argMaps.put("cursor", cursor);
                result = proxyOkHttpClient.postUrl(getContact, argMaps, new HashMap<>());
                if (JSONPath.read(result, "$.errcode").equals(0)) {
                    JSONArray jsonArray = JSONArray.parseArray(JSONPath.read(result, "$.external_contact_list").toString());
                    resultLists.addAll(jsonArray.toJavaList(QyweixinExternalContactRsp.class));
                }
                nextCursor = JSONPath.read(result, "$.next_cursor");
                if(nextCursor==null) break;
                cursor = nextCursor.toString();
            }
        }
        if(JSONPath.read(result, "$.errcode").equals(0)) {
            return Result.newSuccess(resultLists);
        } else {
            return Result.newError(String.valueOf(JSONPath.read(result, "$.errcode")), String.valueOf(JSONPath.read(result, "$.errmsg")));
        }
    }

    public Result<QyweixinGroupChatDetail> getGroupChatDetail(String corpId, String appId, String roomId) {
        //查看是否有repAppId
        String finalAppId = getFinalAppId(corpId, appId);

        Result<String> tokenResult = getCorpAccessToken(finalAppId, corpId, false);
        if(!tokenResult.isSuccess() || StringUtils.isEmpty(tokenResult.getData())) {
            return Result.newError(tokenResult.getCode(), tokenResult.getMsg());
        }

        String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/get?access_token=" + tokenResult.getData();
        Map<String, Object> form = new HashMap<>();
        form.put("chat_id", roomId);
        form.put("need_name",1);
        String httpRsp = proxyOkHttpClient.postUrl(url, form, new HashMap<>());
        log.info("QYWeixinManager.getGroupChatDetail,httpRsp={}",httpRsp);
        QyweixinGroupChatDetail result = JSONObject.parseObject(httpRsp,QyweixinGroupChatDetail.class);
        if(result.isSuccess()) {
            return Result.newSuccess(result);
        } else {
            return Result.newError(String.valueOf(result.getErrcode()), result.getErrmsg());
        }
    }

    /**
     * 两种方案获取外部联系人列表
     */
    public Result<List<QyweixinExternalContactBatchInfo>> getContactListByTwoScheme(String token, String userId) {
        String getContact = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/batch/get_by_user?access_token=" + token;
        Map<String, Object> argMaps = Maps.newHashMap();
        argMaps.put("userid", userId);
        argMaps.put("cursor", null);
        argMaps.put("limit", 100);
        List<QyweixinExternalContactBatchInfo> resultLists = Lists.newArrayList();
        String result = proxyOkHttpClient.postUrl(getContact, argMaps, new HashMap<>());
        log.info("getContactList result:{}", result);
        if (JSONPath.read(result, "$.errcode").equals(0)) {
            JSONArray jsonArray = JSONArray.parseArray(JSONPath.read(result, "$.external_contact_list").toString());
            resultLists.addAll(jsonArray.toJavaList(QyweixinExternalContactBatchInfo.class));
        }

        String cursor = ObjectUtils.isNotEmpty(JSONPath.read(result, "$.next_cursor")) ? JSONPath.read(result, "$.next_cursor").toString() : null;
        while (StringUtils.isNotEmpty(cursor)) {
            //继续获取下一页
            argMaps.put("cursor", cursor);
            result = proxyOkHttpClient.postUrl(getContact, argMaps, new HashMap<>());
            if (JSONPath.read(result, "$.errcode").equals(0)) {
                JSONArray jsonArray = JSONArray.parseArray(JSONPath.read(result, "$.external_contact_list").toString());
                resultLists.addAll(jsonArray.toJavaList(QyweixinExternalContactBatchInfo.class));
            }
            cursor = ObjectUtils.isNotEmpty(JSONPath.read(result, "$.next_cursor")) ? JSONPath.read(result, "$.next_cursor").toString() : null;
        }
        if(JSONPath.read(result, "$.errcode").equals(0)) {
            return Result.newSuccess(resultLists);
        } else {
            return Result.newError(String.valueOf(JSONPath.read(result, "$.errcode")), String.valueOf(JSONPath.read(result, "$.errmsg")));
        }
    }


    /**
     * 自建应用和代开发获取token的方式一样，唯一的是corpSecret不同
     * @param corpSecret 自建应用的corpSecret需要客户填写的corpSecret，代开发的corpSecret为企业永久授权码
     * @param corpId
     * @return
     */
    public Result<String> getToken(String corpSecret, String corpId) {
        String combineAccount = Joiner.on("|").join(corpId, corpSecret);
        String corpAccessToken = cache.get(combineAccount);
        if(StringUtils.isEmpty(corpAccessToken)){
            return Result.newError(ResultCodeEnum.SYS_EXCEPTION);
        }
        return Result.newSuccess(corpAccessToken);
    }

    public Result<List<String>> getExternalContactEmployeeId(String corpId, String appId) {
        //查看是否有repAppId
        String finalAppId = getFinalAppId(corpId, appId);

        Result<String> tokenResult = getCorpAccessToken(finalAppId, corpId, false);
        if(!tokenResult.isSuccess() || StringUtils.isEmpty(tokenResult.getData())) {
            return Result.newError(tokenResult.getCode(), tokenResult.getMsg());
        }

        String getContact = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get_follow_user_list?access_token=" + tokenResult.getData();
        List<String> list = new LinkedList<>();
        String result = proxyOkHttpClient.getUrl(getContact, new HashMap<>());
        log.info("QYWeixinManager.getExternalContactEmployeeId result:{}", result);
        if (JSONPath.read(result, "$.errcode").equals(0)) {
            log.info("QYWeixinManager.getExternalContactEmployeeId result={}", result);
            list.addAll(JSONObject.parseObject(JSONPath.read(result, "$.follow_user").toString(), List.class));
        } else {
            return Result.newError(String.valueOf(JSONPath.read(result, "$.errcode")), String.valueOf(JSONPath.read(result, "$.errmsg")));
        }
        return Result.newSuccess(list);
    }

    /**
     * 把明文的corpId转换成密文的corpId
     * 企业微信已经把代开发应用和自建应用的ID升级为了密文，这个接口，就没有用了
     * @param corpId
     * @return
     */
    public Result<String> corpId2OpenCorpId(String corpId) {
        //密文的corpId长度是32，如果长度大于等于32，则认为当前corpId为密文
        if(StringUtils.length(corpId)>=32) {
            return Result.newSuccess(corpId);
        }
        //服务商企业只有明文，不处理
        if (ConfigCenter.QYWX_SERVICE_PROVIDER.equals(corpId)) {
            return Result.newSuccess(corpId);
        }
        Stopwatch stopwatch = Stopwatch.createStarted();
        Result<String> getProviderAccessTokenResult = getProviderAccessToken(null);   //获取服务商token
        String getContact = "https://qyapi.weixin.qq.com/cgi-bin/service/corpid_to_opencorpid?provider_access_token=" + getProviderAccessTokenResult.getData();
        Map<String, String> form = new HashMap<>();
        String isvCorpId = corpId;
        form.put("corpid", corpId);
        String result = proxyOkHttpClient.postUrl(getContact, form, new HashMap<>());
        log.info("QYWeixinManager.corpId2OpenCorpId result:{}", result);
        if (JSONPath.read(result, "$.errcode").equals(0)) {
            log.info("QYWeixinManager.corpId2OpenCorpId result={}", result);
            isvCorpId  = JSONPath.read(result, "$.open_corpid").toString();
        }
        log.info("QYWeixinManager.corpId2OpenCorpId time={}", stopwatch.elapsed(TimeUnit.MILLISECONDS));
        return Result.newSuccess(isvCorpId);
    }

    /**
     * 用第三方应用的身份，将明文userId批量转换成官方userId
     * @param accountList
     * @param corpId
     * @return
     */
    public Result<List<QyweixinOpenUserIdInfo>> userId2OpenUserId(List<String> accountList, String corpId, String appId) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        Result<String> corpAccessTokenResult = getCorpAccessToken(appId,corpId,false);
        if(!corpAccessTokenResult.isSuccess() || StringUtils.isEmpty(corpAccessTokenResult.getData())) {
            return Result.newError(corpAccessTokenResult.getCode(), corpAccessTokenResult.getMsg());
        }
        log.info("QYWeixinManager.userId2OpenUserId,getCorpAccessToken,time={}", stopwatch.elapsed(TimeUnit.MILLISECONDS));
        String getContact = "https://qyapi.weixin.qq.com/cgi-bin/batch/userid_to_openuserid?access_token=" + corpAccessTokenResult.getData();
        Map<String, Object> form = new HashMap<>();
        List<QyweixinOpenUserIdInfo> resultLists = new LinkedList<>();
        form.put("userid_list", accountList);
        String result = proxyOkHttpClient.postUrl(getContact, form, new HashMap<>());
        log.info("QYWeixinManager.userId2OpenUserId result:{}", result);
        log.info("QYWeixinManager.userId2OpenUserId,total time={}", stopwatch.elapsed(TimeUnit.MILLISECONDS));
        if (JSONPath.read(result, "$.errcode").equals(0)) {
            log.info("QYWeixinManager.userId2OpenUserId result={}", result);
            JSONArray jsonArray = JSONArray.parseArray(JSONPath.read(result, "$.open_userid_list").toString());
            resultLists.addAll(jsonArray.toJavaList(QyweixinOpenUserIdInfo.class));
            return Result.newSuccess(resultLists);
        } else {
            return Result.newError(String.valueOf(JSONPath.read(result, "$.errcode")), String.valueOf(JSONPath.read(result, "$.errmsg")));
        }
    }

//    public Result<List<QyweixinOpenUserIdInfo>> userId2OpenUserId(List<String> accountList, String corpId) {
//        return userId2OpenUserId(accountList, corpId, ConfigCenter.crmAppId);
//    }

    /**
     * 获取会话存档开启成员列表
     * @param token
     * @return
     */
    public Result<GetPermitUserListResult> getPermitUserList(String token) {
        //获取会话内容存档开启成员列表:自建应用、代开发都可以调用，但是返回的都是明文的账号
        String getContact = "https://qyapi.weixin.qq.com/cgi-bin/msgaudit/get_permit_user_list?access_token=" + token;
        List<String> list = new LinkedList<>();
        GetPermitUserListResult result = null;
        String response = proxyOkHttpClient.postUrl(getContact, new HashMap<>(), new HashMap<>());
        log.info("QYWeixinManager.getPermitUserList,response={}", response);
        result = JSONObject.parseObject(response,GetPermitUserListResult.class);
        if(QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(result.getErrcode())) {
            return Result.newSuccess(result);
        } else {
            return Result.newError(String.valueOf(result.getErrcode()), result.getErrmsg());
        }
    }

    public Result<List<String>> getPermitUserList2(String token) {
        Result<GetPermitUserListResult> result = getPermitUserList(token);
        if(result.isSuccess() && ObjectUtils.isNotEmpty(result.getData())) {
            return Result.newSuccess(result.getData().getIds());
        }
        return Result.newError(result.getCode(), result.getMsg());
    }

    /**
     * 查询对应roomid里面所有外企业的外部联系人的同意情况
     * @param token
     * @return
     */
    public Result<CheckRoomAgreeResult> checkRoomAgree(String token,String roomId) {
        String url = "https://qyapi.weixin.qq.com/cgi-bin/msgaudit/check_room_agree?access_token=" + token;
        CheckRoomAgreeResult result = null;
        Map<String,String> body = new HashMap<>();
        body.put("roomid",roomId);
        String response = proxyOkHttpClient.postUrl(url, body, new HashMap<>());
        log.info("QYWeixinManager.checkRoomAgree,response={}", response);
        result = JSONObject.parseObject(response,CheckRoomAgreeResult.class);
        if(QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(result.getErrCode())) {
            return Result.newSuccess(result);
        } else {
            return Result.newError(String.valueOf(result.getErrCode()), result.getErrMsg());
        }
    }

    public Result<Map<String, Object>> getExternalContact(String corpId, String appId, List<String> userIds, String next_cursor, int limit) {
        //查看是否有repAppId
        String finalAppId = getFinalAppId(corpId, appId);

        Result<String> tokenResult = getCorpAccessToken(finalAppId, corpId, false);
        if(!tokenResult.isSuccess() || StringUtils.isEmpty(tokenResult.getData())) {
            return Result.newError(tokenResult.getCode(), tokenResult.getMsg());
        }

        String getContact = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/batch/get_by_user?access_token=" + tokenResult.getData();
        Map<String, Object> argMaps = Maps.newHashMap();
        Map<String, Object> resultMap = Maps.newHashMap();
        argMaps.put("userid_list", userIds);
        argMaps.put("cursor", next_cursor);
        argMaps.put("limit", limit);
        List<QyweixinExternalContactBatchInfo> resultLists = Lists.newArrayList();
        String result = proxyOkHttpClient.postUrl(getContact, argMaps, new HashMap<>());
        log.info("getContactList result:{}", result);
        if (JSONPath.read(result, "$.errcode").equals(0)) {
            JSONArray jsonArray = JSONArray.parseArray(JSONPath.read(result, "$.external_contact_list").toString());
            resultLists.addAll(jsonArray.toJavaList(QyweixinExternalContactBatchInfo.class));
            String cursor = JSONPath.read(result, "$.next_cursor").toString();
            resultMap.put("external_contact_list", resultLists);
            resultMap.put("next_cursor", cursor);
            log.info("QYWeixinManager.getExternalContact result={}", resultMap);
        } else {
            return Result.newError(String.valueOf(JSONPath.read(result, "$.errcode")), String.valueOf(JSONPath.read(result, "$.errmsg")));
        }
        return Result.newSuccess(resultMap);
    }

    //获取外部联系人详情
    public Result<QyweixinExternalContact> queryExternalAccount(String corpSecret,String corpId,String external){
        String combineAccount = Joiner.on("|").join(corpId, corpSecret);
        String corpAccessToken = cache.get(combineAccount);
        String getContact = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get?access_token="+corpAccessToken+"&external_userid="+external;
        QyweixinExternalContact qyweixinExternalContact=null;
        String result = proxyOkHttpClient.getUrl(getContact, new HashMap<>());
        log.info("getContactList result:{}", result);
        if (JSONPath.read(result, "$.errcode").equals(0)) {
            qyweixinExternalContact=  JSONObject.parseObject(JSONPath.read(result, "$.external_contact").toString(),QyweixinExternalContact.class);
            return Result.newSuccess(qyweixinExternalContact);
        } else {
            return Result.newError(String.valueOf(JSONPath.read(result, "$.errcode")), String.valueOf(JSONPath.read(result, "$.errmsg")));
        }
    }

    private QyweixinExternalContactRsp convertFollowUserId(QyweixinExternalContactRsp externalContactRsp,String corpId,String userId, String appId) {
        if(externalContactRsp.isSuccess()) {
            QyweixinFollowUser validFollowUser = null;
            for(QyweixinFollowUser followUser : externalContactRsp.getFollow_user()) {
                if(StringUtils.equalsIgnoreCase(followUser.getUserid(),userId)) {
                    validFollowUser = followUser;
                    break;
                }
            }
            if(validFollowUser==null) {
                boolean isBeak = false;
                for(QyweixinFollowUser followUser : externalContactRsp.getFollow_user()) {
                    List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByNotPage(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.qywx).outEa(corpId).appId(appId).outEmpId(followUser.getUserid()).build());
                    if(CollectionUtils.isNotEmpty(employeeBindEntities)) {
                        for (OuterOaEmployeeBindEntity employeeBindEntity : employeeBindEntities) {
                            if(StringUtils.equalsIgnoreCase(employeeBindEntity.getOutEmpId(),userId)
                                    || StringUtils.equalsIgnoreCase(employeeBindEntity.getOutEmpId(),userId)) {
                                followUser.setUserid(userId);
                                validFollowUser = followUser;
                                isBeak = true;
                                break;
                            }
                        }
                    }
                    if (isBeak) {
                        break;
                    }
                }
            }
            if(validFollowUser!=null) {
                externalContactRsp.getFollow_user().clear();
                externalContactRsp.getFollow_user().add(validFollowUser);
                return externalContactRsp;
            }
        }
        return null;
    }

    /**
     * 获取外部联系人详情
     * @param corpSecret
     * @param corpId
     * @param externalUserId
     * @param userId
     * @return
     */
    public Result<QyweixinExternalContactRsp> getExternalContactDetail(String corpSecret,
                                                                       String corpId,
                                                                       String externalUserId,
                                                                       String userId,
                                                                       String appId){
        //查看是否有repAppId
        String finalAppId = getFinalAppId(corpId, appId);

        Result<String> corpAccessTokenResult = getCorpAccessToken(finalAppId, corpId, false);
        if(!corpAccessTokenResult.isSuccess() || StringUtils.isEmpty(corpAccessTokenResult.getData())) {
            return Result.newError(corpAccessTokenResult.getCode(), corpAccessTokenResult.getMsg());
        }
        String corpAccessToken = corpAccessTokenResult.getData();
        String url="https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get?access_token="+corpAccessToken+"&external_userid="+externalUserId;
        String result = proxyOkHttpClient.getUrl(url, new HashMap<>());
        log.info("QYWeixinManager.getExternalContactDetail,result={}", result);
        QyweixinExternalContactRsp externalContactRsp = JSONObject.parseObject(result,QyweixinExternalContactRsp.class);
        if(externalContactRsp.isSuccess()) {
            return Result.newSuccess(StringUtils.isNotEmpty(userId) ? convertFollowUserId(externalContactRsp,corpId,userId,appId) : externalContactRsp);
        } else {
            return Result.newError(String.valueOf(externalContactRsp.getErrcode()), externalContactRsp.getErrmsg());
        }
    }

    public Result<String> toServiceExternalUserId(String corpSecretCode, String externalUserId) {
        Map<String, String> form = Maps.newHashMap();
        form.put("external_userid", externalUserId);
        log.info("QYWeixinManager.toServiceExternalUserId,form={}", form);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/to_service_external_userid?access_token=" + corpSecretCode;
        String httpRsp = proxyOkHttpClient.postUrl(url, form, new HashMap<>());
        JSONObject object = JSONObject.parseObject(httpRsp);
        String externalUserIdByISV = String.valueOf(object.get("external_userid"));
        log.info("QYWeixinManager.toServiceExternalUserId,externalUserIdByISV:{}", externalUserIdByISV);
        if (JSONPath.read(httpRsp, "$.errcode").equals(0)) {
            return Result.newSuccess(externalUserIdByISV);
        } else {
            return Result.newError(String.valueOf(JSONPath.read(httpRsp, "$.errcode")), String.valueOf(JSONPath.read(httpRsp, "$.errmsg")));
        }
    }

    /**
     * 直接连状态一起返回
     * @param corpSecretCode
     * @param externalUserId
     * @return
     */
    public Result<String> toServiceExternalUserId2(String corpSecretCode, String externalUserId) {
        Map<String, String> form = Maps.newHashMap();
        form.put("external_userid", externalUserId);
        log.info("QYWeixinManager.toServiceExternalUserId2,form={}", form);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/to_service_external_userid?access_token=" + corpSecretCode;
        String httpRsp = proxyOkHttpClient.postUrl(url, form, new HashMap<>());
        return Result.newSuccess(httpRsp);
    }

    //自建应用获取accessToken

    public Result<String> getAppAccessToken(String combineAccount) {
        List<String> account = Splitter.on("|").splitToList(combineAccount);
        String url = String.format(getTokenUrl, account.get(0), account.get(1));
        log.info("trace getAppAccessToken combineAccount:{}",combineAccount);
        String result = proxyOkHttpClient.getUrl(url, new HashMap<>());
        if (JSONPath.read(result, "$.errcode").equals(0)) {
            return Result.newSuccess(JSONPath.read(result, "$.access_token").toString());
        }
        log.info("trace getToken fail corpId:{},result:{}", account, result);
        return Result.newError(String.valueOf(JSONPath.read(result, "$.errcode")), String.valueOf(JSONPath.read(result, "$.errmsg")));
    }


    /**
     * 企业是否取消授权
     *
     * @param corpId corpId
     * @param appId  appId
     * @return true:已取消。false:未取消
     */
    public Result<Boolean> isCancelAuth(String corpId, String appId) {
        OuterOaAppInfoEntity oaAppInfoEntity = outerOaAppInfoManager.getEntity(ChannelEnum.qywx, corpId, appId);
        log.info("isCancelAuth queryQyweixinCorpBind success. corpId:{}, appId:{}, oaAppInfoEntity:{}", corpId, appId, oaAppInfoEntity);
        if (Objects.isNull(oaAppInfoEntity)) {
            return Result.newSuccess(true);
        }
        return Result.newSuccess(oaAppInfoEntity.getStatus() == OuterOaAppInfoStatusEnum.stop);
    }

    public Result<String> uploadTranslateFile(String corpId, File file) {
        Result<String> accessTokenResult = getProviderAccessToken(false);
        String uploadUrl = "https://qyapi.weixin.qq.com/cgi-bin/service/media/upload?provider_access_token=" + accessTokenResult.getData() + "&type=file";
        //返回的uploadResult 是上传需要转译的文件
        TranslateFileType.UploadResult uploadResult = new Gson().fromJson(httpHelper.httpClientUploadFile(uploadUrl, file), TranslateFileType.UploadResult.class);

        log.info("upload file success. corpId:{}, uploadUrl:{}, uploadResult:{}", corpId, uploadUrl, uploadResult);
        if (uploadResult.getErrcode() != TranslateFileType.SUCCESS_CODE) {
            log.info("upload file failed. corpId:{}, url:{}, result:{}", corpId, uploadUrl, uploadResult);
            return Result.newError(String.valueOf(uploadResult.getErrcode()), uploadResult.getErrmsg());
        }

        String mediaId = uploadResult.getMediaId();
        String translateUrl = "https://qyapi.weixin.qq.com/cgi-bin/service/contact/id_translate?provider_access_token=" + accessTokenResult.getData();
        Map<String, Object> form = Maps.newHashMap();
        form.put("auth_corpid", corpId);
        form.put("media_id_list", Lists.newArrayList(mediaId));
        form.put("output_file_name", file.getName());
        //异步通讯录ID转译。
        TranslateFileType.GetJobIdResult getJobIdResult = new Gson().fromJson(proxyOkHttpClient.postUrl(translateUrl, form, new HashMap<>()), TranslateFileType.GetJobIdResult.class);
        log.info("get job id success. corpId:{}, url:{}, form:{}, result:{}", corpId, translateUrl, form, getJobIdResult);
        if (getJobIdResult.getErrcode() != TranslateFileType.SUCCESS_CODE) {
            log.info("get jobId failed. corpId:{}, url:{}, result:{}", corpId, uploadUrl, uploadResult);
            return Result.newError(String.valueOf(getJobIdResult.getErrcode()), getJobIdResult.getErrmsg());
        }
        return Result.newSuccess(getJobIdResult.getJobId());
    }

    public Result<Pair<Integer, String>> getTranslateUrl(String ea, String jobId) {
        Result<String> accessTokenResult = getProviderAccessToken(false);
        String url =
                "https://qyapi.weixin.qq.com/cgi-bin/service/batch/getresult?provider_access_token=" + accessTokenResult.getData() + "&jobid=" + jobId;
        TranslateFileType.GetResult getResult = new Gson().fromJson(proxyOkHttpClient.postUrl(url, Collections.emptyMap(), new HashMap<>()), TranslateFileType.GetResult.class);
        log.info("getTranslateUrl success. ea:{}, jobId:{}, url{}, result:{}", ea, jobId, url, getResult);
        if (getResult.getErrcode() != TranslateFileType.SUCCESS_CODE) {
            log.info("get translate url failed. ea:{}, jobId:{}, result:{}", ea, jobId, getResult);
            return Result.newError(String.valueOf(getResult.getErrcode()), getResult.getErrmsg());
        }
        return Result.newSuccess(new Pair<>(getResult.getStatus(), getResult.getUrl()));
    }

    /**
     * 获取应用的管理员列表，只支持三方应用，不支持代开发应用，企微官方不支持
     *
     * @param auth_corpid
     * @param appId
     * @param agentId
     * @return
     */
    public Result<QyweixinGetAdminListRsp> getAdminList(String auth_corpid, String appId, int agentId) {
        String suiteAccessToken = getSuiteAccessTokenFromRedis(appId);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/get_admin_list?suite_access_token=" + suiteAccessToken;
        Map<String, String> form = Maps.newHashMap();
        form.put("auth_corpid", auth_corpid);
        form.put("agentid", agentId+"");
        log.info("QYWeixinManager.getAdminList,form={}", form);
        String httpRsp = proxyOkHttpClient.postUrl(url, form, new HashMap<>());
        QyweixinGetAdminListRsp qyweixinGetAdminListRsp = new Gson().fromJson(httpRsp, QyweixinGetAdminListRsp.class);
        log.info("QYWeixinManager.getAdminList,qyweixinGetAdminListRsp:{}", qyweixinGetAdminListRsp);
        if(qyweixinGetAdminListRsp.isSuccess()) {
            return Result.newSuccess(qyweixinGetAdminListRsp);
        } else {
            return Result.newError(String.valueOf(qyweixinGetAdminListRsp.getErrcode()), qyweixinGetAdminListRsp.getErrmsg());
        }
    }

    public Result<String> sendAutoMessage(Map<String, String> headerMap, Map<String, Object> bodyMap){
        String url = CrmUrlUtils.activeRecordUrl;
        String httpRsp = proxyOkHttpClient.postUrl(url, bodyMap, headerMap);
        return Result.newSuccess(httpRsp);
    }

    public Result<String> switchExternalContactEmployeeId(String outEa, List<String> externalUserIds, String appId) {
        //查看是否有repAppId
        String finalAppId = getFinalAppId(outEa, appId);

        Result<String> tokenResult = getCorpAccessToken(finalAppId, outEa, false);
        if(!tokenResult.isSuccess() || StringUtils.isEmpty(tokenResult.getData())) {
            return Result.newError(tokenResult.getCode(), tokenResult.getMsg());
        }
        String url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get_new_external_userid?access_token=" + tokenResult.getData();
        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put("external_userid_list", externalUserIds);
        String result = proxyOkHttpClient.postUrl(url, resultMap, new HashMap<>());
        log.info("qyWeixinManager.switchExternalContactEmployeeId result:{}", result);
        return Result.newSuccess(result);
    }

    public Result<String> finishExternalMigration(String outEa) {
        Result<String> getProviderAccessTokenResult = getProviderAccessToken(null);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/externalcontact/finish_external_userid_migration?provider_access_token=" + getProviderAccessTokenResult.getData();
        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put("corpid", outEa);
        String result = proxyOkHttpClient.postUrl(url, resultMap, new HashMap<>());
        log.info("qyWeixinManager.switchExternalContactEmployeeId result:{}", result);
        return Result.newSuccess(result);
    }

    /**
     * 获取应用的接口许可状态
     * @param corpId
     * @param appId
     * @return
     */
    public Result<AppLicenseInfo> getAppLicenseInfo(String corpId,String appId) {
        Result<String> getProviderAccessTokenResult = getProviderAccessToken(null);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/license/get_app_license_info?provider_access_token=" + getProviderAccessTokenResult.getData();
        Map<String, Object> map = Maps.newHashMap();
        map.put("corpid", corpId);
        map.put("suite_id",appId);

        AppLicenseInfo appLicenseInfo = null;
        String result = proxyOkHttpClient.postUrl(url, map, new HashMap<>());
        log.info("QYWeixinManager.getAppLicenseInfo, result={}", result);
        appLicenseInfo = JSONObject.parseObject(result,AppLicenseInfo.class);
        if(appLicenseInfo.getErrcode() == 0) {
            return Result.newSuccess(appLicenseInfo);
        } else {
            return Result.newError(String.valueOf(appLicenseInfo.getErrcode()), appLicenseInfo.getErrmsg());
        }
    }

    /**
     * 获取指定的应用详情，包括应用的可见范围
     * @param corpId
     * @param appId
     * @return
     */
    public Result<AppInfo> getAppInfo(String corpId, String appId) {
        Result<String> tokenResult = getCorpAccessToken(appId, corpId, false);
        if(!tokenResult.isSuccess() || StringUtils.isEmpty(tokenResult.getData())) {
            return Result.newError(tokenResult.getCode(), tokenResult.getMsg());
        }
        OuterOaAppInfoEntity oaAppInfoEntity = outerOaAppInfoManager.getEntity(ChannelEnum.qywx, corpId, appId);
        if(ObjectUtils.isEmpty(oaAppInfoEntity)) {
            return Result.newError(ResultCodeEnum.MISSING_PARAMETER);
        }
        QyweixinAppInfoParams qyweixinAppInfoParams = JSON.parseObject(oaAppInfoEntity.getAppInfo(), QyweixinAppInfoParams.class);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/agent/get?access_token={accessToken}&agentid={agentId}";
        url = url.replace("{accessToken}",tokenResult.getData()).replace("{agentId}",String.valueOf(qyweixinAppInfoParams.getAuthAppInfo().getAgentId()));

        log.info("QYWeixinManager.getAppInfo, url={}", url);
        AppInfo appInfo = null;
        String result = proxyOkHttpClient.getUrl(url, new HashMap<>());
        log.info("QYWeixinManager.getAppInfo, result={}", result);
        appInfo = JSONObject.parseObject(result, AppInfo.class);
        if(appInfo.getErrcode() == 0) {
            return Result.newSuccess(appInfo);
        } else {
            return Result.newError(String.valueOf(appInfo.getErrcode()), appInfo.getErrmsg());
        }
    }

    private List<String> getDepartmentEmployeeId(String corpId, String appId,List<String> partyList) {
        List<String> userIdList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(partyList)) {
            for(String partyId : partyList) {
                Result<QyweixinDepartmentEmployeeListRsp> departmentEmployeeListRspResult = getDepartmentEmployeeList(appId, corpId, partyId);
                if(departmentEmployeeListRspResult.isSuccess() && departmentEmployeeListRspResult.getData()!=null && CollectionUtils.isNotEmpty(departmentEmployeeListRspResult.getData().getUserlist())) {
                    List<String> list = departmentEmployeeListRspResult.getData().getUserlist().stream()
                            .map(QyweixinUserDetailInfoRsp::getUserid)
                            .collect(Collectors.toList());
                    userIdList.addAll(list);
                }
            }
        }

        return userIdList;
    }

    public Result<List<String>> getUserListInAppVisibleRange(String corpId, String appId) {
        List<String> userIdList = new ArrayList<>();

        Result<AppInfo> appInfoResult = getAppInfo(corpId,appId);
        if(!appInfoResult.isSuccess() || ObjectUtils.isEmpty(appInfoResult.getData())) return Result.newError(appInfoResult.getCode(), appInfoResult.getMsg());
        AppInfo appInfo = appInfoResult.getData();
        if(appInfo.getAllow_userinfos()!=null && CollectionUtils.isNotEmpty(appInfo.getAllow_userinfos().getUser())) {
            for(AppInfo.AllowUserInfos.User user : appInfo.getAllow_userinfos().getUser()) {
                userIdList.add(user.getUserid());
            }
        }

        if(appInfo.getAllow_partys()!=null && CollectionUtils.isNotEmpty(appInfo.getAllow_partys().getPartyid())) {
            userIdList.addAll(getDepartmentEmployeeId(corpId,appId,appInfo.getAllow_partys().getPartyid()));
        }

        if(appInfo.getAllow_tags()!=null && CollectionUtils.isNotEmpty(appInfo.getAllow_tags().getTagid())) {
            for(String tagId : appInfo.getAllow_tags().getTagid()) {
                Result<QyweixinTagEmployeeListRsp> tagEmployeeListRspResult = getTagEmployeeList(appId, corpId, tagId);
                if(tagEmployeeListRspResult.isSuccess() && tagEmployeeListRspResult.getData()!=null && CollectionUtils.isNotEmpty(tagEmployeeListRspResult.getData().getUserlist())) {
                    QyweixinTagEmployeeListRsp tagEmployeeListRsp = tagEmployeeListRspResult.getData();
                    List<String> list = tagEmployeeListRsp.getUserlist().stream()
                            .map(QyweixinUserDetailInfoRsp::getUserid)
                            .collect(Collectors.toList());
                    userIdList.addAll(list);
                    userIdList.addAll(getDepartmentEmployeeId(corpId,appId,tagEmployeeListRsp.getPartylist()));
                }
            }
        }

        userIdList=userIdList.stream().distinct().collect(Collectors.toList());

        return Result.newSuccess(userIdList);
    }

//    public void senEaList(List<String> ea) {
//        FsEaArg arg = new FsEaArg();
//        arg.setEaList(ea);
//        try {
//            sendFsEaApi.sendFsEa(arg);
//        } catch (Exception e) {
//            throw new RuntimeException(arg.toString());
//        }
//    }

    /**
     * 代开发应用设置和企业迁移完成
     * 1.userid与corpid只能同时设置为升级模式，external_userid可以单独升级。
     * 2.此接口还可为第三方应用设置迁移完成状态，若只传入正确corpid，未传入代开发应用agentid，则视为：更新该企业下同服务商所有第三方应用的对应升级状态
     * @param corpId
     * @param agentId
     * @return
     */
    public Result<String> enterpriseBind2OpenEnterpriseBind(String corpId, String agentId) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        Result<String> getProviderAccessTokenResult = getProviderAccessToken(null);   //获取服务商token
        String getContact = "https://qyapi.weixin.qq.com/cgi-bin/service/finish_openid_migration?provider_access_token=" + getProviderAccessTokenResult.getData();
        Map<String, Object> form = new HashMap<>();
        String result = null;
        form.put("corpid", corpId);
        if(StringUtils.isNotEmpty(agentId)) {
            form.put("agentid", agentId);
        }
        form.put("openid_type", Lists.newArrayList(1, 3));
        result = proxyOkHttpClient.postUrl(getContact, form, new HashMap<>());
        log.info("QYWeixinManager.enterpriseBind2OpenEnterpriseBind result:{}", result);
        log.info("QYWeixinManager.enterpriseBind2OpenEnterpriseBind time={}", stopwatch.elapsed(TimeUnit.MILLISECONDS));
        return Result.newSuccess(result);
    }

    public Result<String> getEnterpriseAccountMigration(String corpId, String appId) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        Result<String> tokenResult = getCorpAccessToken(appId, corpId, false);
        if(!tokenResult.isSuccess() || StringUtils.isEmpty(tokenResult.getData())) {
            return Result.newError(tokenResult.getCode(), tokenResult.getMsg());
        }
        String getContact = "https://qyapi.weixin.qq.com/cgi-bin/corp/get_openid_migration?access_token=" + tokenResult.getData();
        Map<String, String> form = new HashMap<>();
        String result = null;
        result = proxyOkHttpClient.postUrl(getContact, form, new HashMap<>());
        log.info("QYWeixinManager.getEnterpriseAccountMigration result:{}", result);
        log.info("QYWeixinManager.getEnterpriseAccountMigration time={}", stopwatch.elapsed(TimeUnit.MILLISECONDS));
        return Result.newSuccess(result);
    }

    public Result<String> getUserIdByPhone(String corpId, String mobile) {
        return getUserIdByPhone(corpId, ConfigCenter.crmAppId, mobile);
    }

    public Result<String> getUserIdByPhone(String corpId, String appId, String mobile) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        Result<String> tokenResult = getCorpAccessToken(appId, corpId, false);
        if(!tokenResult.isSuccess() || StringUtils.isEmpty(tokenResult.getData())) {
            return Result.newError(tokenResult.getCode(), tokenResult.getMsg());
        }
        String getContact = "https://qyapi.weixin.qq.com/cgi-bin/user/getuserid?access_token=" + tokenResult.getData();
        Map<String, String> form = new HashMap<>();
        form.put("mobile", mobile);
        String userId = null;
        String result = proxyOkHttpClient.postUrl(getContact, form, new HashMap<>());
        log.info("QYWeixinManager.getUserIdByPhone result:{}", result);
        if(JSONPath.read(result, "$.errcode").equals(0)) {
            userId = JSONPath.read(result, "$.userid").toString();
            return Result.newSuccess(userId);
        }
        log.info("QYWeixinManager.getUserIdByPhone time={}", stopwatch.elapsed(TimeUnit.MILLISECONDS));
        return Result.newError(String.valueOf(JSONPath.read(result, "$.errcode")), String.valueOf(JSONPath.read(result, "$.errmsg")));
    }

    /**
     * 获取客户列表
     * 企业可通过此接口获取指定成员添加的客户列表。客户是指配置了客户联系功能的成员所添加的外部联系人。
     * 没有配置客户联系功能的成员，所添加的外部联系人将不会作为客户返回。
     * @return
     */
    public Result<List<String>> getExternalContactList(String corpId, String appId, String userId) {
        Stopwatch stopwatch = Stopwatch.createStarted();

        //查看是否有repAppId
        String finalAppId = getFinalAppId(corpId, appId);

        Result<String> tokenResult = getCorpAccessToken(finalAppId, corpId, false);
        if(!tokenResult.isSuccess() || StringUtils.isEmpty(tokenResult.getData())) {
            return Result.newError(tokenResult.getCode(), tokenResult.getMsg());
        }
        String getContact = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/list?access_token=" + tokenResult.getData() + "&userid=" + userId;
        List<String> externalContactList = null;
        String result = proxyOkHttpClient.getUrl(getContact, new HashMap<>());
        log.info("QYWeixinManager.getExternalContactList time={}", stopwatch.elapsed(TimeUnit.MILLISECONDS));
        if(JSONPath.read(result, "$.errcode").equals(0)) {
            String result1 = JSONArray.toJSONString(JSONPath.read(result, "$.external_userid"));
            externalContactList = JSONArray.parseArray(result1, String.class);
            log.info("QYWeixinManager.getExternalContactList result:{}", result);
            return Result.newSuccess(externalContactList);
        }
        return Result.newError(String.valueOf(JSONPath.read(result, "$.errcode")), String.valueOf(JSONPath.read(result, "$.errmsg")));
    }

    /**
     * 企业可通过此接口，转接在职成员的客户给其他成员。
     * 注：该接口每次只能转接100个客户。
     * @returnw2`
     */
    public Result<List<QyweixinTransferCustomerResult>> externalContactTransferCustomer(QyweixinTransferCustomer customerInfo, String corpId, Boolean isResigned, String appId) {
        Stopwatch stopwatch = Stopwatch.createStarted();

        //查看是否有repAppId
        String finalAppId = getFinalAppId(corpId, appId);

        Result<String> tokenResult = getCorpAccessToken(finalAppId, corpId, false);
        if(!tokenResult.isSuccess() || StringUtils.isEmpty(tokenResult.getData())) {
            return Result.newError(tokenResult.getCode(), tokenResult.getMsg());
        }
        String getContact = null;
        Map<String, Object> form = new HashMap<>();
        form.put("handover_userid", customerInfo.getHandoverUserId());
        form.put("takeover_userid", customerInfo.getTakeoverUserId());
        if(isResigned) {
            getContact = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/resigned/transfer_customer?access_token=" + tokenResult.getData();
        } else {
            getContact = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/transfer_customer?access_token=" + tokenResult.getData();
            form.put("transfer_success_msg", customerInfo.getTransferSuccessMsg());
        }
        List<QyweixinTransferCustomerResult> transferCustomerResult = new LinkedList<>();
        Map<Integer, List<String>> map = this.groupList(customerInfo.getExternalUserId());
        Set<Integer> set = map.keySet();
        Iterator<Integer> iterator = set.iterator();
        while (iterator.hasNext()) {
            Integer key = iterator.next();
            form.put("external_userid", map.get(key));
            String result = proxyOkHttpClient.postUrl(getContact, form, new HashMap<>());
            log.info("QYWeixinManager.externalContactTransferCustomer result:{}", result);
            if(JSONPath.read(result, "$.errcode").equals(0)) {
                String result1 = JSONArray.toJSONString(JSONPath.read(result, "$.customer"));
                List<QyweixinTransferCustomerResult> transferCustomerResult1 = JSONArray.parseArray(result1, QyweixinTransferCustomerResult.class);
                if(CollectionUtils.isNotEmpty(transferCustomerResult1)) {
                    transferCustomerResult.addAll(transferCustomerResult1);
                }
            } else {
                return Result.newError(String.valueOf(JSONPath.read(result, "$.errcode")), String.valueOf(JSONPath.read(result, "$.errmsg")));
            }
        }
        log.info("QYWeixinManager.externalContactTransferCustomer time={}", stopwatch.elapsed(TimeUnit.MILLISECONDS));
        return Result.newSuccess(transferCustomerResult);
    }


    /**
     * 实现java 中 list集合中有超过100条数据,每100条为一组取出
     * @param list 可穿入超过100条数据的List
     * @return map 每一Kye中有100条数据的List
     */
    public Map groupList(List list) {

        int listSize = list.size();
        int toIndex = 100;
        Map map = new HashMap();     //用map存起来新的分组后数据
        int keyToken = 0;
        for (int i = 0; i < list.size(); i += 100) {
            if (i + 100 > listSize) {        //作用为toIndex最后没有100条数据则剩余几条newList中就装几条
                toIndex = listSize - i;
            }
            List newList = list.subList(i, i + toIndex);
            map.put(keyToken, newList);
            keyToken++;
        }

        return map;
    }

    /**
     * 企业和第三方可通过此接口查询在职成员的客户转接情况。
     * @return
     */
    public Result<QyweixinTransferCustomerStatusResult> transferCustomerStatusResult(QyweixinTransferCustomerStatusInfo customerStatusInfo, String corpId, Boolean isResigned, String appId) {
        Stopwatch stopwatch = Stopwatch.createStarted();

        //查看是否有repAppId
        String finalAppId = getFinalAppId(corpId, appId);

        Result<String> tokenResult = getCorpAccessToken(finalAppId, corpId, false);
        if(!tokenResult.isSuccess() || StringUtils.isEmpty(tokenResult.getData())) {
            return Result.newError(tokenResult.getCode(), tokenResult.getMsg());
        }
        String getContact = null;
        if(isResigned) {
            getContact = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/resigned/transfer_result?access_token=" + tokenResult.getData();
        } else {
            getContact = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/transfer_result?access_token=" + tokenResult.getData();
        }

        Map<String, Object> form = new HashMap<>();
        form.put("handover_userid", customerStatusInfo.getHandoverUserId());
        form.put("takeover_userid", customerStatusInfo.getTakeoverUserId());
        form.put("cursor", customerStatusInfo.getCursor());
        QyweixinTransferCustomerStatusResult transferCustomerStatusResult = null;
        String result = proxyOkHttpClient.postUrl(getContact, form, new HashMap<>());
        log.info("QYWeixinManager.transferCustomerStatusResult result:{}", result);
        log.info("QYWeixinManager.transferCustomerStatusResult time={}", stopwatch.elapsed(TimeUnit.MILLISECONDS));
        if(JSONPath.read(result, "$.errcode").equals(0)) {
            //String result1 = JSONArray.toJSONString(JSONPath.read(result, "$.customer"));
            transferCustomerStatusResult = JSON.parseObject(result, QyweixinTransferCustomerStatusResult.class);
            return Result.newSuccess(transferCustomerStatusResult);
        }
        return Result.newError(String.valueOf(JSONPath.read(result, "$.errcode")), String.valueOf(JSONPath.read(result, "$.errmsg")));
    }

    /**
     * 该接口用于获取配置过客户群管理的客户群列表。
     * @param groupChatInfo
     * @param corpId
     * @return
     */
    public Result<QyweixinGroupChatResult> getGroupChatList(QyweixinGroupChatInfo groupChatInfo, String corpId, String appId) {
        Stopwatch stopwatch = Stopwatch.createStarted();

        //查看是否有repAppId
        String finalAppId = getFinalAppId(corpId, appId);

        Result<String> tokenResult = getCorpAccessToken(finalAppId, corpId, false);
        if(!tokenResult.isSuccess() || StringUtils.isEmpty(tokenResult.getData())) {
            return Result.newError(tokenResult.getCode(), tokenResult.getMsg());
        }
        String getContact = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/list?access_token=" + tokenResult.getData();
        Map<String, Object> form = new HashMap<>();
        form.put("status_filter", groupChatInfo.getStatusFilter());
        Map<String, Object> userIdList = new HashMap<>();
        if(groupChatInfo.getOwnerFilter()!=null) {
            userIdList.put("userid_list", groupChatInfo.getOwnerFilter().getUserIdList());
            form.put("owner_filter", userIdList);
        }
        form.put("cursor", groupChatInfo.getCursor());
        form.put("limit", groupChatInfo.getLimit());
        QyweixinGroupChatResult groupChatResult = null;
        String result = proxyOkHttpClient.postUrl(getContact, form, new HashMap<>());
        log.info("QYWeixinManager.getGroupChatList result:{}", result);
        log.info("QYWeixinManager.getGroupChatList time={}", stopwatch.elapsed(TimeUnit.MILLISECONDS));
        if(JSONPath.read(result, "$.errcode").equals(0)) {
            //String result1 = JSONArray.toJSONString(JSONPath.read(result, "$.customer"));
            groupChatResult = JSON.parseObject(result, QyweixinGroupChatResult.class);
            return Result.newSuccess(groupChatResult);
        }
        return Result.newError(String.valueOf(JSONPath.read(result, "$.errcode")), String.valueOf(JSONPath.read(result, "$.errmsg")));
    }


    /**
     * 企业可通过此接口，将在职成员为群主的群，分配给另一个客服成员。
     * @param transferGroupChatInfo
     * @param corpId
     * @return
     */
    public Result<List<QyweixinTransferGroupChatResult>> externalContactTransferGroupChat(QyweixinTransferGroupChatInfo transferGroupChatInfo, String corpId, Boolean isResigned, String appId) {
        Stopwatch stopwatch = Stopwatch.createStarted();

        //查看是否有repAppId
        String finalAppId = getFinalAppId(corpId, appId);

        Result<String> tokenResult = getCorpAccessToken(finalAppId, corpId, false);
        if(!tokenResult.isSuccess() || StringUtils.isEmpty(tokenResult.getData())) {
            return Result.newError(tokenResult.getCode(), tokenResult.getMsg());
        }
        String getContact = null;
        if(isResigned) {
            getContact = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/transfer?access_token=" + tokenResult.getData();
        } else {
            getContact = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/groupchat/onjob_transfer?access_token=" + tokenResult.getData();
        }
        Map<String, Object> form = new HashMap<>();
        form.put("chat_id_list", transferGroupChatInfo.getChatIdList());
        form.put("new_owner", transferGroupChatInfo.getNewOwner());
        List<QyweixinTransferGroupChatResult> transferGroupChatResult = null;
        String result = proxyOkHttpClient.postUrl(getContact, form, new HashMap<>());
        log.info("QYWeixinManager.externalContactTransferGroupChat result:{}", result);
        log.info("QYWeixinManager.externalContactTransferGroupChat time={}", stopwatch.elapsed(TimeUnit.MILLISECONDS));
        if(JSONPath.read(result, "$.errcode").equals(0)) {
            String result1 = JSONArray.toJSONString(JSONPath.read(result, "$.failed_chat_list"));
            transferGroupChatResult = JSONArray.parseArray(result1, QyweixinTransferGroupChatResult.class);
            return Result.newSuccess(transferGroupChatResult);
        }
        return Result.newError(String.valueOf(JSONPath.read(result, "$.errcode")), String.valueOf(JSONPath.read(result, "$.errmsg")));
    }

    /**
     * 企业和第三方可通过此接口，获取所有离职成员的客户列表，并可进一步调用分配离职成员的客户接口将这些客户重新分配给其他企业成员。
     * @param externalContactInfo
     * @param corpId
     * @return
     */
    public Result<QyweixinUnassignedExternalContactResult> unassignedExternalContact(QyweixinUnassignedExternalContactInfo externalContactInfo, String corpId, String appId) {
        Stopwatch stopwatch = Stopwatch.createStarted();

        //查看是否有repAppId
        String finalAppId = getFinalAppId(corpId, appId);

        Result<String> tokenResult = getCorpAccessToken(finalAppId, corpId, false);
        if(!tokenResult.isSuccess() || StringUtils.isEmpty(tokenResult.getData())) {
            return Result.newError(tokenResult.getCode(), tokenResult.getMsg());
        }
        String getContact = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get_unassigned_list?access_token=" + tokenResult.getData();
        Map<String, Object> form = new HashMap<>();
        if(!externalContactInfo.getPageId().equals(-1)) {
            form.put("page_id", externalContactInfo.getPageId());
        }
        form.put("cursor", externalContactInfo.getCursor());
        form.put("page_size", externalContactInfo.getPageSize());
        QyweixinUnassignedExternalContactResult externalContactResult = null;
        String result = proxyOkHttpClient.postUrl(getContact, form, new HashMap<>());
        log.info("QYWeixinManager.unassignedExternalContact result:{}", result);
        log.info("QYWeixinManager.unassignedExternalContact time={}", stopwatch.elapsed(TimeUnit.MILLISECONDS));
        if(JSONPath.read(result, "$.errcode").equals(0)) {
            externalContactResult = JSON.parseObject(result, QyweixinUnassignedExternalContactResult.class);
            return Result.newSuccess(externalContactResult);
        }
        return Result.newError(String.valueOf(JSONPath.read(result, "$.errcode")), String.valueOf(JSONPath.read(result, "$.errmsg")));
    }

    /**
     * 查询员工详情，状态码一同返回，这里不做处理
     * @param corpId
     * @param userId
     * @return
     */
    public Result<QyweixinUserDetailInfoRsp> getUserInfoResult(String appId, String corpId, String userId) {
        Result<String> corpAccessTokenResult = getCorpAccessToken(appId, corpId, false);
        if(!corpAccessTokenResult.isSuccess() || StringUtils.isEmpty(corpAccessTokenResult.getData())) {
            return Result.newError(corpAccessTokenResult.getCode(), corpAccessTokenResult.getMsg());
        }
        String url = "https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token=" + corpAccessTokenResult.getData() + "&userid=" + userId;
        String httpRsp = proxyOkHttpClient.getUrl(url, new HashMap<>());
        QyweixinUserDetailInfoRsp qyweixinUserDetailInfoRsp = new Gson().fromJson(httpRsp, QyweixinUserDetailInfoRsp.class);
        if(qyweixinUserDetailInfoRsp.getErrcode() == 0) {
            if(ObjectUtils.isNotEmpty(qyweixinUserDetailInfoRsp) && CollectionUtils.isNotEmpty(qyweixinUserDetailInfoRsp.getDepartment()) && StringUtils.isNotEmpty(qyweixinUserDetailInfoRsp.getMain_department())) {
                List<String> departments = this.swapDepartment(qyweixinUserDetailInfoRsp.getDepartment(), qyweixinUserDetailInfoRsp.getMain_department());
                qyweixinUserDetailInfoRsp.setDepartment(departments);
            }
            return Result.newSuccess(qyweixinUserDetailInfoRsp);
        }
        return Result.newError(String.valueOf(qyweixinUserDetailInfoRsp.getErrcode()), qyweixinUserDetailInfoRsp.getErrmsg());
    }

    public Result<UploadResult> uploadFile(String corpId,File file, String type) {
        Result<String> accessTokenResult = getCorpAccessToken(ConfigCenter.crmAppId,corpId,false);
        if(!accessTokenResult.isSuccess() || StringUtils.isEmpty(accessTokenResult.getData())) {
            return Result.newError(accessTokenResult.getCode(), accessTokenResult.getMsg());
        }
        String url = "https://qyapi.weixin.qq.com/cgi-bin/media/upload?access_token=" + accessTokenResult.getData() + "&type="+type;
        String result = httpHelper.httpClientUploadFile(url, file);
        UploadResult uploadResult = new Gson().fromJson(result, UploadResult.class);
        if(uploadResult.getErrcode() == 0) {
            return Result.newSuccess(uploadResult);
        }
        return Result.newError(String.valueOf(uploadResult.getErrcode()), uploadResult.getErrmsg());
    }

    public Result<UploadResult> uploadFile(String corpId, String fileName, FileUploadArg.FileTypeEnum type, byte[] data) {
        log.info("QYWeixinManager.uploadFile,corpId={},fileName={},type={}",corpId,fileName,type);
        Long bandwidth = 1 * 1024 * 1024L;
        if(StringUtils.isNotEmpty(bandwidthConfig)) {
            Map<String,Integer> bandwidthConfigMap = JSONObject.parseObject(bandwidthConfig,Map.class);
            if(bandwidthConfigMap.get(corpId)!=null) {
                bandwidth = (long) bandwidthConfigMap.get(corpId);
            }
        }
        log.info("QYWeixinManager.uploadFile,bandwidth={}",bandwidth);
        Result<String> accessTokenResult = getCorpAccessToken(ConfigCenter.crmAppId,corpId,false);
        if(!accessTokenResult.isSuccess() || StringUtils.isEmpty(accessTokenResult.getData())) {
            return Result.newError(accessTokenResult.getCode(), accessTokenResult.getMsg());
        }
        log.info("QYWeixinManager.uploadFile,accessToken={}",accessTokenResult);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/media/upload?access_token=" + accessTokenResult.getData() + "&type="+type.name();
        ByteArrayInputStream bis = new ByteArrayInputStream(data);
        FsInputStreamBody body = new FsInputStreamBody(bis,fileName,bandwidth);
        String result = httpHelper.httpClientUploadFile(url, body);
        try {
            bis.close();
        } catch (Exception e) {

        }
        UploadResult uploadResult = new Gson().fromJson(result, UploadResult.class);
        if(uploadResult.getErrcode() == 0) {
            return Result.newSuccess(uploadResult);
        }
        return Result.newError(String.valueOf(uploadResult.getErrcode()), uploadResult.getErrmsg());
    }

    /**
     * 主属部门取得是企微部门列表的第一个，现在把主属部门放在部门列表第一位，减少开发的工作量
     * @param departments
     * @param mainDepartment
     * @return
     */
    private List<String> swapDepartment(List<String> departments, String mainDepartment) {
        if(departments.get(0).equals(mainDepartment)) {
            return departments;
        }
        for (int i = 0; i < departments.size(); i++) {
            if(departments.get(i).equals(mainDepartment)) {
                Collections.swap(departments, 0, i);
                break;
            }
        }
        return departments;
    }

    public Result<CheckSingleAgreeResult> checkSingleAgree(String token, QyweixinCheckSingleAgreeArg arg) {
        String url = "https://qyapi.weixin.qq.com/cgi-bin/msgaudit/check_single_agree?access_token=" + token;
        CheckSingleAgreeResult checkSingleAgreeResult = null;
        Map<String,Object> body = new HashMap<>();
        body.put("info", arg.getInfo());
        String result = proxyOkHttpClient.postUrl(url, body, new HashMap<>());
        log.info("QYWeixinManager.checkSingleAgree,result={}", result);
        checkSingleAgreeResult = JSONObject.parseObject(result, CheckSingleAgreeResult.class);
        if(checkSingleAgreeResult.getErrCode() == 0) {
            return Result.newSuccess(checkSingleAgreeResult);
        }
        return Result.newError(String.valueOf(checkSingleAgreeResult.getErrCode()), checkSingleAgreeResult.getErrMsg());
    }

    /**
     * 获取登录用户身份，仅用于登录授权应用
     * @param code
     * @return
     */
    public Result<QyweixinUserInfo3rdRsp> getUserInfoByLoginAuthApp(String code, String appId) {
        String suiteAccessToken = getSuiteAccessTokenFromRedis(ConfigCenter.loginAuthSuiteId);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/auth/getuserinfo3rd?suite_access_token=" + suiteAccessToken + "&code=" + code;
        String httpRsp = proxyOkHttpClient.getUrl(url, new HashMap<>());
        QyweixinUserInfo3rdRsp qyweixinUserInfo3rdRsp = new Gson().fromJson(httpRsp, QyweixinUserInfo3rdRsp.class);
        log.info("getUserInfo3rd,result={}", qyweixinUserInfo3rdRsp);
        OuterOaAppInfoEntity oaAppInfoEntity = null;
        if(qyweixinUserInfo3rdRsp.isSuccess()) {
            if (appId.equals("wx867ad65506013329")) {
                List<OuterOaAppInfoEntity> appInfoEntities = outerOaAppInfoManager.getEntities(OuterOaAppInfoParams.builder().channel(ChannelEnum.qywx).outEa(qyweixinUserInfo3rdRsp.getCorpid()).status(OuterOaAppInfoStatusEnum.normal).build());
                if (CollectionUtils.isNotEmpty(appInfoEntities)) {
                    oaAppInfoEntity = appInfoEntities.get(0);
                }
            } else {
                oaAppInfoEntity = outerOaAppInfoManager.getEntity(ChannelEnum.qywx, qyweixinUserInfo3rdRsp.getCorpid(), appId);
            }
            log.info("getUserInfo3rd,queryEnterpriseNameByOutEa,oaAppInfoEntity={}", oaAppInfoEntity);
            //Result<QyweixinGetAuthInfoRsp> corpInfo = getCorpInfo(qyweixinUserInfo3rdRsp.getCorpid(), crmAppId);
            if(ObjectUtils.isNotEmpty(oaAppInfoEntity)) {
                QyweixinAppInfoParams qyweixinAppInfoParams = JSON.parseObject(oaAppInfoEntity.getAppInfo(), QyweixinAppInfoParams.class);
                qyweixinUserInfo3rdRsp.setCorpName(qyweixinAppInfoParams.getAuthCorpInfo().getCorpName());
            }
            return Result.newSuccess(qyweixinUserInfo3rdRsp);
        } else {
            log.error("getUserInfo3rd,errMsg:{}", qyweixinUserInfo3rdRsp.getErrmsg() + "_" + qyweixinUserInfo3rdRsp.getErrcode());
            return Result.newError(String.valueOf(qyweixinUserInfo3rdRsp.getErrcode()), qyweixinUserInfo3rdRsp.getErrmsg());
        }
    }

    /**
     * 获取应用共享信息
     * 局校互联中的局端或者上下游中的上游企业通过该接口可以获取某个应用分享给的所有企业列表。
     * 特别注意，对于有敏感权限的应用，需要下级/下游企业确认后才能共享成功，若下级/下游企业未确认，则不会存在于该接口的返回列表
     * @param corpId
     * @param appId
     * @param agentId
     * @param downStreamCorpId
     * @return
     */
    public Result<List<ListAppShareInfoRsp.CorpInfo>> listAppShareInfo(String corpId,String appId,String agentId,String downStreamCorpId) {
        Result<String> corpAccessTokenResult = getCorpAccessToken(appId, corpId, false);
        if(!corpAccessTokenResult.isSuccess() || StringUtils.isEmpty(corpAccessTokenResult.getData())) {
            return Result.newError(corpAccessTokenResult.getCode(), corpAccessTokenResult.getMsg());
        }
        String url = "https://qyapi.weixin.qq.com/cgi-bin/corpgroup/corp/list_app_share_info?access_token=" + corpAccessTokenResult.getData();
        Map<String, Object> argMaps = Maps.newHashMap();
        argMaps.put("agentid", agentId);
        argMaps.put("business_type", "1");
        argMaps.put("cursor", null);
        argMaps.put("limit", 100);
        if(StringUtils.isNotBlank(downStreamCorpId)){
            argMaps.put("corpid",downStreamCorpId);
        }
        List<ListAppShareInfoRsp.CorpInfo> pagedList = Lists.newArrayList();
        log.info("QYWeixinManager.listAppShareInfo,argMaps={}",argMaps);
        String result = proxyOkHttpClient.postUrl(url, argMaps, new HashMap<>());
        log.info("QYWeixinManager.listAppShareInfo,result={}",result);
        ListAppShareInfoRsp listAppShareInfoRsp = JSONObject.parseObject(result,ListAppShareInfoRsp.class);

        if(listAppShareInfoRsp.getErrcode()!=0) {
            return Result.newError(listAppShareInfoRsp.getErrcode()+"",listAppShareInfoRsp.getErrmsg());
        }
        pagedList.addAll(listAppShareInfoRsp.getCorp_list());
        Object nextCursor = listAppShareInfoRsp.getNext_cursor();
        if(nextCursor!=null) {
            String cursor = nextCursor.toString();
            while (StringUtils.isNotEmpty(cursor)) {
                //继续获取下一页
                argMaps.put("cursor", cursor);
                result = proxyOkHttpClient.postUrl(url, argMaps, new HashMap<>());
                log.info("QYWeixinManager.listAppShareInfo,result2={}",result);
                listAppShareInfoRsp = JSONObject.parseObject(result,ListAppShareInfoRsp.class);
                if (listAppShareInfoRsp.getErrcode()==0) {
                    //JSONArray jsonArray = JSONArray.parseArray(JSONPath.read(result, "$.external_contact_list").toString());
                    pagedList.addAll(listAppShareInfoRsp.getCorp_list());
                }
                nextCursor = listAppShareInfoRsp.getNext_cursor();
                if(nextCursor==null) break;
                cursor = nextCursor.toString();
            }
        }
        return Result.newSuccess(pagedList);
    }

    public Result<String> getCorpGroupAccessToken(String upstreamCorpId, String appId, String corpId, Integer agentId, Integer businessType) {
        Result<String> accessTokenResult = getCorpAccessToken(appId, upstreamCorpId, false);
        if(!accessTokenResult.isSuccess() || StringUtils.isEmpty(accessTokenResult.getData())) {
            return accessTokenResult;
        }

        String url = "https://qyapi.weixin.qq.com/cgi-bin/corpgroup/corp/gettoken?access_token=" + accessTokenResult.getData();

        Map<String, Object> form = Maps.newHashMap();
        form.put("corpid", corpId);
        form.put("business_type", businessType);
        form.put("agentid", agentId);
        log.info("trace getCorpGroupAccessToken set:{}", form);
        String httpRsp = proxyOkHttpClient.postUrl(url, form, new HashMap<>());
        QyweixinCorpGroupAccessTokenRsp qyweixinCorpGroupAccessTokenRsp = new Gson().fromJson(httpRsp, QyweixinCorpGroupAccessTokenRsp.class);
        log.info("trace getCorpGroupAccessToken httpRsp:{}", qyweixinCorpGroupAccessTokenRsp);

        if(QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(qyweixinCorpGroupAccessTokenRsp.getErrcode())) {
            return Result.newSuccess(qyweixinCorpGroupAccessTokenRsp.getAccess_token());
        } else {
            return Result.newError(String.valueOf(qyweixinCorpGroupAccessTokenRsp.getErrcode()), qyweixinCorpGroupAccessTokenRsp.getErrmsg());
        }
    }

    /**
     * 下单获取订单详情
     * @param orderId
     */
    public  QywxPermissionOrderDetail getPermitOrder(String orderId){
        //服务商开发、代开发都需要拿到服务商token
        Result<String> providerAccessToken = getProviderAccessToken(null);
        String urlToken="https://qyapi.weixin.qq.com/cgi-bin/license/get_order?provider_access_token="+providerAccessToken.getData();
        Map<String,String> bodyMap=Maps.newHashMap();
        Map<String,String> headerMap=Maps.newHashMap();
        bodyMap.put("order_id",orderId);
        QywxPermissionOrderDetail qywxPermissionOrderDetail = proxyOkHttpClient.postUrlByJson(urlToken, JSONObject.toJSONString(bodyMap), headerMap, new TypeReference<QywxPermissionOrderDetail>() {
        });
        return qywxPermissionOrderDetail;
    }

    /**
     * 服务商查询自己某段时间内的平台能力服务订单列表
     * @param
     */
    public  QywxPermissionOrderList getPayOrderList(Long startTime,Long endTime,String corpId){
        //服务商开发、代开发都需要拿到服务商token
        Result<String> providerAccessToken = getProviderAccessToken(null);
        String urlToken="https://qyapi.weixin.qq.com/cgi-bin/license/list_order?provider_access_token="+providerAccessToken.getData();
        Map<String,Object> bodyMap=Maps.newHashMap();
        Map<String,String> headerMap=Maps.newHashMap();
        bodyMap.put("start_time",startTime);
        bodyMap.put("end_time",endTime);
        bodyMap.put("corpid",corpId);
        bodyMap.put("limit",1000);
        QywxPermissionOrderList qywxPermissionOrderDetail = proxyOkHttpClient.postUrlByJson(urlToken, JSONObject.toJSONString(bodyMap), headerMap, new TypeReference<QywxPermissionOrderList>() {
        });
        return qywxPermissionOrderDetail;
    }

    /**
     * 获取订单许可账号列表
     */
    public  List<QywxPermissionOrderAccountDetail.OrderAccountList> getPermitOrderAccount(String orderId){
        //服务商开发、代开发都需要拿到服务商token
        Result<String> providerAccessToken = getProviderAccessToken(null);
        String urlToken="https://qyapi.weixin.qq.com/cgi-bin/license/list_order_account?provider_access_token="+providerAccessToken.getData();
        Map<String,Object> bodyMap=Maps.newHashMap();
        Map<String,String> headerMap=Maps.newHashMap();
        bodyMap.put("order_id",orderId);
        bodyMap.put("limit",1000);//最大值1000，默认值500
        bodyMap.put("cursor",null);//用于分页查询的游标
        List<QywxPermissionOrderAccountDetail.OrderAccountList> accountLists=Lists.newArrayList();
        boolean continueQuery=true;
        while (continueQuery){
            QywxPermissionOrderAccountDetail qywxPermissionOrderDetail = proxyOkHttpClient.postUrlByJson(urlToken, JSONObject.toJSONString(bodyMap), headerMap, new TypeReference<QywxPermissionOrderAccountDetail>() {
            });
            if(qywxPermissionOrderDetail.isSuccess()&&CollectionUtils.isNotEmpty(qywxPermissionOrderDetail.getAccount_list())){
                accountLists.addAll(qywxPermissionOrderDetail.getAccount_list());
            }
            if(ObjectUtils.isNotEmpty(qywxPermissionOrderDetail.getHas_more())&&qywxPermissionOrderDetail.getHas_more()==1){
                bodyMap.put("cursor",qywxPermissionOrderDetail.getNext_cursor());
            }else{
                continueQuery=false;
            }
        }
        return accountLists;
    }

    //https://qyapi.weixin.qq.com/cgi-bin/license/list_actived_account?provider_access_token=ACCESS_TOKEN
    //获取企业的账号列表
    public List<QywxPermissionOrderAccountDetail.OrderAccountList> getEnterpriseActiveAccount(String corpId){
        Result<String> providerAccessToken = getProviderAccessToken(null);
        String urlToken="https://qyapi.weixin.qq.com/cgi-bin/license/list_actived_account?provider_access_token="+providerAccessToken.getData();
        Map<String,Object> bodyMap=Maps.newHashMap();
        Map<String,String> headerMap=Maps.newHashMap();
        bodyMap.put("corpid",corpId);
        bodyMap.put("limit",1000);//最大值1000，默认值500
        bodyMap.put("cursor",null);//用于分页查询的游标
        boolean continueQuery=true;
        List<QywxPermissionOrderAccountDetail.OrderAccountList> accountLists=Lists.newArrayList();
        while (continueQuery){
            QywxPermissionOrderAccountDetail qywxPermissionOrderDetail = proxyOkHttpClient.postUrlByJson(urlToken, JSONObject.toJSONString(bodyMap), headerMap, new TypeReference<QywxPermissionOrderAccountDetail>() {
            });
            if(qywxPermissionOrderDetail.isSuccess()&&CollectionUtils.isNotEmpty(qywxPermissionOrderDetail.getAccount_list())){
                accountLists.addAll(qywxPermissionOrderDetail.getAccount_list());
            }
            if(qywxPermissionOrderDetail.getHas_more()==1){
                bodyMap.put("cursor",qywxPermissionOrderDetail.getNext_cursor());
            }else{
                continueQuery=false;
            }
        }
        return accountLists;
    }

    public QywxActiveCodeDetail getActiveInfoByUserId(String corpId,String userId){
        //服务商开发、代开发都需要拿到服务商token
        Result<String> providerAccessToken = getProviderAccessToken(null);
        String urlToken="https://qyapi.weixin.qq.com/cgi-bin/license/get_active_info_by_user?provider_access_token="+providerAccessToken.getData();
        Map<String,Object> bodyMap=Maps.newHashMap();
        Map<String,String> headerMap=Maps.newHashMap();
        bodyMap.put("corpid",corpId);
        bodyMap.put("userid",userId);
        QywxActiveCodeDetail qywxActiveCodeDetail = proxyOkHttpClient.postUrlByJson(urlToken, JSONObject.toJSONString(bodyMap), headerMap, new TypeReference<QywxActiveCodeDetail>() {
        });
        return qywxActiveCodeDetail;

    }

    //查询某个账号激活码的状态以及激活绑定情况。
    public  QywxActiveCodeDetail getActiveInfoByCode(String corpId,List<String> activeCodes){
        //服务商开发、代开发都需要拿到服务商token
        Result<String> providerAccessToken = getProviderAccessToken(null);
        String urlToken="https://qyapi.weixin.qq.com/cgi-bin/license/batch_get_active_info_by_code?provider_access_token="+providerAccessToken.getData();
        Map<String,Object> bodyMap=Maps.newHashMap();
        Map<String,String> headerMap=Maps.newHashMap();
        bodyMap.put("corpid",corpId);
        bodyMap.put("active_code_list",activeCodes);
        QywxActiveCodeDetail qywxActiveCodeDetail = proxyOkHttpClient.postUrlByJson(urlToken, JSONObject.toJSONString(bodyMap), headerMap, new TypeReference<QywxActiveCodeDetail>() {
        });
        return qywxActiveCodeDetail;
    }


    public Result<List<String>> getPermissionInfo(String corpId,String appId){
        Result<String> accessTokenResult = getCorpAccessToken(appId, corpId, false);
        if(!accessTokenResult.isSuccess() || StringUtils.isEmpty(accessTokenResult.getData())) {
            log.error("获取accessToken失败，corpId:{},appId:{}:message:{}", corpId, appId,accessTokenResult.getMsg());
            return Result.newError(accessTokenResult.getCode(), accessTokenResult.getMsg());
        }
        String getPermissionUrl="https://qyapi.weixin.qq.com/cgi-bin/agent/get_permissions?access_token="+accessTokenResult.getData();
        QywxPermissionAppResult qywxBaseResult = proxyOkHttpClient.postUrl(getPermissionUrl, Maps.newHashMap(), Maps.newHashMap(), new TypeReference<QywxPermissionAppResult>() {
        });
        return Result.newSuccess(qywxBaseResult.getApp_permissions());

    }

    //设置公钥&设置回调地址
    public Result<String> setCallback(String corpId,String appId) throws NoSuchAlgorithmException {
        Result<String> accessTokenResult = getCorpAccessToken(appId, corpId, false);
        if(!accessTokenResult.isSuccess() || StringUtils.isEmpty(accessTokenResult.getData())) {
            log.error("获取accessToken失败，corpId:{},appId:{}:message:{}", corpId, appId,accessTokenResult.getMsg());
            return Result.newError(accessTokenResult.getCode(), accessTokenResult.getMsg());
        }
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).outEa(corpId).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newSuccess();
        }

        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            String fsEa = enterpriseBindEntity.getFsEa();
            //用企业绑定的appId作为保存数据的appId
            List<OuterOaConfigInfoEntity> configInfoEntities = outerOaConfigInfoManager.getEntities(OuterOaConfigInfoParams.builder().fsEa(fsEa).outEa(corpId).appId(enterpriseBindEntity.getAppId()).type(OuterOaConfigInfoTypeEnum.CONVERSATION_ARCHIVE_CONFIG).build());
            Integer publicKeyVersion=1;
            if(CollectionUtils.isNotEmpty(configInfoEntities)) {
                ConversationArchiveInfo conversationArchiveInfo = JSON.parseObject(configInfoEntities.get(0).getConfigInfo(), ConversationArchiveInfo.class);
                publicKeyVersion = conversationArchiveInfo.findMaxVersionConfig().get().getVersion() + 1;
            }

            String setPublicKeyUrl="https://qyapi.weixin.qq.com/cgi-bin/callback/set_public_key?access_token="+accessTokenResult.getData();
            Map<String,Object> dataMap=Maps.newHashMap();
            dataMap.put("public_key_ver",publicKeyVersion);
            Map<String, String> keyValues = RSAUtil.initSuffix();
            dataMap.put("public_key",keyValues.get("publicKey"));
            QywxPermissionAppResult qywxPermissionAppResult = proxyOkHttpClient.postUrl(setPublicKeyUrl, dataMap, Maps.newHashMap(), new TypeReference<QywxPermissionAppResult>() {
            });
            log.info("setCallback values:{},setResult:{}",JSONObject.toJSONString(dataMap),JSONObject.toJSONString(qywxPermissionAppResult));
            if(!qywxPermissionAppResult.isSuccess()){
                continue;
            }
            String setCallbackUrl="https://qyapi.weixin.qq.com/cgi-bin/chatdata/set_receive_callback?access_token="+accessTokenResult.getData();
            Map<String,String> callBackValue=Maps.newHashMap();
            callBackValue.put("program_id",ConfigCenter.programId);
            QywxPermissionAppResult setCallBackResult = proxyOkHttpClient.postUrl(setCallbackUrl, callBackValue, Maps.newHashMap(), new TypeReference<QywxPermissionAppResult>() {
            });
            log.info("setCallback values:{},setResult:{}",JSONObject.toJSONString(callBackValue),JSONObject.toJSONString(setCallBackResult));
            if(!setCallBackResult.isSuccess()){
                continue;
            }
            //插入配置信息
            ConversationArchiveInfo.ConversationConfig conversationConfig = new ConversationArchiveInfo.ConversationConfig();
            conversationConfig.setVersion(publicKeyVersion);
            conversationConfig.setPublicKey(cleanSuffixPublicKey(keyValues.get("publicKey")));
            conversationConfig.setPrivateKey(cleanSuffixPrivateKey(keyValues.get("privateKey")));
            int count = 0;
            if (CollectionUtils.isNotEmpty(configInfoEntities)) {
                OuterOaConfigInfoEntity configInfoEntity = configInfoEntities.get(0);
                ConversationArchiveInfo conversationArchiveInfo = JSON.parseObject(configInfoEntities.get(0).getConfigInfo(), ConversationArchiveInfo.class);
                conversationArchiveInfo.getConversationConfigs().add(conversationConfig);
                configInfoEntity.setConfigInfo(JSON.toJSONString(conversationArchiveInfo));
                configInfoEntity.setUpdateTime(System.currentTimeMillis());
                count = outerOaConfigInfoManager.updateById(configInfoEntity);
            } else {
                OuterOaConfigInfoEntity configInfoEntity = new OuterOaConfigInfoEntity();
                configInfoEntity.setChannel(ChannelEnum.qywx);
                configInfoEntity.setDcId(enterpriseBindEntity.getId());
                configInfoEntity.setFsEa(fsEa);
                configInfoEntity.setOutEa(corpId);
                configInfoEntity.setAppId(enterpriseBindEntity.getAppId());
                ConversationArchiveInfo conversationArchiveInfo = new ConversationArchiveInfo();
                //目前只允许一个纷享一个企微，先保留，但是设置为false
                List<OuterOaConfigInfoEntity> allowConfigInfoEntities = outerOaConfigInfoManager.getEntities(OuterOaConfigInfoParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).outEa(corpId).type(OuterOaConfigInfoTypeEnum.CONVERSATION_ARCHIVE_CONFIG).build());
                if (CollectionUtils.isNotEmpty(allowConfigInfoEntities)) {
                    conversationArchiveInfo.setIsOpen(Boolean.FALSE);
                } else {
                    conversationArchiveInfo.setIsOpen(Boolean.TRUE);
                }
                MessageStorageArg messageStorageArg = new MessageStorageArg();
                messageStorageArg.setConversionObjType(1);
                messageStorageArg.setSalesRetentionType(1);
                conversationArchiveInfo.setStorageLocation(messageStorageArg);
                conversationArchiveInfo.setConversationConfigs(Lists.newArrayList(conversationConfig));
                configInfoEntity.setType(OuterOaConfigInfoTypeEnum.CONVERSATION_ARCHIVE_CONFIG);
                configInfoEntity.setConfigInfo(JSON.toJSONString(conversationArchiveInfo));
                configInfoEntity.setCreateTime(System.currentTimeMillis());
                configInfoEntity.setUpdateTime(System.currentTimeMillis());
                count = outerOaConfigInfoManager.insert(configInfoEntity);
            }
            log.info("setCallback insert count:{}",count);
        }

//        List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(corpId);
//        if(CollectionUtils.isEmpty(enterpriseMappingList)) {
//            return Result.newSuccess();
//        }
//        QyweixinAccountEnterpriseMapping enterpriseMapping = enterpriseMappingList.get(0);
//        String fsEa = enterpriseMapping.getFsEa();
//        com.facishare.open.qywx.save.result.Result<GenerateSettingVo> generateSettingVoResult = messageGeneratingService.queryByEaSettingByLastVersion(fsEa);
//
//        Integer publicKeyVersion=1;
//        if(generateSettingVoResult!=null&&ObjectUtils.isNotEmpty(generateSettingVoResult.getData())){
//            publicKeyVersion=generateSettingVoResult.getData().getVersion()+1;
//        }
//
//        String setPublicKeyUrl="https://qyapi.weixin.qq.com/cgi-bin/chatdata/set_public_key?access_token="+accessTokenResult.getData();
//        Map<String,Object> dataMap=Maps.newHashMap();
//        dataMap.put("public_key_ver",publicKeyVersion);
//        Map<String, String> keyValues = RSAUtil.initSuffix();
//        dataMap.put("public_key",keyValues.get("publicKey"));
//        QywxPermissionAppResult qywxPermissionAppResult = proxyOkHttpClient.postUrl(setPublicKeyUrl, dataMap, Maps.newHashMap(), new TypeReference<QywxPermissionAppResult>() {
//        });
//        log.info("setCallback values:{},setResult:{}",JSONObject.toJSONString(dataMap),JSONObject.toJSONString(qywxPermissionAppResult));
//        if(!qywxPermissionAppResult.isSuccess()){
//            return Result.newError(qywxPermissionAppResult.getErrcode().toString(),qywxPermissionAppResult.getErrmsg());
//        }
//        String setCallbackUrl="https://qyapi.weixin.qq.com/cgi-bin/callback/set_receive_callback?access_token="+accessTokenResult.getData();
//        Map<String,String> callBackValue=Maps.newHashMap();
//        callBackValue.put("program_id",ConfigCenter.programId);
//        QywxPermissionAppResult setCallBackResult = proxyOkHttpClient.postUrl(setCallbackUrl, callBackValue, Maps.newHashMap(), new TypeReference<QywxPermissionAppResult>() {
//        });
//        log.info("setCallback values:{},setResult:{}",JSONObject.toJSONString(callBackValue),JSONObject.toJSONString(setCallBackResult));
//        if(!setCallBackResult.isSuccess()){
//            return Result.newError(setCallBackResult.getErrcode().toString(),setCallBackResult.getErrmsg());
//        }
//        //存储到会话
//        GenerateSettingVo generateSettingVo=new GenerateSettingVo();
//        generateSettingVo.setEa(fsEa);
//        generateSettingVo.setVersion(publicKeyVersion);
//        generateSettingVo.setPublicKey(cleanSuffixPublicKey(keyValues.get("publicKey")));
//        generateSettingVo.setPrivateKey(cleanSuffixPrivateKey(keyValues.get("privateKey")));
//        generateSettingVo.setQywxCorpId(corpId);
//        generateSettingVo.setVersion(publicKeyVersion);
//        int tenantId = eieaConverter.enterpriseAccountToId(fsEa);
//        generateSettingVo.setFsTenantId(tenantId);
//        messageGeneratingService.saveSetting(generateSettingVo);
        return Result.newSuccess();
    }

    private String cleanSuffixPublicKey(String publicKey){
        return publicKey.replace("-----BEGIN PUBLIC KEY-----","").replace("-----END PUBLIC KEY-----","").replace("\n","");

    }
    private String cleanSuffixPrivateKey(String privateKey){
        return privateKey.replace("-----BEGIN PRIVATE KEY-----","").replace("-----END PRIVATE KEY-----","").replace("\n","");

    }

    public Result<QyWeixinSendMsgRsp> sendMsg(QyweixinSendMessageModel sendMessageModel,
                                               String outEa,
                                               String appId) {
        Result<String> corpAccessTokenResult = getCorpAccessToken(appId, outEa, false);
        if(!corpAccessTokenResult.isSuccess() || StringUtils.isEmpty(corpAccessTokenResult.getData())) {
            return Result.newError(corpAccessTokenResult.getCode(), corpAccessTokenResult.getMsg());
        }
        String url ="https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token="+corpAccessTokenResult.getData();

        String replaceMsg = replaceMsg(JSONObject.toJSONString(sendMessageModel));
        try{
            log.info("trace sendMsg set replaceMsg={}", replaceMsg);
            QyWeixinSendMsgRsp qyWeixinSendMsgRsp = proxyOkHttpClient.postUrl(url, replaceMsg, Maps.newHashMap(), new TypeReference<QyWeixinSendMsgRsp>() {
            });
            log.info("trace sendMsg qyWeixinSendMsgRsp:{}", qyWeixinSendMsgRsp);
            if(qyWeixinSendMsgRsp.getErrcode() == 0) {
                return Result.newSuccess(qyWeixinSendMsgRsp);
            } else {
                return Result.newError(String.valueOf(qyWeixinSendMsgRsp.getErrcode()), qyWeixinSendMsgRsp.getErrmsg());
            }
        } catch (Exception e){
            log.error("tarce sendMsg get exception, error={}", e.getMessage());
            return Result.newError(ResultCodeEnum.SYS_EXCEPTION, e.getMessage());
        }
    }

    private String replaceMsg(String msg) {
        String userNamePrefix = "U-FSQYWX-";
        Pattern pattern = Pattern.compile(userNamePrefix);
        Matcher matcher = pattern.matcher(msg);
        Set<String> userAccountSet = new HashSet<>();
        Set<String> deptAccountSet = new HashSet<>();
        while (matcher.find()){
            //字串的索引
            String account = msg.substring(matcher.start(), matcher.end() + 32);
            userAccountSet.add(account);
        }
        String deptNamePrefix = "D-FSQYWX-";
        Pattern pattern1 = Pattern.compile(deptNamePrefix);
        Matcher matcher1 = pattern1.matcher(msg);
        while (matcher1.find()){
            System.out.println(matcher1.start());
            //字串的索引
            int sum = 0;
            int index = matcher1.end();
            char c = msg.charAt(index);
            while (org.apache.commons.lang3.StringUtils.isNumeric(String.valueOf(c))){
                sum ++;
                c = msg.charAt(index ++);
            }
            String account = msg.substring(matcher1.start(), matcher1.end() + sum - 1);
            deptAccountSet.add(account);
        }
        if(CollectionUtils.isNotEmpty(userAccountSet)) {
            for(String account : userAccountSet) {
                msg = msg.replace(account, "$userName="+ account.replace(userNamePrefix, "") +"$");
            }
        }
        if(CollectionUtils.isNotEmpty(deptAccountSet)) {
            for(String account : deptAccountSet) {
                msg = msg.replace(account, "$departmentName="+ account.replace(deptNamePrefix, "") +"$");
            }
        }
        return msg;
    }

    /**
     * 指定激活码用户
     * @param corpId
     * @param activeCode
     * @param userId
     * @return
     */
    public QywxBaseResult triggerActiveCodeTOUserId(String corpId,String activeCode,String userId){
        //服务商开发、代开发都需要拿到服务商token
        Result<String> providerAccessToken = getProviderAccessToken(null);
        String urlToken="https://qyapi.weixin.qq.com/cgi-bin/license/active_account?provider_access_token="+providerAccessToken.getData();
        Map<String,Object> bodyMap=Maps.newHashMap();
        Map<String,String> headerMap=Maps.newHashMap();
        bodyMap.put("corpid",corpId);
        bodyMap.put("active_code",activeCode);
        bodyMap.put("userid",userId);
        QywxBaseResult qywxBaseResult = proxyOkHttpClient.postUrlByJson(urlToken, JSONObject.toJSONString(bodyMap), headerMap, new TypeReference<QywxBaseResult>() {
        });
        return qywxBaseResult;
    }

    /**
     * 账号继承
     * @param corpId
     * @param transferList
     *
     * @return
     */
    public QywxTransferResult transferUserIds(String corpId, List<QywxActviceCodeArg.QywxTransferUserItem> transferList){
        //服务商开发、代开发都需要拿到服务商token
        Result<String> providerAccessToken = getProviderAccessToken(null);
        String urlToken="https://qyapi.weixin.qq.com/cgi-bin/license/batch_transfer_license?provider_access_token="+providerAccessToken.getData();
        Map<String,Object> bodyMap=Maps.newHashMap();
        Map<String,String> headerMap=Maps.newHashMap();
        bodyMap.put("corpid",corpId);
        bodyMap.put("transfer_list", GsonUtil.toJson(transferList));
        QywxTransferResult qywxBaseResult = proxyOkHttpClient.postUrlByJson(urlToken, JSONObject.toJSONString(bodyMap), headerMap, new TypeReference<QywxTransferResult>() {
        });
        return qywxBaseResult;
    }

    //刷库用
    public Result<QyweixinGetAuthInfoRsp> getCorpInfo2(String corpId, String appId, String permanentCode) {
        String suiteAccessToken = getSuiteAccessTokenFromRedis(appId);
        String url = "https://qyapi.weixin.qq.com/cgi-bin/service/get_auth_info?suite_access_token=" + suiteAccessToken;
        Map<String, String> form = Maps.newHashMap();
        form.put("auth_corpid", corpId);
        form.put("permanent_code", permanentCode);
        log.info("trace getCorpInfo get:{}", form);
        String httpRsp = proxyOkHttpClient.postUrl(url, form, new HashMap<>());
        QyweixinGetAuthInfoRsp qyweixinAuthCorpInfo = new Gson().fromJson(httpRsp, QyweixinGetAuthInfoRsp.class);
        if(qyweixinAuthCorpInfo.isSuccess()) {
            log.info("trace getCorpInfo rsp:{}", qyweixinAuthCorpInfo);
            qyweixinAuthCorpInfo.getAuth_corp_info().setCorpid(corpId2OpenCorpId(qyweixinAuthCorpInfo.getAuth_corp_info().getCorpid()).getData());
            return Result.newSuccess(qyweixinAuthCorpInfo);
        } else {
            return Result.newError(String.valueOf(qyweixinAuthCorpInfo.getErrcode()), qyweixinAuthCorpInfo.getErrmsg());
        }
    }

    private String getFinalAppId(String corpId, String appId) {
        //查看是否有repAppId
        String finalAppId = appId;
        if (finalAppId.equals(ConfigCenter.crmAppId)) {
            OuterOaAppInfoEntity repAppInfoEntity = outerOaAppInfoManager.getEntity(ChannelEnum.qywx, corpId, ConfigCenter.repAppId);
            if (ObjectUtils.isNotEmpty(repAppInfoEntity) && repAppInfoEntity.getStatus() == OuterOaAppInfoStatusEnum.normal) {
                log.info("QYWeixinManager.getFinalAppId,repAppInfoEntity={}", repAppInfoEntity);
                finalAppId = ConfigCenter.repAppId;
            }
        }
        return finalAppId;
    }

    public com.facishare.open.qywx.accountsync.result.Result<RuleId> createRule(String appId,String corpId, RuleModel ruleModel) {
        com.facishare.open.qywx.accountinner.result.Result<String> corpAccessTokenResult = this.getCorpAccessToken(appId, corpId, false);
        if(!corpAccessTokenResult.isSuccess()){
            return new com.facishare.open.qywx.accountsync.result.Result<>(corpAccessTokenResult.getCode(),corpAccessTokenResult.getMsg());
        }
        String callUrl=String.format(Constant.QYWX_CALL_DATA_URL,corpAccessTokenResult.getData());
        QywxMessageDataResult callData = getCallData(callUrl, AbilityIdEnum.invoke_create_rule.name(), ConfigCenter.programId, null, JSONObject.toJSONString(ruleModel));
        if(!callData.isSuccess()||StringUtils.isBlank(callData.getResponseData())){
            return new com.facishare.open.qywx.accountsync.result.Result<>(callData.getErrcode().toString(),callData.getErrmsg());
        }
        return new com.facishare.open.qywx.accountsync.result.Result<>(GsonUtil.fromJson(callData.getResponseData(),RuleId.class));
    }
    public com.facishare.open.qywx.accountsync.result.Result<RuleListResult> getRuleList(String appId,String corpId, GetRuleListArg getRuleListArg) {
        com.facishare.open.qywx.accountinner.result.Result<String> corpAccessTokenResult = this.getCorpAccessToken(appId, corpId, false);
        if(!corpAccessTokenResult.isSuccess()){
            return new com.facishare.open.qywx.accountsync.result.Result<>(corpAccessTokenResult.getCode(),corpAccessTokenResult.getMsg());
        }
        String callUrl=String.format(Constant.QYWX_CALL_DATA_URL,corpAccessTokenResult.getData());
        QywxMessageDataResult callData = getCallData(callUrl, AbilityIdEnum.invoke_get_rule_list.name(), ConfigCenter.programId, null, JSONObject.toJSONString(getRuleListArg));
        if(!callData.isSuccess()||StringUtils.isBlank(callData.getResponseData())){
            return new com.facishare.open.qywx.accountsync.result.Result<>(callData.getErrcode().toString(),callData.getErrmsg());
        }
        return new com.facishare.open.qywx.accountsync.result.Result<>(GsonUtil.fromJson(callData.getResponseData(),RuleListResult.class));
    }
    public com.facishare.open.qywx.accountsync.result.Result<RuleModel> getRuleDetail(String appId,String corpId, RuleId ruleId) {
        com.facishare.open.qywx.accountinner.result.Result<String> corpAccessTokenResult = this.getCorpAccessToken(appId, corpId, false);
        if(!corpAccessTokenResult.isSuccess()){
            return new com.facishare.open.qywx.accountsync.result.Result<>(corpAccessTokenResult.getCode(),corpAccessTokenResult.getMsg());
        }
        String callUrl=String.format(Constant.QYWX_CALL_DATA_URL,corpAccessTokenResult.getData());
        QywxMessageDataResult callData = getCallData(callUrl, AbilityIdEnum.invoke_get_rule_detail.name(), ConfigCenter.programId, null, JSONObject.toJSONString(ruleId));
        if(!callData.isSuccess()||StringUtils.isBlank(callData.getResponseData())){
            return new com.facishare.open.qywx.accountsync.result.Result<>(callData.getErrcode().toString(),callData.getErrmsg());
        }
        return new com.facishare.open.qywx.accountsync.result.Result<>(GsonUtil.fromJson(callData.getResponseData(),RuleModel.class));
    }
    public com.facishare.open.qywx.accountsync.result.Result<Void> updateRule(String appId,String corpId, RuleModel ruleModel) {
        com.facishare.open.qywx.accountinner.result.Result<String> corpAccessTokenResult = this.getCorpAccessToken(appId, corpId, false);
        if(!corpAccessTokenResult.isSuccess()){
            return new com.facishare.open.qywx.accountsync.result.Result<>(corpAccessTokenResult.getCode(),corpAccessTokenResult.getMsg());
        }
        String callUrl=String.format(Constant.QYWX_CALL_DATA_URL,corpAccessTokenResult.getData());
        QywxMessageDataResult callData = getCallData(callUrl, AbilityIdEnum.invoke_update_rule.name(), ConfigCenter.programId, null, JSONObject.toJSONString(ruleModel));
        if(!callData.isSuccess()||StringUtils.isBlank(callData.getResponseData())){
            return new com.facishare.open.qywx.accountsync.result.Result<>(callData.getErrcode().toString(),callData.getErrmsg());
        }
        return new com.facishare.open.qywx.accountsync.result.Result<>();
    }
    public com.facishare.open.qywx.accountsync.result.Result<Void> deleteRule(String appId,String corpId, RuleId ruleId) {
        com.facishare.open.qywx.accountinner.result.Result<String> corpAccessTokenResult = this.getCorpAccessToken(appId, corpId, false);
        if(!corpAccessTokenResult.isSuccess()){
            return new com.facishare.open.qywx.accountsync.result.Result<>(corpAccessTokenResult.getCode(),corpAccessTokenResult.getMsg());
        }
        String callUrl=String.format(Constant.QYWX_CALL_DATA_URL,corpAccessTokenResult.getData());
        QywxMessageDataResult callData = getCallData(callUrl, AbilityIdEnum.invoke_delete_rule.name(), ConfigCenter.programId, null, JSONObject.toJSONString(ruleId));
        if(!callData.isSuccess()||StringUtils.isBlank(callData.getResponseData())){
            return new com.facishare.open.qywx.accountsync.result.Result<>(callData.getErrcode().toString(),callData.getErrmsg());
        }
        return new com.facishare.open.qywx.accountsync.result.Result<>();
    }
    public com.facishare.open.qywx.accountsync.result.Result<MsgListResult> getHitMsgList(String appId, String corpId, GetHitMsgListArg arg) {

        com.facishare.open.qywx.accountinner.result.Result<String> corpAccessTokenResult = this.getCorpAccessToken(appId, corpId, false);
        if(!corpAccessTokenResult.isSuccess()){
            return new com.facishare.open.qywx.accountsync.result.Result<>(corpAccessTokenResult.getCode(),corpAccessTokenResult.getMsg());
        }
        String callUrl=String.format(Constant.QYWX_CALL_DATA_URL,corpAccessTokenResult.getData());
        QywxMessageDataResult callData = getCallData(callUrl, AbilityIdEnum.invoke_get_hit_msg_list.name(), ConfigCenter.programId, null, JSONObject.toJSONString(arg));
        if(!callData.isSuccess()||StringUtils.isBlank(callData.getResponseData())){
            return new com.facishare.open.qywx.accountsync.result.Result<>(callData.getErrcode().toString(),callData.getErrmsg());
        }
        return new com.facishare.open.qywx.accountsync.result.Result<>(GsonUtil.fromJson(callData.getResponseData(),MsgListResult.class));
    }
    public QywxMessageDataResult getCallData(String callUrl,String abilityId,String programId,String notifyId,String requestData ){
        Map<String,String> dataMap=Maps.newHashMap();
        dataMap.put("ability_id",abilityId);
        dataMap.put("program_id",programId);
        dataMap.put("notify_id",notifyId);
        dataMap.put("request_data",requestData);
        log.info("getcall data url:{}：ability_id:{},program_id：{},notify_id：{},request_data:{}",callUrl,abilityId,programId,notifyId,requestData);
        QywxMessageDataResult qywxMessageDataResult = proxyOkHttpClient.postUrl(callUrl,dataMap, Maps.newHashMap() ,new TypeReference<QywxMessageDataResult>(){});
        if(qywxMessageDataResult.isSuccess()&&StringUtils.isNotBlank(qywxMessageDataResult.getResponseData())){//把responseData的errorCode提取出来
            QywxResult qywxResult=GsonUtil.fromJson(qywxMessageDataResult.getResponseData(), QywxResult.class);
            qywxMessageDataResult.setErrcode(qywxResult.getErrcode());
            qywxMessageDataResult.setErrmsg(qywxResult.getErrmsg());
        }
        return qywxMessageDataResult;
    }
}