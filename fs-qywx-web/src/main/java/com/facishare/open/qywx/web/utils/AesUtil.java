package com.facishare.open.qywx.web.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import java.security.SecureRandom;

/**
 * Created by fengyh on 2018/3/22.
 */
@Slf4j
public class AesUtil {

    private static final Logger logger = LoggerFactory.getLogger(AesUtil.class);

    public static String doAesEncrypt(String content, String encryptKey) throws Exception {
        Cipher cipher = Cipher.getInstance("AES");
        KeyGenerator kgen = KeyGenerator.getInstance("AES");
        SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
        secureRandom.setSeed(encryptKey.getBytes());
        kgen.init(128, secureRandom);
        SecretKey secretKey = kgen.generateKey();
        System.out.println("密钥的长度为：" + secretKey.getEncoded().length);

        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        byte[] encrypt = cipher.doFinal(content.getBytes());
        String base64Str = new String(Base64.encodeBase64(encrypt));
        System.out.println("base64 string ：" + base64Str);
        return base64Str;
    }

    public static String doAesDecrypt(String content, String encryptKey) throws Exception {
        Cipher cipher = Cipher.getInstance("AES");
        KeyGenerator kgen = KeyGenerator.getInstance("AES");
        SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
        secureRandom.setSeed(encryptKey.getBytes());
        kgen.init(128, secureRandom);
        SecretKey secretKey = kgen.generateKey();
        System.out.println("密钥的长度为：" + secretKey.getEncoded().length);

        cipher.init(Cipher.DECRYPT_MODE, secretKey);//使用解密模式初始化 密钥
        byte[] decrypt = cipher.doFinal(Base64.decodeBase64(content));
        return new String(decrypt);
    }

    public static void main(String[] args) {
        String ticketMoble= "userId=%s&timestamp=%s&appId=%s&corpId=%s";
        String userId = RandomUtils.getRandomStr() + "liuwei";
        long timestamp = System.currentTimeMillis();
        String ticket = String.format(ticketMoble, userId, timestamp, "suiteId", "corpId");
        String ticketAesKey = "fa15b85858cf4bec";
        try {
            System.out.println(ticket);
            System.out.println(AesUtil.doAesEncrypt(ticket, ticketAesKey));
            System.out.println(MD5Helper.getMD5HexString(ticket));
            System.out.println(SHA1.genWithAmple(ticket));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("trace createTicket exception ,", e);
        }
    }
}

