package com.facishare.open.aliyun.market.service;

import com.facishare.open.aliyun.market.BaseTest;
import com.facishare.open.feishu.syncapi.model.login.FsUserModel;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.AliyunLoginService;
import org.junit.Test;

import javax.annotation.Resource;

public class AliyunLoginServiceTest extends BaseTest {
    @Resource
    private AliyunLoginService aliyunLoginService;

    @Test
    public void test() {
        Result<FsUserModel> result = aliyunLoginService.getFsUser("cc8fe42f0659a0e527a0ba1492b89a00");
        System.out.println(result);
    }
}
