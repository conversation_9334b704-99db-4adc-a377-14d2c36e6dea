package com.facishare.open.aliyun.market.controller;

import com.facishare.open.feishu.syncapi.model.jsapi.JsApiSignatureModel;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.JsApiService;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 阿里云JSAPI控制器
 * <AUTHOR>
 * @date 20220913
 */
@Slf4j
@RestController
@RequestMapping(value = "/aliyun/market")
public class JsApiController {
    @Resource
    private JsApiService jsApiService;

    /**
     * 用ticket获取纷享用户信息
     * @param appId
     * @param fsEa
     * @param url
     * @return
     */
    @RequestMapping(value="/getJsApiSignature",method = RequestMethod.GET)
    @ResponseBody
    public Result<JsApiSignatureModel> getJsApiSignature(@RequestParam String appId,
                                                         @RequestParam String fsEa,
                                                         @RequestParam String url,
                                                         @RequestParam(required = false) String outEa) {
        LogUtils.info("JsApiController.getJsApiSignature,appId={},fsEa={},outEa={},url={}",appId,fsEa,outEa,url);
        Result<JsApiSignatureModel> result = jsApiService.getJsApiSignature2(appId, fsEa, outEa, url);
        LogUtils.info("JsApiController.getJsApiSignature,result={}",result);
        return result;
    }
}
