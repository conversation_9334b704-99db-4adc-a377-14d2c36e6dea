package com.facishare.open.feishu.syncapi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.facishare.open.feishu.syncapi.enums.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("tb_order_info")
public class OrderInfoEntity implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 渠道
     */
    private ChannelEnum channel;
    /**
     * 订单ID
     */
    private String orderId;
    /**
     * 源订单ID，当前为升级购买时(buy_type 为upgrade)，该字段表示原订单ID，升级后原订单失效，状态变为已升级(业务方需要处理)
     */
    private String srcOrderId;
    /**
     * 应用ID
     */
    private String appId;
    /**
     * 付款企业ID，对于飞书，此值等于displayId
     */
    private String paidCorpId;
    /**
     * 购买类型 buy普通购买 upgrade为升级购买 renew为续费购买
     */
    private BuyTypeEnum buyType;
    /**
     * 订单状态
     */
    private OrderStatusEnum orderStatus;
    /**
     * 用户购买方案类型
     * "trial" -试用；
     * "permanent"-免费；
     * "per_year"-企业年付费；
     * "per_month"-企业月付费；
     * "per_seat_per_year"-按人按年付费；
     * "per_seat_per_month"-按人按月付费；
     * "permanent_count"-按次付费
     */
    private PricePlanTypeEnum pricePlanType;
    /**
     * 购买版本ID
     */
    private String editionId;
    /**
     *  购买版本名字
     */
    private String editionName;
    /**
     * 购买的人数
     */
    private Integer userCount;
    /**
     *  购买的天数
     */
    private Integer orderDays;
    /**
     * 订单价格 单位分
     */
    private Integer price;
    /**
     * 订单支付价格 单位分
     */
    private Integer payPrice;
    /**
     *  下单时间
     */
    private Timestamp orderTime;
    /**
     * 付款时间
     */
    private Timestamp payTime;
    /**
     * 购买有效时间开始
     */
    private Timestamp beginTime;
    /**
     * 购买有效时间结束
     */
    private Timestamp endTime;
    /**
     *  订单来源
     */
    private OrderFromEnum orderFrom;
    /**
     * 下单企业ID，如果下单企业和付款企业不是同一家企业，paidCorpId和orderCordId不同
     * 对于飞书，此值等于displayId
     */
    private String orderCorpId;
}
