package com.facishare.open.huawei.kit.web.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.open.huawei.kit.web.manager.CorpInfoManager;
import com.facishare.open.huawei.kit.web.manager.EmployeeBindManager;
import com.facishare.open.huawei.kit.web.manager.EnterpriseBindManager;
import com.facishare.open.huawei.kit.web.manager.OrderInfoManager;
import com.facishare.open.huawei.kit.web.utils.HuaweiApiUtil;
import com.facishare.open.feishu.syncapi.arg.CreateCustomerAndUpdateMappingArg;
import com.facishare.open.feishu.syncapi.arg.CreateOrderArg;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.config.GlobalValue;
import com.facishare.open.feishu.syncapi.entity.CorpInfoEntity;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.feishu.syncapi.entity.OrderInfoEntity;
import com.facishare.open.feishu.syncapi.enums.*;
import com.facishare.open.huawei.kit.web.info.AppInfo;
import com.facishare.open.huawei.kit.web.info.HuaweiOrderInfo;
import com.facishare.open.huawei.kit.web.info.InstanceInfo;
import com.facishare.open.huawei.kit.web.info.ProductInfo;
import com.facishare.open.huawei.kit.web.model.HuaweiOrderDataModel;
import com.facishare.open.huawei.kit.web.model.SkuArg;
import com.facishare.open.feishu.syncapi.model.config.VersionModel;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.ResultCodeEnum;
import com.facishare.open.huawei.kit.web.result.OrderInfoResult;
import com.facishare.open.huawei.kit.web.result.QueryInstanceResult;
import com.facishare.open.huawei.kit.web.service.AppService;
import com.facishare.open.huawei.kit.web.service.HuaweiOrderService;
import com.facishare.open.order.contacts.proxy.api.arg.CreateCrmOrderArg;
import com.facishare.open.order.contacts.proxy.api.data.FsObjectCustomer;
import com.facishare.open.order.contacts.proxy.api.service.FsOrderServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.EnterpriseUtils;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.webhook.common.dao.paas.enums.CrmCustomerSource;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.message.extrnal.platform.model.arg.SendTextMessageArg;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static com.facishare.open.huawei.kit.web.consts.HuaweiProduceConstant.*;
import static com.facishare.open.feishu.syncapi.enums.PricePlanTypeEnum.*;

@Service("huaweiOrderService")
public class HuaweiOrderServiceImpl implements HuaweiOrderService {
    @Resource
    private EnterpriseBindManager enterpriseBindManager;
    @Resource
    private CorpInfoManager corpInfoManager;
    private List<SkuArg> skuList;

    private Map<String, String> productIdMap;
    @Resource
    private OrderInfoManager orderInfoManager;
    @Resource
    private EmployeeBindManager employeeBindManager;
    @Resource
    private FsOrderServiceProxy fsOrderServiceProxy;
    @Resource
    private AppService appService;

    @PostConstruct
    public void init() {
        skuList = JSON.parseObject(ConfigCenter.SKU_CODE, new TypeReference<List<SkuArg>>() {});
        LogUtils.info("HuaweiOrderServiceImpl init success. skuList:{}", skuList);
    }

    @Override
    public Result<List<EnterpriseBindEntity>> getEnterpriseBindList(String outEa) {
        List<EnterpriseBindEntity> enterpriseBindList = enterpriseBindManager.getEnterpriseBindList(outEa);
        LogUtils.info("HuaweiOrderServiceImpl.getEnterpriseBindList,enterpriseBindList={}", enterpriseBindList);
        return Result.newSuccess(enterpriseBindList);
    }

    @Override
    public Result<CorpInfoEntity> getCorpInfo(String instanceId) {
        CorpInfoEntity entityByDisplayId = corpInfoManager.getEntityByDisplayId(instanceId);
        LogUtils.info("HuaweiOrderServiceImpl.getCorpInfo,entityByDisplayId={}", entityByDisplayId);
        return Result.newSuccess(entityByDisplayId);
    }

    @Override
    public QueryInstanceResult queryInstance(String outEa) {
        List<InstanceInfo> instanceInfos = new LinkedList<>();
        List<String> instanceIds = Splitter.on(",").splitToList(outEa);

        for(String instanceId : instanceIds) {
            //实例不会创建纷享企业，只返回纷享官网
            InstanceInfo instanceInfo = new InstanceInfo();
            instanceInfo.setInstanceId(instanceId);
            AppInfo appInfo = new AppInfo();
            appInfo.setFrontEndUrl(ConfigCenter.crm_domain);
            instanceInfo.setAppInfo(appInfo);
            instanceInfos.add(instanceInfo);
        }
        return QueryInstanceResult.newSuccess(instanceInfos);
    }

    @Override
    public Result<HuaweiOrderInfo> saveOrder(HuaweiOrderDataModel huaweiOrderDataModel) {
        //查询订单
        // 调用华为云商店api，获取订单详情
        Map<String, String> header = new HashMap<String, String>();
        header.put("orderId", huaweiOrderDataModel.getOrderId());
        String result;
        try {
            result = HuaweiApiUtil.doGET("https://mkt.myhuaweicloud.com/api/mkp-openapi-public/global/v1/order" +
                    "/query", header);
        } catch (Exception e) {
            LogUtils.info("newInstance errored! ", e);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }
        OrderInfoResult apiResult = JSONObject.parseObject(result, new TypeReference<OrderInfoResult>() {
        });
        LogUtils.info("HuaweiOrderServiceImpl.newInstance apiResult={} ", apiResult);
        if (apiResult == null || ObjectUtils.isEmpty(apiResult.getOrderInfo()) || !"MKT.0000".equals(apiResult.getResultCode())) {
            LogUtils.info("newInstance errored! apiResult: {}", apiResult);
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }

        //华为比较特殊，没有提供试用订单，企业也没有办法重复创建，直接在订单改参数就好
        HashMap<String, HuaweiOrderInfo.BuyerInfo> tryOrderTenantIdMap = JSON.parseObject(ConfigCenter.try_order_tenant_id, new TypeToken<HashMap<String, HuaweiOrderInfo.BuyerInfo>>() {
        }.getType());
        if(tryOrderTenantIdMap.containsKey(huaweiOrderDataModel.getOrderId())) {
            apiResult.getOrderInfo().getBuyerInfo().setCustomerId(tryOrderTenantIdMap.get(huaweiOrderDataModel.getOrderId()).getCustomerId());
            apiResult.getOrderInfo().getBuyerInfo().setCustomerRealName(tryOrderTenantIdMap.get(huaweiOrderDataModel.getOrderId()).getCustomerRealName());

            if(StringUtils.isNotEmpty(tryOrderTenantIdMap.get(huaweiOrderDataModel.getOrderId()).getUserId())) {
                apiResult.getOrderInfo().getBuyerInfo().setUserId(tryOrderTenantIdMap.get(huaweiOrderDataModel.getOrderId()).getUserId());
            }

            if(StringUtils.isNotEmpty(tryOrderTenantIdMap.get(huaweiOrderDataModel.getOrderId()).getUserName())) {
                apiResult.getOrderInfo().getBuyerInfo().setUserName(tryOrderTenantIdMap.get(huaweiOrderDataModel.getOrderId()).getUserName());
            }

            LogUtils.info("HuaweiOrderServiceImpl.newInstance apiResult={} ", apiResult);
        }

        //保存订单
        HuaweiOrderInfo orderInfo = apiResult.getOrderInfo();
        HuaweiOrderInfo.BuyerInfo buyerInfo = null;
        HuaweiOrderInfo.OrderLine orderLine = null;
        ProductInfo productInfo = null;
        try {
            buyerInfo = orderInfo.getBuyerInfo();
            orderLine = orderInfo.getOrderLine().get(0);
            // 我们对接的场景，不存在一个订单行多个产品的情况
            productInfo = orderLine.getProductInfo().get(0);
        } catch (NullPointerException e) {
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR);
        }

        // 获取产品标识
        String skuCodeName = "";
        String productId = productInfo.getSkuCode();
        if (CollectionUtils.isNotEmpty(skuList) && StringUtils.isNotBlank(productId)) {
            String skuCode = orderInfo.getOrderLine().get(0).getProductInfo().get(0).getSkuCode();
            skuCodeName = skuList.stream()
                    .filter(v -> v.getCode().equals(skuCode))
                    .findFirst()
                    .map(SkuArg::getName)
                    .orElse("");
        }

        long orderStartTime = orderInfo.getCreateTime().getTime();
        long orderEndTime = orderLine.getExpireTime().getTime();
        long orderPayTime = Instant.now().toEpochMilli();
        int orderDays = 0;

        String orderType = orderInfo.getOrderType();
        BuyTypeEnum buyType = BuyTypeEnum.buy;
        if (RENEW.equals(orderType) || SUB.equals(orderType)) {
            buyType = BuyTypeEnum.renew;
        } else if (CHANGE.equals(orderType)) {
            buyType = BuyTypeEnum.upgrade;
        }

        PricePlanTypeEnum pricePlanType = permanent_count;
        if(orderType.equals("TRIAL")) {
            pricePlanType = trial;
            orderDays = 15;
        } else {
            if (StringUtils.isNotBlank(orderLine.getPeriodType())) {
                // TODO: 时长不是固定的，想要精准计算，需要调研一下云商店售卖情况
                int period;
                if ("month".equals(orderLine.getPeriodType())) {
                    period = 30;
                    pricePlanType = per_seat_per_month;
                } else {
                    period = 365;
                    pricePlanType = per_seat_per_year;
                }
                orderDays = period * orderLine.getPeriodNumber();
            }
        }

        Integer userCount = ObjectUtils.isEmpty(productInfo.getLinearValue()) ? 0 : productInfo.getLinearValue();
        // 默认为客户订单
        OrderFromEnum orderFrom = OrderFromEnum.customer;
        // 企业id用客户id表示
        huaweiOrderDataModel.setTenantId(buyerInfo.getCustomerId());
        huaweiOrderDataModel.setEnterpriseName(buyerInfo.getCustomerRealName());
        String corpId = huaweiOrderDataModel.getTenantId();

        OrderInfoEntity entity = OrderInfoEntity.builder()
                .appId(ConfigCenter.HUAWEI_APP_ID)
                .orderId(huaweiOrderDataModel.getOrderId())
                .channel(ChannelEnum.huawei)
                .buyType(buyType)
                .orderStatus(OrderStatusEnum.normal)
                .orderDays(orderDays)
                .orderCorpId(corpId)
                .paidCorpId(corpId)
                .price(convertToCents(orderLine.getCurrency()))
                .payPrice(convertToCents(orderLine.getCurrencyAfterDiscount()))
                .pricePlanType(pricePlanType)
                .editionId(productId)
                .editionName(skuCodeName)
                .userCount(userCount)
                .orderTime(new Timestamp(orderStartTime))
                .payTime(new Timestamp(orderPayTime))
                .beginTime(new Timestamp(orderStartTime))
                .endTime(new Timestamp(orderEndTime))
                .orderFrom(orderFrom)
                .build();

        LogUtils.info("HuaweiOrderServiceImpl.newInstance, entity={}", JSONObject.toJSONString(entity));
        int rows = orderInfoManager.insertOrUpdateOrderInfo(entity);
        LogUtils.info("HuaweiOrderServiceImpl.newInstance, result={}", rows);

        HuaweiOrderInfo finalOrderInfo = new HuaweiOrderInfo();
        BeanUtils.copyProperties(orderInfo, finalOrderInfo);

        return Result.newSuccess(finalOrderInfo);
    }

    @Override
    public Result<String> genFsEa(String enterpriseName) {
        String fsEa = EnterpriseUtils.genEA(enterpriseName,"feishu");
        LogUtils.info("HuaweiOrderServiceImpl.genFsEa,fsEa={}", fsEa);
        return new Result<>(fsEa);
    }

    @Override
    public Result<Void> createCustomerAndUpdateMapping(CreateCustomerAndUpdateMappingArg arg) {
        LogUtils.info("HuaweiOrderServiceImpl.createCustomerAndUpdateMapping,arg={}",arg);
        String fsEa = arg.getFsEa();

        //新购订单，开通全新的纷享企业

        FsObjectCustomer customerObject = new FsObjectCustomer();
        customerObject.setName(arg.getEnterpriseName());
        customerObject.setEnterpriseName(arg.getEnterpriseName());
        customerObject.setEnterpriseAccount(fsEa);
        customerObject.setManagerName(arg.getInstallerName());
        customerObject.setManagerMobile(arg.getInstallerMobilePhone());
        HashMap<String, String> crmProductIdMap = JSON.parseObject(ConfigCenter.crm_product_ids, new TypeToken<HashMap<String, String>>() {
        }.getType());
        customerObject.setSource(crmProductIdMap.get("HUAWEI"));
        customerObject.setOutEid(arg.getOutEid());
        customerObject.setOwner(Lists.newArrayList("-10000"));

        com.facishare.open.order.contacts.proxy.api.result.Result<String> result = fsOrderServiceProxy.createObjCustomer(customerObject);
        LogUtils.info("HuaweiOrderServiceImpl.createCustomerAndUpdateMapping,result={}",result);
        if(!result.isSuccess()) {
            if(!result.isSuccess()) {
                com.facishare.open.order.contacts.proxy.api.result.Result<List<ObjectData>> listResult = fsOrderServiceProxy.queryCustomer(ConfigCenter.MASTER_EA, arg.getOutEid());
                if (listResult.isSuccess() && CollectionUtils.isNotEmpty(listResult.getData())) {
                    fsEa = listResult.getData().get(0).get(ConfigCenter.CRM_CUSTOM_ENTERPRISE_ID_FILE).toString();
                    arg.setFsEa(fsEa);
                } else {
                    return Result.newError(result.getCode(), result.getMsg());
                }
            }
        }

        //保存企业绑定关系
        int insertEnt = enterpriseBindManager.insert(ChannelEnum.huawei,
                fsEa,
                arg.getOutEa(),
                ConfigCenter.crm_domain,
                BindTypeEnum.auto,
                BindStatusEnum.create);

        //保存管理员绑定关系
        int insertEmp = employeeBindManager.insert(ChannelEnum.huawei,
                fsEa,
                GlobalValue.FS_ADMIN_USER_ID + "",
                arg.getOutEa(),
                arg.getInstallerUserId(),
                BindTypeEnum.auto,
                BindStatusEnum.create);

        LogUtils.info("HuaweiOrderServiceImpl.createCustomerAndUpdateMapping,insertEnt={},insertEmp={}",insertEnt,insertEmp);

        return Result.newSuccess();
    }

    @Override
    public Result<Void> createOrder(CreateOrderArg arg) {
        LogUtils.info("HuaweiOrderServiceImpl.createOrder,arg={}", arg);
        OrderInfoEntity entity = orderInfoManager.getEntity(arg.getOrderId());
        LogUtils.info("HuaweiOrderServiceImpl.createOrder,entity={}", entity);
        CreateCrmOrderArg createCrmOrderArg = buildCreateCrmOrderArg(entity, arg.getFsEa());
        LogUtils.info("HuaweiOrderServiceImpl.createOrder,createCrmOrderArg={}", createCrmOrderArg);
        com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsOrderServiceProxy.createCrmOrder(createCrmOrderArg);
        LogUtils.info("HuaweiOrderServiceImpl.createOrder,result={}", result);
        return Result.newError(result.getCode(),result.getMsg());
    }

    /**
     * 将字符串的金额转换为以分为单位的int
     * @param amountStr
     * @return
     */
    private int convertToCents(String amountStr) {
        if (amountStr == null || amountStr.isEmpty()) {
            return 0;
        }
        boolean isNegative = false;
        if (amountStr.startsWith("-")) {
            isNegative = true;
            amountStr = amountStr.substring(1);
        }
        // 去除非数字字符和小数点以外的字符
        amountStr = amountStr.replaceAll("[^\\d.]", "");
        // 判断是否包含小数点
        int decimalIndex = amountStr.indexOf('.');
        if (decimalIndex == -1) {
            // 如果没有小数点，直接转换为整数并返回（单位为分）
            int result = Integer.parseInt(amountStr) * 100;
            return isNegative? -result : result;
        } else {
            // 如果有小数点，分别处理整数部分和小数部分
            String integerPart = amountStr.substring(0, decimalIndex);
            StringBuilder decimalPart = new StringBuilder(amountStr.substring(decimalIndex + 1));
            // 补全小数部分到两位
            while (decimalPart.length() < 2) {
                decimalPart.append("0");
            }
            int result = Integer.parseInt(integerPart) * 100 + Integer.parseInt(decimalPart.toString());
            return isNegative? -result : result;
        }
    }

    private CreateCrmOrderArg buildCreateCrmOrderArg(OrderInfoEntity entity, String fsEa) {
        int userCount = entity.getUserCount();
        LogUtils.info("HuaweiOrderServiceImpl.openEnterprise,userCount={}", userCount);
        if(entity.getBuyType()== BuyTypeEnum.upgrade) {
            //todo
        } else if(entity.getBuyType() == BuyTypeEnum.renew) {
            //续订人数不变，只增加年限
            userCount = 0;
        }
        LogUtils.info("HuaweiOrderServiceImpl.openEnterprise,userCount2={}", userCount);

        VersionModel versionModel = ConfigCenter.getFirstVersionProductId(entity.getAppId(), entity.getEditionId());
        CreateCrmOrderArg.CrmOrderDetailInfo orderDetailInfo = CreateCrmOrderArg.CrmOrderDetailInfo.builder()
                .enterpriseAccount(fsEa)
                .orderId(entity.getOrderId())
                .orderTime(entity.getPayTime().getTime())
                .orderTpye(entity.getPricePlanType() == PricePlanTypeEnum.trial ? CreateCrmOrderArg.CrmOrderDetailInfo.ORDER_TYPE_TRY : CreateCrmOrderArg.CrmOrderDetailInfo.ORDER_TYPE_BUY)
                .build();

        //订单金额单位是 分，需要转换成 元
        BigDecimal orderAmount = BigDecimal.valueOf(entity.getPayPrice() / 100.0);
        CreateCrmOrderArg.CrmOrderProductInfo orderProductInfo = CreateCrmOrderArg.CrmOrderProductInfo.builder()
                .beginTime(entity.getBeginTime().getTime())
                .endTime(entity.getEndTime().getTime())
                .quantity(userCount)
                .allResourceCount(userCount)
                .orderAmount(orderAmount + "")
                .productId(versionModel.getProductId())
                .build();

        CreateCrmOrderArg orderArg = CreateCrmOrderArg.builder()
                .crmOrderDetailInfo(orderDetailInfo)
                .crmOrderProductInfo(orderProductInfo)
                .build();

        return orderArg;
    }

    @Override
    public Result<Boolean> isEnterpriseBind(String ea) {
        EnterpriseBindEntity entity = enterpriseBindManager.getEntity(ChannelEnum.huawei,ea, BindStatusEnum.create);
        LogUtils.info("HuaweiOrderServiceImpl.isEnterpriseBind,entity={}", entity);
        return Result.newSuccess(entity!=null);
    }

    @Override
    public Result<EnterpriseBindEntity> getEnterpriseBind(String ea) {
        EnterpriseBindEntity entity = enterpriseBindManager.getEntity(ChannelEnum.huawei,ea, BindStatusEnum.normal);
        LogUtils.info("HuaweiOrderServiceImpl.isEnterpriseBind,entity={}", entity);
        return Result.newSuccess(entity);
    }

    @Override
    public Result<Void> updateEnterpriseAndAdminMapping(String ea, String adminUserId) {
        enterpriseBindManager.updateBindStatus(ea, null, BindStatusEnum.normal);
        employeeBindManager.batchUpdateBindStatus(ea,
                Lists.newArrayList(adminUserId),
                BindStatusEnum.normal,
                null);
        return Result.newSuccess();
    }

    @Override
    public Result<Void> saveOrderAndAddCrmOrder(HuaweiOrderDataModel huaweiOrderDataModel) {
        LogUtils.info("HuaweiOrderServiceImpl.createAndSaveOrder,huaweiOrderDataModel={}", huaweiOrderDataModel);
        Result<HuaweiOrderInfo> saveOrder = saveOrder(huaweiOrderDataModel);
        LogUtils.info("HuaweiOrderServiceImpl.createAndSaveOrder,saveOrder={}", saveOrder);
        if(saveOrder.isSuccess()) {
            List<EnterpriseBindEntity> enterpriseBindList = getEnterpriseBindList(huaweiOrderDataModel.getTenantId()).getData();
            LogUtils.info("HuaweiOrderServiceImpl.createAndSaveOrder,enterpriseBindList={}", enterpriseBindList);
            if(CollectionUtils.isNotEmpty(enterpriseBindList)) {
                if(enterpriseBindList.size()==1) {
                    OrderInfoEntity entity = orderInfoManager.getEntity(huaweiOrderDataModel.getOrderId());

                    //自动绑定的企业，重新下单逻辑
                    if(enterpriseBindList.get(0).getBindType()== BindTypeEnum.auto) {
                        String fsEa = enterpriseBindList.get(0).getFsEa();
                        LogUtils.info("HuaweiOrderServiceImpl.createAndSaveOrder,fsEa={}", fsEa);
                        CreateCrmOrderArg orderArg = buildCreateCrmOrderArg(entity,fsEa);
                        LogUtils.info("HuaweiOrderServiceImpl.createAndSaveOrder,orderArg={}", orderArg);
                        //升级订单或续订订单，更新纷享企业
                        com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsOrderServiceProxy.createCrmOrder(orderArg);
                        LogUtils.info("HuaweiOrderServiceImpl.createAndSaveOrder,createCrmOrder,result={}", result);
                        return Result.newInstance(result.getCode(),result.getMsg());
                    } else {
                        //手动绑定地企业，重新下单，前面保存完订单信息，到这里就结束了。
                        return Result.newSuccess();
                    }
                }
                //多对一场景，多个纷享企业对一个飞书企业
                if(enterpriseBindList.size()>1) {
                    //只有手动绑定场景，才支持多个纷享企业对一个飞书企业，重新下单，前面保存完订单信息，到这里就结束了。
                    return Result.newSuccess();
                }
            }
        }
        return Result.newSuccess();
    }
}
