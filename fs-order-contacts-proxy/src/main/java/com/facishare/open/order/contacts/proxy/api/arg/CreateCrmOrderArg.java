package com.facishare.open.order.contacts.proxy.api.arg;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CreateCrmOrderArg implements Serializable {

    private CrmOrderDetailInfo crmOrderDetailInfo;
    private CrmOrderProductInfo crmOrderProductInfo;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class CrmOrderDetailInfo implements Serializable{
        public static Integer ORDER_TYPE_BUY = 1;
        public static Integer ORDER_TYPE_TRY = 2;
        public static Integer ORDER_TYPE_GIFT = 3;

        private String orderId;
        private String enterpriseAccount;
        //订单类型：1-标准（购买），2-试用，3-赠送， 必填
        private Integer orderTpye;
        // 订单类型，新版用这个 CrmOrderTypeEnum
        private String crmOrderType;
        private Long orderTime;
//        private String orderStatus;
//        private String contractStamp;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class CrmOrderProductInfo implements Serializable{
        private String productId;
        private Long beginTime;
        private Long endTime;
        //套数
        private Integer quantity;
        private Integer allResourceCount;
        private String orderAmount;
    }
}
