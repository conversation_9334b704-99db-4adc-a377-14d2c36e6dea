package com.facishare.open.order.contacts.proxy.config;

import com.github.autoconf.ConfigFactory;

public class ConfigCenter {
    public static String RESET_URL = "http://172.31.100.247:15056/";
    public static String CRM_REST_OBJ_URL="http://172.31.100.247:17263/API/v1/rest/object";
    public static String CRM_RATE_LIMIT = "{\"defaultRateLimit\":20,\"84883\":2}";
    public static String CRM_ACCOUNT_TEMPLATE_ID = "650c125913968d3fd0a6b995";
    public static String DEAL_CRM_TODO_URL = "http://172.31.100.247:30204/fs-crm-workflow/approval/complete";
    public static Integer MASTER_EA = 76517;
    public static String crm_account_source_ids;
    public static String crm_order_product_id;
    public static String sales_order_describe_id;
    public static String price_book_id;
    public static String sales_order_product_obj;

    public static String crm_order_type_ids;

    static {
        ConfigFactory.getInstance().getConfig("fs-erp-order-contacts-proxy", config -> {
            RESET_URL = config.get("RESET_URL", RESET_URL);
            CRM_REST_OBJ_URL = config.get("CRM_REST_OBJ_URL", CRM_REST_OBJ_URL);
            CRM_RATE_LIMIT = config.get("CRM_RATE_LIMIT", CRM_RATE_LIMIT);
            CRM_ACCOUNT_TEMPLATE_ID = config.get("CRM_ACCOUNT_TEMPLATE_ID", CRM_ACCOUNT_TEMPLATE_ID);
            DEAL_CRM_TODO_URL = config.get("DEAL_CRM_TODO_URL", DEAL_CRM_TODO_URL);
            MASTER_EA = config.getInt("MASTER_EA", MASTER_EA);
            crm_account_source_ids = config.get("crm_account_source_ids", crm_account_source_ids);
            crm_order_product_id = config.get("crm_order_product_id", crm_order_product_id);
            sales_order_describe_id = config.get("sales_order_describe_id", sales_order_describe_id);
            price_book_id = config.get("price_book_id", price_book_id);
            sales_order_product_obj = config.get("sales_order_product_obj", sales_order_product_obj);
            crm_order_type_ids = config.get("crm_order_type_ids", crm_order_type_ids);
        });
    }
}