
package com.facishare.open.order.contacts.proxy.api.data;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * Created by chenzx on 2015/01/02.
 * 用于创建订单
 */
@Data
public class Crm112OrderObjectData {
    /**
     * SalesOrderObj对象的 account_id 即 AccountObj 对象的 _id
     */
    @JSONField(name = "account_id")
    private String customerId;

    /**
     * SalesOrderObj record_type
     */
    @JSONField(name = "record_type")
    private String recordType;

    /**
     * SalesOrderObj object_describe_api_name
     */
    @JSONField(name = "object_describe_api_name")
    private String objectDescribeApiName;

    /**
     * SalesOrderObj object_describe_id
     */
    @JSONField(name = "object_describe_id")
    private String objectDescribeId;

    /**
     * SalesOrderObj UDSSel1__c
     * 订单类型：1-标准，2-试用，3-赠送
     */
    @JSONField(name = "UDSSel1__c")
    private String orderTpye;

    /**
     * SalesOrderObj UDSSel4__c
     * 是否触发工作流: 1-是，2-否
     */
    @JSONField(name = "UDSSel4__c")
    private String isTriggerWorkFlow;

    /**
     * SalesOrderObj order_time
     */
    @JSONField(name = "order_time")
    private String orderTime;

    /**
     * SalesOrderObj UDSText1__c
     */
    @JSONField(name = "UDSText1__c")
    private String orderId;

    /**
     * SalesOrderObj UDSText3__c
     */
    @JSONField(name = "UDSText3__c")
    private String managerName;

    /**
     * SalesOrderObj UDSText4__c
     */
    @JSONField(name = "UDSText4__c")
    private String enterpriseAccount;

    /**
     * SalesOrderObj UDTel1__c
     */
    @JSONField(name = "UDTel1__c")
    private String managerMobile;

    /**
     * SalesOrderObj UDMail1__c
     */
    @JSONField(name = "UDMail1__c")
    private String managerEmail;

    /**
     * SalesOrderObj price_book_id
     */
    @JSONField(name = "price_book_id")
    private String priceBookId;

    /**
     * SalesOrderObj owner
     */
    @JSONField(name = "owner")
    private List<String> ownerIds;

    /**
     * SalesOrderObj field_tvS1k__c
     */
    @JSONField(name = "field_tvS1k__c")
    private String outAccountSource;

    /**
     * SalesOrderObj UDSText2__c
     * 合同章名称
     */
    @JSONField(name = "UDSText2__c")
    private String contractStamp;

    /**
     * SalesOrderObj UDSSel2__c
     * 采购性质
     */
    @JSONField(name = "UDSSel2__c")
    private String buyNature;

    /**
     * SalesOrderObj UDSSel3__c
     * 合同签约主体
     */
    @JSONField(name = "UDSSel3__c")
    private String contractMainBody;

    /**
     * SalesOrderObj UDDate1__c
     * 业务归属日期
     */
    @JSONField(name = "UDDate1__c")
    private String businessDate;
}

//package com.facishare.webhook.common.dao.paas.model;
//
//import com.alibaba.fastjson.annotation.JSONField;
//import lombok.Data;
//
//import java.util.List;
//
///**
// * Created by mawenrui on 2019/4/19.
// * 用于创建订单
// */
//@Data
//public class CrmOrderData {
//    /**
//     * SalesOrderObj对象的 account_id 即 AccountObj 对象的 _id
//     */
//    @JSONField(name = "account_id")
//    private String customerId;
//
//    /**
//     * SalesOrderObj record_type
//     */
//    @JSONField(name = "record_type")
//    private String recordType;
//
//    /**
//     * SalesOrderObj object_describe_api_name
//     */
//    @JSONField(name = "object_describe_api_name")
//    private String objectDescribeApiName;
//
//    /**
//     * SalesOrderObj object_describe_id
//     */
//    @JSONField(name = "object_describe_id")
//    private String objectDescribeId;
//
//    /**
//     * SalesOrderObj UDSSel1__c
//     * 订单类型：1-标准，2-试用，3-赠送
//     */
//    @JSONField(name = "UDSSel1__c")
//    private String orderTpye;
//
//    /**
//     * SalesOrderObj UDSSel2__c
//     * 是否触发工作流: 1-是，2-否
//     */
//    @JSONField(name = "UDSSel2__c")
//    private String isTriggerWorkFlow;
//
//    /**
//     * SalesOrderObj order_time
//     */
//    @JSONField(name = "order_time")
//    private String orderTime;
//
//    /**
//     * SalesOrderObj UDSText5__c
//     */
//    @JSONField(name = "UDSText5__c")
//    private String orderId;
//
//    /**
//     * SalesOrderObj UDSText6__c
//     */
//    @JSONField(name = "UDSText6__c")
//    private String managerName;
//
//    /**
//     * SalesOrderObj UDSText4__c
//     */
//    @JSONField(name = "UDSText4__c")
//    private String enterpriseAccount;
//
//    /**
//     * SalesOrderObj UDTel1__c
//     */
//    @JSONField(name = "UDTel1__c")
//    private String managerMobile;
//
//    /**
//     * SalesOrderObj UDMail1__c
//     */
//    @JSONField(name = "UDMail1__c")
//    private String managerEmail;
//
//    /**
//     * SalesOrderObj price_book_id
//     */
//    @JSONField(name = "price_book_id")
//    private String priceBookId;
//
//    /**
//     * SalesOrderObj owner
//     */
//    @JSONField(name = "owner")
//    private List<String> ownerIds;
//
//    /**
//     * SalesOrderObj field_uT2Cf__c
//     */
//    @JSONField(name = "field_uT2Cf__c")
//    private String outAccountSource;
//
//    /**
//     * SalesOrderObj UDSText9__c
//     * 合同章名称
//     */
//    @JSONField(name = "UDSText9__c")
//    private String contractStamp;
//
//    /**
//     * SalesOrderObj UDSSel7__c
//     * 采购性质
//     */
//    @JSONField(name = "UDSSel7__c")
//    private String buyNature;
//
//    /**
//     * SalesOrderObj UDSSel6__c
//     * 合同签约主体
//     */
//    @JSONField(name = "UDSSel6__c")
//    private String contractMainBody;
//
//    /**
//     * SalesOrderObj UDDate3__c
//     * 业务归属日期
//     */
//    @JSONField(name = "UDDate3__c")
//    private String businessDate;
//}
