package com.facishare.open.order.contacts.proxy.api.data;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by chenzx on 2025/01/02.
 */
@Data
public class Crm112CustomerObjectData implements Serializable {
    /**
     * AccountObj UDInt1__c
     */
    @JSONField(name = "UDInt1__c")
    private Integer customerEnterpriseId;

    /**
     * AccountObj UDSText2__c
     */
    @JSONField(name = "UDSText2__c")
    private String enterpriseAccount;

    /**
     * AccountObj name
     */
    @JSONField(name = "name")
    private String name;

    /**
     * AccountObj owner
     */
    @JSONField(name = "owner")
    private List<String> owner;

    /**
     * AccountObj UDSText7__c
     */
    @JSONField(name = "UDSText7__c")
    private String enterpriseName;

    /**
     * AccountObj field_1uJcQ__c
     */
    @JSONField(name = "field_1uJcQ__c")
    private Long enterpriseCreateTime;

    /**
     * AccountObj field_5Q4od__c
     */
    @JSONField(name = "field_5Q4od__c")
    private String enterpriseStatus;

    /**
     * AccountObj field_4a9k8__c
     */
    @JSONField(name = "field_4a9k8__c")
    private String enterpriseType;

    /**
     * AccountObj UDSText3__c
     */
    @JSONField(name = "UDSText3__c")
    private String crmVersion;

    /**
     * AccountObj UDDate1__c
     */
    @JSONField(name = "UDDate1__c")
    private Long crmEndDate;

    /**
     * AccountObj UDInt3__c
     */
    @JSONField(name = "UDInt3__c")
    private Integer crmCount;

    /**
     * AccountObj field_ek4sE__c
     */
    @JSONField(name = "field_ek4sE__c")
    private Integer usedCrmCount;

    /**
     * AccountObj field_iF5k9__c
     */
    @JSONField(name = "field_iF5k9__c")
    private Integer unusedCrmCount;

    /**
     * AccountObj UDDate2__c
     */
    @JSONField(name = "UDDate2__c")
    private Long officeEndDate;

    /**
     * AccountObj UDInt4__c
     */
    @JSONField(name = "UDInt4__c")
    private Integer officeCount;

    /**
     * AccountObj field_24044__c
     */
    @JSONField(name = "field_24044__c")
    private Integer usedOfficeCount;

    /**
     * AccountObj field_oXvR3__c
     */
    @JSONField(name = "field_oXvR3__c")
    private Integer unusedOfficeCount;

    /**
     * AccountObj UDSText4__c
     */
    @JSONField(name = "UDSText4__c")
    private String managerName;

    /**
     * AccountObj UDSText5__c
     */
    @JSONField(name = "UDSText5__c")
    private String managerMobile;

    /**
     * AccountObj UDMail1__c
     */
    @JSONField(name = "UDMail1__c")
    private String managerEmail;

    /**
     * AccountObj account_source
     */
    @JSONField(name = "account_source")
    private String source;

    /**
     * AccountObj field_fsj82__c
     */
    @JSONField(name = "field_fsj82__c")
    private String outEid;

    /**
     * AccountObj UDSSel37__c
     */
    @JSONField(name = "UDSSel37__c")
    private String customerGroup;

    /**
     * AccountObj field_0tt39__c
     */
    @JSONField(name = "field_0tt39__c")
    private String upStreamCustomerId;
}