package com.facishare.open.order.contacts.proxy.api.data;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * Created by chenzx on 2025/01/02.
 */
@Data
public class CrmCustomerObjectData implements Serializable {

    /**
     * AccountObj UDInt12__c
     */
    @JSONField(name = "UDInt12__c")
    private Integer customerEnterpriseId;

    /**
     * AccountObj UDSText6__c
     */
    @JSONField(name = "UDSText6__c")
    private String enterpriseAccount;

    /**
     * AccountObj UDSText7__c
     */
    @JSONField(name = "UDSText7__c")
    private String enterpriseName;

    /**
     * AccountObj name
     */
    @JSONField(name = "name")
    private String name;

    /**
     * AccountObj owner
     */
    @JSONField(name = "owner")
    private List<String> owner;

    /**
     * AccountObj UDDate6__c
     */
    @J<PERSON><PERSON>ield(name = "UDDate6__c")
    private Long enterpriseCreateTime;

    /**
     * AccountObj UDSSel32__c
     */
    @JSONField(name = "UDSSel32__c")
    private String enterpriseStatus;

    /**
     * AccountObj UDSSel36__c
     */
    @JSONField(name = "UDSSel36__c")
    private String enterpriseType;

    /**
     * AccountObj UDSText8__c
     */
    @JSONField(name = "UDSText8__c")
    private String crmVersion;

    /**
     * AccountObj UDDate3__c
     */
    @JSONField(name = "UDDate3__c")
    private Long crmEndDate;

    /**
     * AccountObj UDInt2__c
     */
    @JSONField(name = "UDInt2__c")
    private Integer crmCount;

    /**
     * AccountObj UDInt3__c
     */
    @JSONField(name = "UDInt3__c")
    private Integer usedCrmCount;

    /**
     * AccountObj UDInt4__c
     */
    @JSONField(name = "UDInt4__c")
    private Integer unusedCrmCount;

    /**
     * AccountObj UDDate4__c
     */
    @JSONField(name = "UDDate4__c")
    private Long officeEndDate;

    /**
     * AccountObj UDInt5__c
     */
    @JSONField(name = "UDInt5__c")
    private Integer officeCount;

    /**
     * AccountObj UDInt6__c
     */
    @JSONField(name = "UDInt6__c")
    private Integer usedOfficeCount;


    /**
     * AccountObj UDInt7__c
     */
    @JSONField(name = "UDInt7__c")
    private Integer unusedOfficeCount;

    /**
     * AccountObj UDSText9__c
     */
    @JSONField(name = "UDSText9__c")
    private String managerName;

    public String getManagerName() {
        if (StringUtils.isEmpty(managerName)) {
            return "Unknown";
        }
        return managerName.length() > 20 ? managerName.substring(0, 20) : managerName;
    }

    /**
     * AccountObj UDTel1__c
     */
    @JSONField(name = "UDTel1__c")
    private String managerMobile;

    /**
     * AccountObj UDMail1__c
     */
    @JSONField(name = "UDMail1__c")
    private String managerEmail;

    /**
     * AccountObj account_source
     */
    @JSONField(name = "account_source")
    private String source;

    /**
     * AccountObj field_fsj82__c
     */
    @JSONField(name = "field_fsj82__c")
    private String outEid;

    /**
     * AccountObj UDSSel37__c
     */
    @JSONField(name = "UDSSel37__c")
    private String customerGroup;

    /**
     * AccountObj field_0tt39__c
     */
    @JSONField(name = "field_0tt39__c")
    private String upStreamCustomerId;
}
