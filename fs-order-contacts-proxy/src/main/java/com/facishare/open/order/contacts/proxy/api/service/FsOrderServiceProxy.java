package com.facishare.open.order.contacts.proxy.api.service;

import com.facishare.open.order.contacts.proxy.api.arg.CreateCrmOrderArg;
import com.facishare.open.order.contacts.proxy.api.arg.CreateCustomerArg;
import com.facishare.open.order.contacts.proxy.api.arg.QueryCustomerArg;
import com.facishare.open.order.contacts.proxy.api.data.FsCustomerObjectData;
import com.facishare.open.order.contacts.proxy.api.data.FsObjectCustomer;
import com.facishare.open.order.contacts.proxy.api.model.CrmOrderMappingModel;
import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.SearchTemplateQuery;

import java.io.Serializable;
import java.util.List;

/**
 * 纷享订单服务
 * <AUTHOR>
 * @date 20220727
 */
public interface FsOrderServiceProxy extends Serializable {
    /**
     * 创建CRM客户
     * @param arg
     * @return
     */
    Result<Void> createCustomer(CreateCustomerArg arg);

    /**
     * 创建CRM订单
     * @param arg
     * @return
     */
    Result<Void> createCrmOrder(CreateCrmOrderArg arg);

    /**
     * 批量创建CRM订单
     * @param orderArgList
     * @return
     */
    Result<List<CrmOrderMappingModel>> batchCreateCrmOrder(List<CreateCrmOrderArg> orderArgList);

    /**
     * 下单开通CRM企业
     * @param customerArg
     * @param orderArg
     * @return
     */
    Result<Void> openEnterprise(CreateCustomerArg customerArg, CreateCrmOrderArg orderArg);

//    /**
//     * CRM企业开通事件处理逻辑
//     * @param ei
//     * @param ea
//     * @param enterpriseName
//     * @return
//     */
//    Result<Void> onEnterpriseOpened(Integer ei,String ea,String enterpriseName);

    Result<List<ObjectData>> queryCustomer(Integer ei, String outEa);

    Result<String> createObjCustomer(FsObjectCustomer fsObjectCustomer);

    Result<Void> deleteCustomer(String customerId);

    Result<String> createCustomer2(CreateCustomerArg arg);

    Result<List<ObjectData>> queryCustomerBySearchTemplate(QueryCustomerArg arg);

    Result<Void> createCrmOrder2(CreateCrmOrderArg arg);

    Result<List<ObjectData>> queryProductById(String productId);
}
