package com.facishare.open.order.contacts.proxy.manager;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.open.order.contacts.proxy.api.arg.CreateCrmOrderArg;
import com.facishare.open.order.contacts.proxy.api.arg.CreateCustomerArg;
import com.facishare.open.order.contacts.proxy.api.consts.GlobalValue;
import com.facishare.open.order.contacts.proxy.api.data.*;
import com.facishare.open.order.contacts.proxy.api.detail.Crm112OrderDetail;
import com.facishare.open.order.contacts.proxy.api.detail.CrmOrderDetail;
import com.facishare.open.order.contacts.proxy.api.enums.CrmOrderRecordTypeEnum;
import com.facishare.open.order.contacts.proxy.api.model.CrmOrderProductModel;
import com.facishare.open.order.contacts.proxy.api.network.ProxyHttpClient;
import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.facishare.open.order.contacts.proxy.api.result.ResultCodeEnum;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.order.contacts.proxy.config.ConfigCenter;
import com.facishare.open.order.contacts.proxy.limiter.CrmRateLimiter;
import com.facishare.open.order.contacts.proxy.paas.dao.Crm112ObjectCustomerDao;
import com.facishare.open.order.contacts.proxy.paas.dao.CrmObjectCustomerDao;
import com.facishare.open.order.contacts.proxy.service.impl.FsEmployeeAndDepartmentProxy;
import com.facishare.organization.adapter.api.exception.ErrorEmployeeStopException;
import com.facishare.organization.adapter.api.permission.enums.role.SystemRoleEnum;
import com.facishare.organization.adapter.api.permission.model.AddRoleWithDepartmentToEmployeesByAppId;
import com.facishare.organization.adapter.api.permission.model.BatchGetRoleCodesByEmployeeIds;
import com.facishare.organization.adapter.api.permission.model.RoleCodeAndDepartmentIds;
import com.facishare.organization.adapter.api.permission.service.PermissionService;
import com.facishare.organization.api.exception.OrganizationException;
import com.facishare.paas.dao.BaseDao;
import com.facishare.paas.license.Result.LicenseVersionResult;
import com.facishare.paas.license.arg.QueryProductArg;
import com.facishare.paas.license.http.LicenseClient;
import com.facishare.paas.model.DeleteById;
import com.facishare.paas.model.PaasBean;
import com.facishare.privilege.api.UserPrivilegeRestService;
import com.facishare.privilege.api.module.PrivilegeContext;
import com.facishare.privilege.api.module.user.BatchAddUserRoleVo;
import com.facishare.privilege.api.module.user.DeleteByUserIdsVo;
import com.facishare.privilege.api.module.user.GetRolesByUsersVo;
import com.facishare.privilege.api.module.user.UserRoleVo;
import com.facishare.webhook.common.dao.paas.dao.CrmOrderProductObject;
import com.fxiaoke.crmrestapi.arg.ActionAddArg;
import com.fxiaoke.crmrestapi.common.contants.CrmConstants;
import com.fxiaoke.crmrestapi.common.data.HeaderObj;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.common.data.SearchTemplateQuery;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.ObjectDataCreateResult;
import com.fxiaoke.crmrestapi.result.QueryBySearchTemplateResult;
import com.fxiaoke.crmrestapi.service.MetadataActionService;
import com.fxiaoke.crmrestapi.service.ObjectDataService;
import com.github.trace.TraceContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.facishare.webhook.common.util.Constant.*;


@Service
@Slf4j
public class CrmManager {
    @Autowired
    private ProxyHttpClient proxyHttpClient;
    //创建客户的url
    private String CREATE_CUSTOMER_URL = "versionRegisterService/addCrmCustomer";
    //创建订单的url
    private String ORDER_URL = "versionRegisterService/addCrmOrder";

    //人员相关的api
    @Autowired
    private UserPrivilegeRestService userPrivilegeService;
    @Autowired
    private PermissionService permissionService;
    @Resource(name = "licenseClient")
    private LicenseClient licenseClient;

    @Autowired
    private CrmObjectCustomerDao crmObjectCustomerDao;
    @Autowired
    private Crm112ObjectCustomerDao crm112ObjectCustomerDao;
    @Resource
    private FsEmployeeAndDepartmentProxy fsEmployeeAndDepartmentProxy;

    @Autowired
    private ObjectDataService objectDataService;
    @Autowired
    private MetadataActionService metadataActionService;

    private static final String BUY_NATURE_API_NAME = "2"; // 增购
    private static final String CONTRACT_MAINBODY_API_NAME = "2"; // 纷扬


    //新企业创建客户
    public Result<String> createCustomer(CreateCustomerArg createCustomerArg) {
        //限速
        if(!CrmRateLimiter.isAllowed(null)) {
            return Result.newError(ResultCodeEnum.CREATE_CUSTOM_FAILED);
        }
        Object createJson = JSONObject.toJSON(createCustomerArg);
        String result = proxyHttpClient.postUrl(ConfigCenter.RESET_URL.concat(CREATE_CUSTOMER_URL), createJson, createHeader());
        log.info("createCustomer,result={}",result);
        Map map = JSONObject.parseObject(result, Map.class);
        if(ObjectUtils.isNotEmpty(map.get("customerId"))){
            return Result.newSuccess(result);
        }
        return Result.newError(ResultCodeEnum.CREATE_CUSTOM_FAILED,result);
    }

    //创建订单
    public Result<String> createCrmOrder(CreateCrmOrderArg createCrmOrderArg) {
        //限速
        if(!CrmRateLimiter.isAllowed(null)) {
            return Result.newError(ResultCodeEnum.CREATE_ORDER_FAILED);
        }
        String createJson = JSONObject.toJSON(createCrmOrderArg).toString();
        String result = proxyHttpClient.postUrl(ConfigCenter.RESET_URL.concat(ORDER_URL), createJson, createHeader());
        log.info("createCrmOrder,result={}",result);
        Object isSuccess = JSONPath.read(result, "$.isSuccess");
        if (ObjectUtils.isEmpty(isSuccess) || !Boolean.valueOf(isSuccess.toString())) {
            log.error("create crm order failed arg:{}", createCrmOrderArg);
            return Result.newError(ResultCodeEnum.CREATE_ORDER_FAILED);
        }
        return Result.newSuccess(JSONPath.read(result, "$.orderId").toString());
    }

    /**
     * 批量添加用户角色
     * @param ei
     * @param empIds
     * @param roleCodeList
     * @param mainRoleCode
     * @return
     */
    public Result<Void> batchAddUserRole(Integer ei, List<String> empIds,List<String> roleCodeList, String mainRoleCode) {
        if(CollectionUtils.isEmpty(empIds)){
            return Result.newSuccess();
        }
        PrivilegeContext crm = PrivilegeContext.builder().appId("CRM").tenantId(ei).operatorId(GlobalValue.SYSTEM_USER_ID).build();
        BatchAddUserRoleVo.Argument argument = new BatchAddUserRoleVo.Argument();
        argument.setRoleCodes(roleCodeList);
        argument.setUserIds(empIds);
        argument.setMajorRole(mainRoleCode);
        argument.setUpdateMajorRole(false);
        BatchAddUserRoleVo.Result result = null;
        try {
            result = userPrivilegeService.batchAddUserRole(crm, argument);
            LogUtils.info("CrmManager.batchAddUserRole,result={}",result);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Result.newSuccess();
    }

    /**
     * 批量移除用户角色
     * @param ei
     * @param empIds
     * @param roleCode
     * @return
     */
    public Result<Void> batchDeleteUserRole(Integer ei, List<String> empIds, String roleCode) {
        final PrivilegeContext crm = PrivilegeContext.builder().appId("CRM").tenantId(ei).operatorId(GlobalValue.SYSTEM_USER_ID).build();
        DeleteByUserIdsVo.Argument argument = new DeleteByUserIdsVo.Argument();
        argument.setRoleCode(roleCode);
        argument.setUserIds(empIds);
        DeleteByUserIdsVo.Argument result = userPrivilegeService.deleteByUserIds(crm, argument);
        LogUtils.info("CrmManager.batchDeleteUserRole,result={}",result);
        return Result.newSuccess();
    }

    /**
     * 添加管理员角色
     * @param ei
     * @param empIdList
     * @return
     */
    public Result<Void> addManagerRole(Integer ei, List<Integer> empIdList) {
        AddRoleWithDepartmentToEmployeesByAppId.Argument argument = new AddRoleWithDepartmentToEmployeesByAppId.Argument();
        RoleCodeAndDepartmentIds roleCodeAndDepartmentIds = new RoleCodeAndDepartmentIds();
        roleCodeAndDepartmentIds.setRoleCode("99");
        roleCodeAndDepartmentIds.setIsOpen(true);
        roleCodeAndDepartmentIds.setDepartmentIds(Lists.newArrayList(GlobalValue.ALL_COMPANY_DEPARTMENT_ID));
        argument.setRoleCodeAndDepartmentIds(roleCodeAndDepartmentIds);
        argument.setEnterpriseId(ei);
        argument.setAppId("facishare-system");
        argument.setEmployeeIds(empIdList);
        argument.setCurrentEmployeeId(GlobalValue.SYSTEM_USER_ID);
        try {
            AddRoleWithDepartmentToEmployeesByAppId.Result result = permissionService.addRoleWithDepartmentToEmployeesByAppId(argument);
            LogUtils.info("CrmManager.addManagerRole,result={}",result);
        } catch (ErrorEmployeeStopException e) {
            return Result.newError(e.getErrorCode(),e.getMessage());
        } catch (OrganizationException e) {
            return Result.newError(e.getErrorCode(),e.getMessage());
        } catch (Exception e) {
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR.getCode(),e.getMessage());
        }
        return Result.newSuccess();
    }

    public LicenseVersionResult queryProductVersion(QueryProductArg queryProductArg) {
        LicenseVersionResult result = licenseClient.queryProductVersion(queryProductArg);
        LogUtils.info("CrmManager.queryProductVersion,result={}",result);
        return result;
    }

    public static Map<String, String> createHeader() {
        Map<String, String> headers = Maps.newHashMapWithExpectedSize(1);
        headers.put("Content-Type", "application/json");
        return headers;
    }

    public Result<List<UserRoleVo>> batchGetUserRoles(Integer ei, List<String> userIds) {
        PrivilegeContext crm = PrivilegeContext.builder().appId("CRM").tenantId(ei).operatorId(GlobalValue.SYSTEM_USER_ID).build();
        GetRolesByUsersVo.Argument arg = new GetRolesByUsersVo.Argument();
        arg.setUserIds(userIds);
        //rest接口支持跨云调用必传参数
        TraceContext.get().setEi(ei + "");
        List<UserRoleVo> usersRoles = userPrivilegeService.getRolesByUsers(crm, arg);
        //移除上下文，避免跨云调用混乱
        TraceContext.remove();
        LogUtils.info("CrmManager.batchGetUserRoles,usersRoles={}",usersRoles);
        return Result.newSuccess(usersRoles);
    }

    public Result<BatchGetRoleCodesByEmployeeIds.Result> batchGetEmployeeRoleCodes(Integer ei, List<Integer> employeeIds) {
        BatchGetRoleCodesByEmployeeIds.Argument argument = new BatchGetRoleCodesByEmployeeIds.Argument();
        argument.setEnterpriseId(ei);
        argument.setAppId(SystemRoleEnum.APP_ID);
        argument.setEmployeeIds(employeeIds);
        //rest接口支持跨云调用必传参数
        TraceContext.get().setEi(ei + "");
        BatchGetRoleCodesByEmployeeIds.Result result = permissionService.batchGetRoleCodesByEmployeeIds(argument);
        //移除上下文，避免跨云调用混乱
        TraceContext.remove();
        LogUtils.info("CrmManager.batchGetEmployeeRoleCodes,result={}",result);
        return Result.newSuccess(result);
    }

    public Result<String> createObjCustomer(FsObjectCustomer fsObjectCustomer) {
        //限速
        if(!CrmRateLimiter.isAllowed(null)) {
            return Result.newError(ResultCodeEnum.CREATE_CUSTOM_FAILED);
        }

        String env = System.getProperty("process.profile");
        if(StringUtils.isEmpty(env) || env.equals("fstest") || env.equals("fstest-gray")) {
            Crm112ObjectCustomer crm112ObjectCustomer = new Crm112ObjectCustomer();
            BeanUtils.copyProperties(fsObjectCustomer, crm112ObjectCustomer);
            crm112ObjectCustomer.set__xxx_enterpriseId(ENTERPRISEID);
            crm112ObjectCustomer.setOperatorId(OPERATERID);

            BaseDao.OptionInfo optionInfo = new BaseDao.OptionInfo();
            optionInfo.setIsDuplicateSearch(false);

            Map<String, Object> map = Maps.newHashMap();
            map.put("optionInfo", JSON.toJSON(optionInfo));

            PaasBean.Result<Crm112ObjectCustomer> paasResult = null;
            try {
                paasResult = crm112ObjectCustomerDao.insert(crm112ObjectCustomer, map);
            } catch (Exception e) {
                LogUtils.info("CrmManager.createObjCustomer error,message={}", e.getMessage());
                return Result.newError(ResultCodeEnum.CREATE_CUSTOM_FAILED);
            }

            LogUtils.info("CrmManager.createObjCustomer,paasResult={}",paasResult);

            if(ObjectUtils.isEmpty(paasResult.getObject_data()) || ObjectUtils.isEmpty(paasResult.getObject_data().get("_id"))) {
                return Result.newError(ResultCodeEnum.CREATE_CUSTOM_FAILED);
            }

            return Result.newSuccess((String) paasResult.getObject_data().get("_id"));
        } else {
            CrmObjectCustomer crmObjectCustomer = new CrmObjectCustomer();
            BeanUtils.copyProperties(fsObjectCustomer, crmObjectCustomer);
            crmObjectCustomer.set__xxx_enterpriseId(ENTERPRISEID);
            crmObjectCustomer.setOperatorId(OPERATERID);

            BaseDao.OptionInfo optionInfo = new BaseDao.OptionInfo();
            optionInfo.setIsDuplicateSearch(false);

            Map<String, Object> map = Maps.newHashMap();
            map.put("optionInfo", JSON.toJSON(optionInfo));

            PaasBean.Result<CrmObjectCustomer> paasResult = null;
            try {
                paasResult = crmObjectCustomerDao.insert(crmObjectCustomer, map);
            } catch (Exception e) {
                LogUtils.info("CrmManager.createObjCustomer error,message={}", e.getMessage());
                return Result.newError(ResultCodeEnum.CREATE_CUSTOM_FAILED);
            }

            LogUtils.info("CrmManager.createObjCustomer,paasResult={}",paasResult);

            if(ObjectUtils.isEmpty(paasResult.getObject_data()) || ObjectUtils.isEmpty(paasResult.getObject_data().get("_id"))) {
                return Result.newError(ResultCodeEnum.CREATE_CUSTOM_FAILED);
            }

            return Result.newSuccess((String) paasResult.getObject_data().get("_id"));
        }
    }

    public Result<Void> deleteCustomer(String customerId) {
        //限速
        if(!CrmRateLimiter.isAllowed(null)) {
            return Result.newError(ResultCodeEnum.CREATE_CUSTOM_FAILED);
        }

        //作废
        String url = ConfigCenter.CRM_REST_OBJ_URL+ "/AccountObj/action/BulkInvalid";
        String param = "{\"json\":\"{\\\"dataList\\\":[{\\\"object_describe_api_name\\\":\\\"AccountObj\\\",\\\"tenant_id\\\":\\\"{tenant_id}\\\",\\\"_id\\\":\\\"{_id}\\\"}]}\"}";
        param = param.replace("{tenant_id}", ConfigCenter.MASTER_EA + "").replace("{_id}", customerId);

        Result<List<ObjectData>> result = fsEmployeeAndDepartmentProxy.postUrl6(url, param, ConfigCenter.MASTER_EA + "", "objectDataList");
        LogUtils.info("CrmManager.deleteCustomer,outEa={},result={}", customerId, result);

        String env = System.getProperty("process.profile");
        if(StringUtils.isEmpty(env) || env.equals("fstest") || env.equals("fstest-gray")) {
            DeleteById.Argument arg = new DeleteById.Argument();
            arg.set__xxx_enterpriseId(ENTERPRISEID);
            arg.setOperatorId(OPERATERID);
            arg.setApiName("AccountObj");
            arg.setObjectDataId(customerId);
            PaasBean.Result<Crm112ObjectCustomer> crm112ObjectCustomerResult = crm112ObjectCustomerDao.deleteById(arg);
            LogUtils.info("CrmManager.deleteCustomer,crm112ObjectCustomerResult={}",crm112ObjectCustomerResult);
            if(ObjectUtils.isEmpty(crm112ObjectCustomerResult)) {
                return Result.newError(ResultCodeEnum.CREATE_CUSTOM_FAILED);
            }
            return Result.newSuccess();
        } else {
            DeleteById.Argument arg = new DeleteById.Argument();
            arg.set__xxx_enterpriseId(ENTERPRISEID);
            arg.setOperatorId(OPERATERID);
            arg.setApiName("AccountObj");
            arg.setObjectDataId(customerId);
            PaasBean.Result<CrmObjectCustomer> crmObjectCustomerResult = crmObjectCustomerDao.deleteById(arg);
            LogUtils.info("CrmManager.deleteCustomer,crmObjectCustomerResult={}",crmObjectCustomerResult);
            if(ObjectUtils.isEmpty(crmObjectCustomerResult)) {
                return Result.newError(ResultCodeEnum.CREATE_CUSTOM_FAILED);
            }
            return Result.newSuccess();
        }
    }

    public Result<String> createCustomer2(CreateCustomerArg arg) {
        //限速
        if(!CrmRateLimiter.isAllowed(null)) {
            return Result.newError(ResultCodeEnum.CREATE_CUSTOM_FAILED);
        }
        HeaderObj headerObj = HeaderObj.newInstance(ConfigCenter.MASTER_EA, CrmConstants.SYSTEM_USER);

        HashMap<String, String> crmProductIdMap = JSON.parseObject(ConfigCenter.crm_account_source_ids, new TypeToken<HashMap<String, String>>() {
        }.getType());

        ObjectData objectData;
        String env = System.getProperty("process.profile");
        if(StringUtils.isEmpty(env) || env.equals("fstest") || env.equals("fstest-gray")) {
            Crm112CustomerObjectData crm112CustomerObjectData = new Crm112CustomerObjectData();
            BeanUtils.copyProperties(arg, crm112CustomerObjectData);
            crm112CustomerObjectData.setSource(crmProductIdMap.get(arg.getCustomerSource()));
            crm112CustomerObjectData.setName(arg.getEnterpriseName());
            crm112CustomerObjectData.setOwner(Lists.newArrayList(String.valueOf(CrmConstants.SYSTEM_USER)));
            objectData = convertToObjectData(crm112CustomerObjectData);
        } else {
            CrmCustomerObjectData crmCustomerObjectData = new CrmCustomerObjectData();
            BeanUtils.copyProperties(arg, crmCustomerObjectData);
            crmCustomerObjectData.setSource(crmProductIdMap.get(arg.getCustomerSource()));
            crmCustomerObjectData.setName(arg.getEnterpriseName());
            crmCustomerObjectData.setOwner(Lists.newArrayList(String.valueOf(CrmConstants.SYSTEM_USER)));
            objectData = convertToObjectData(crmCustomerObjectData);
        }

        LogUtils.info("CrmManager.createCustomer,arg={}", arg);

        com.fxiaoke.crmrestapi.common.result.Result<ObjectDataCreateResult> objectDataCreateResultResult = objectDataService.create(headerObj, "AccountObj", Boolean.FALSE, Boolean.TRUE, Boolean.FALSE, objectData);

        LogUtils.info("CrmManager.createCustomer,objectDataCreateResultResult={}", objectDataCreateResultResult);

        if (objectDataCreateResultResult.isSuccess() && ObjectUtils.isNotEmpty(objectDataCreateResultResult.getData().getObjectData().getId())) {
            return Result.newSuccess(objectDataCreateResultResult.getData().getObjectData().getId());
        }

        return Result.newError(ResultCodeEnum.CREATE_CUSTOM_FAILED, objectDataCreateResultResult.getMessage());
    }

    public static ObjectData convertToObjectData(Object obj) {
        ObjectData objectData = new ObjectData();
        Class<?> clazz = obj.getClass();

        // 遍历对象的所有字段
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);
            try {
                // 使用 JSONField 注解的 name 属性作为键名
                JSONField jsonField = field.getAnnotation(JSONField.class);
                String fieldName = jsonField != null ? jsonField.name() : field.getName();

                // 将字段名和字段值放入 ObjectData 中
                objectData.put(fieldName, field.get(obj));
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return objectData;
    }

    public Result<List<ObjectData>> queryBySearchTemplate(String apiName, SearchTemplateQuery searchTemplateQuery) {
        HeaderObj headerObj = HeaderObj.newInstance(ConfigCenter.MASTER_EA, CrmConstants.SYSTEM_USER);
        return queryBySearchTemplate(apiName, headerObj, searchTemplateQuery);
    }

    public Result<List<ObjectData>> queryBySearchTemplate(String apiName, HeaderObj headerObj, SearchTemplateQuery searchTemplateQuery) {
        //限速
        if(!CrmRateLimiter.isAllowed(null)) {
            return Result.newError(ResultCodeEnum.CREATE_CUSTOM_FAILED);
        }

        com.fxiaoke.crmrestapi.common.result.Result<QueryBySearchTemplateResult> queryBySearchTemplateResult = objectDataService.queryBySearchTemplate(headerObj, apiName, searchTemplateQuery);

        LogUtils.info("CrmManager.queryBySearchTemplate,queryBySearchTemplateResult={}", queryBySearchTemplateResult);

        if (!queryBySearchTemplateResult.isSuccess()) {
            return Result.newError(ResultCodeEnum.SYSTEM_ERROR, queryBySearchTemplateResult.getMessage());
        }
        return  Result.newSuccess(queryBySearchTemplateResult.getData().getQueryResult().getData());
    }

    public Result<Void> createCrmOrder2(CreateCrmOrderArg arg, CrmOrderProductModel crmOrderProductModel, ObjectData customerData) {
        //限速
        if(!CrmRateLimiter.isAllowed(null)) {
            return Result.newError(ResultCodeEnum.CREATE_CUSTOM_FAILED);
        }
        HeaderObj headerObj = HeaderObj.newInstance(ConfigCenter.MASTER_EA, CrmConstants.SYSTEM_USER);

        ActionAddArg actionAddArg;
        String env = System.getProperty("process.profile");
        if(StringUtils.isEmpty(env) || env.equals("fstest") || env.equals("fstest-gray")) {
            actionAddArg = buildCrm112OrderInfo(arg, crmOrderProductModel, customerData);
        } else {
            actionAddArg = buildCrmOrderInfo(arg, crmOrderProductModel, customerData);
        }

        com.fxiaoke.crmrestapi.common.result.Result<ActionAddResult> objectDataCreateResultResult = metadataActionService.add(headerObj, "SalesOrderObj", Boolean.FALSE, Boolean.FALSE, Boolean.FALSE,  Boolean.TRUE, actionAddArg);

        LogUtils.info("CrmManager.createCustomer,objectDataCreateResultResult={}", objectDataCreateResultResult);

        if (objectDataCreateResultResult.isSuccess() && ObjectUtils.isNotEmpty(objectDataCreateResultResult.getData().getObjectData().getId())) {
            return Result.newSuccess();
        }

        return Result.newError(ResultCodeEnum.CREATE_ORDER_FAILED, objectDataCreateResultResult.getMessage());
    }

    public static ActionAddArg buildCrm112OrderInfo(CreateCrmOrderArg arg, CrmOrderProductModel crmOrderProductModel, ObjectData customerData) {
        HashMap<String, String> crmOrderTypeMap = JSON.parseObject(ConfigCenter.crm_order_type_ids, new TypeToken<HashMap<String, String>>() {
        }.getType());
        if (!crmOrderTypeMap.containsKey(arg.getCrmOrderDetailInfo().getCrmOrderType())) {
            LogUtils.error("CrmManager.buildCrm112OrderInfo,crmOrderTypeMap.get(arg.getCrmOrderDetailInfo().getCrmOrderType()) is null,arg={}", arg);
            return null;
        }

        Crm112CustomerObjectData crmCustomerObjectData = JSON.parseObject(JSON.toJSONString(customerData), Crm112CustomerObjectData.class);

        ObjectData crmOrderObjectData = new ObjectData();

        Crm112OrderObjectData crmOrderData = new Crm112OrderObjectData();
        crmOrderData.setCustomerId(customerData.getId());
        crmOrderData.setRecordType(CrmOrderRecordTypeEnum.ONLINE.getValue());
        crmOrderData.setObjectDescribeApiName("SalesOrderObj");
        crmOrderData.setObjectDescribeId(ConfigCenter.sales_order_describe_id);
        crmOrderData.setOrderTpye(crmOrderTypeMap.get(arg.getCrmOrderDetailInfo().getCrmOrderType()));
        crmOrderData.setIsTriggerWorkFlow("1");
        crmOrderData.setOrderTime(String.valueOf(System.currentTimeMillis()));
        crmOrderData.setOrderId(arg.getCrmOrderDetailInfo().getOrderId());
        crmOrderData.setManagerName(crmCustomerObjectData.getManagerName());
        crmOrderData.setManagerMobile(crmCustomerObjectData.getManagerMobile());
        crmOrderData.setManagerEmail(crmCustomerObjectData.getManagerEmail());
        crmOrderData.setEnterpriseAccount(crmCustomerObjectData.getEnterpriseAccount());
        crmOrderData.setPriceBookId(ConfigCenter.price_book_id);
        crmOrderData.setOwnerIds(Lists.newArrayList(String.valueOf(CrmConstants.SYSTEM_USER)));
        crmOrderData.setContractStamp(crmCustomerObjectData.getName());
        crmOrderData.setBuyNature(BUY_NATURE_API_NAME);
        crmOrderData.setContractMainBody(CONTRACT_MAINBODY_API_NAME);
        crmOrderObjectData.putAll(JSON.parseObject(JSON.toJSONString(crmOrderData), Map.class));

        Crm112OrderDetail crmOrderDetail = new Crm112OrderDetail();
        List<Crm112OrderProductObjectData> crmOrderProductObjects = Lists.newArrayList();

        Crm112OrderProductObjectData crmOrderProductObject = new Crm112OrderProductObjectData();
        crmOrderProductObject.setProductId(arg.getCrmOrderProductInfo().getProductId());
        crmOrderProductObject.setProductPrice("0.00");
        crmOrderProductObject.setPriceBookProductId(crmOrderProductModel.getPriceBookProductId());
        crmOrderProductObject.setUnit("个年");
        crmOrderProductObject.setQuantity(String.valueOf(arg.getCrmOrderProductInfo().getQuantity()));
        crmOrderProductObject.setAllResourceCount(String.valueOf(arg.getCrmOrderProductInfo().getAllResourceCount()));
        crmOrderProductObject.setBeginTime(arg.getCrmOrderProductInfo().getBeginTime());
        crmOrderProductObject.setEndTime(arg.getCrmOrderProductInfo().getEndTime());
        crmOrderProductObject.setOriginalPrice(StringUtils.defaultIfEmpty(arg.getCrmOrderProductInfo().getOrderAmount(), "0"));
        crmOrderProductObject.setOrderPrice(StringUtils.defaultIfEmpty(arg.getCrmOrderProductInfo().getOrderAmount(), "0"));
        crmOrderProductObject.setProductCode(arg.getCrmOrderProductInfo().getProductId());
//            crmOrderProductObject.setProductType(productDefine.getProductType());
        crmOrderProductObject.setCanTry("是");
        crmOrderProductObject.setRecordType(crmOrderProductModel.getRecordType());
        crmOrderProductObject.setDays("0");
        crmOrderProductObject.setObjectDescribeApiName("SalesOrderProductObj");
        crmOrderProductObject.setObjectDescribeId(ConfigCenter.sales_order_product_obj);
        crmOrderProductObjects.add(crmOrderProductObject);
        crmOrderDetail.setSalesOrderProductObjs(crmOrderProductObjects);
        Map<String, List<ObjectData>> crmCreateOrderObjectData = JSON.parseObject(JSON.toJSONString(crmOrderDetail), Map.class);

        ActionAddArg actionAddArg = new ActionAddArg();
        actionAddArg.setObjectData(crmOrderObjectData);
        actionAddArg.setDetails(crmCreateOrderObjectData);

        return actionAddArg;
    }

    public static ActionAddArg buildCrmOrderInfo(CreateCrmOrderArg arg, CrmOrderProductModel crmOrderProductModel, ObjectData customerData) {
        HashMap<String, String> crmOrderTypeMap = JSON.parseObject(ConfigCenter.crm_order_type_ids, new TypeToken<HashMap<String, String>>() {
        }.getType());
        if (!crmOrderTypeMap.containsKey(arg.getCrmOrderDetailInfo().getCrmOrderType())) {
            LogUtils.error("CrmManager.buildCrm112OrderInfo,crmOrderTypeMap.get(arg.getCrmOrderDetailInfo().getCrmOrderType()) is null,arg={}", arg);
            return null;
        }

        CrmCustomerObjectData crmCustomerObjectData = JSON.parseObject(JSON.toJSONString(customerData), CrmCustomerObjectData.class);

        ObjectData crmOrderObjectData = new ObjectData();

        CrmOrderObjectData crmOrderData = new CrmOrderObjectData();
        crmOrderData.setCustomerId(customerData.getId());
        crmOrderData.setRecordType(CrmOrderRecordTypeEnum.ONLINE.getValue());
        crmOrderData.setObjectDescribeApiName("SalesOrderObj");
        crmOrderData.setObjectDescribeId(ConfigCenter.sales_order_describe_id);
        crmOrderData.setOrderTpye(crmOrderTypeMap.get(arg.getCrmOrderDetailInfo().getCrmOrderType()));
        crmOrderData.setIsTriggerWorkFlow("1");
        crmOrderData.setOrderTime(String.valueOf(System.currentTimeMillis()));
        crmOrderData.setOrderId(arg.getCrmOrderDetailInfo().getOrderId());
        crmOrderData.setManagerName(crmCustomerObjectData.getManagerName());
        crmOrderData.setManagerMobile(crmCustomerObjectData.getManagerMobile());
        crmOrderData.setManagerEmail(crmCustomerObjectData.getManagerEmail());
        crmOrderData.setEnterpriseAccount(crmCustomerObjectData.getEnterpriseAccount());
        crmOrderData.setPriceBookId(ConfigCenter.price_book_id);
        crmOrderData.setOwnerIds(Lists.newArrayList(String.valueOf(CrmConstants.SYSTEM_USER)));
        crmOrderData.setContractStamp(crmCustomerObjectData.getName());
        crmOrderData.setBuyNature(BUY_NATURE_API_NAME);
        crmOrderData.setContractMainBody(CONTRACT_MAINBODY_API_NAME);
        crmOrderObjectData.putAll(JSON.parseObject(JSON.toJSONString(crmOrderData), Map.class));

        CrmOrderDetail crmOrderDetail = new CrmOrderDetail();
        List<CrmOrderProductObjectData> crmOrderProductObjects = Lists.newArrayList();

        CrmOrderProductObjectData crmOrderProductObject = new CrmOrderProductObjectData();
        crmOrderProductObject.setProductId(arg.getCrmOrderProductInfo().getProductId());
        crmOrderProductObject.setProductPrice("0.00");
        crmOrderProductObject.setPriceBookProductId(crmOrderProductModel.getPriceBookProductId());
        crmOrderProductObject.setUnit("个年");
        crmOrderProductObject.setQuantity(String.valueOf(arg.getCrmOrderProductInfo().getQuantity()));
        crmOrderProductObject.setAllResourceCount(String.valueOf(arg.getCrmOrderProductInfo().getAllResourceCount()));
        crmOrderProductObject.setBeginTime(arg.getCrmOrderProductInfo().getBeginTime());
        crmOrderProductObject.setEndTime(arg.getCrmOrderProductInfo().getEndTime());
        crmOrderProductObject.setOriginalPrice(StringUtils.defaultIfEmpty(arg.getCrmOrderProductInfo().getOrderAmount(), "0"));
        crmOrderProductObject.setOrderPrice(StringUtils.defaultIfEmpty(arg.getCrmOrderProductInfo().getOrderAmount(), "0"));
        crmOrderProductObject.setProductCode(arg.getCrmOrderProductInfo().getProductId());
//            crmOrderProductObject.setProductType(productDefine.getProductType());
        crmOrderProductObject.setCanTry("是");
        crmOrderProductObject.setRecordType(crmOrderProductModel.getRecordType());
        crmOrderProductObject.setDays("0");
        crmOrderProductObject.setObjectDescribeApiName("SalesOrderProductObj");
        crmOrderProductObject.setObjectDescribeId(ConfigCenter.sales_order_product_obj);
        crmOrderProductObjects.add(crmOrderProductObject);
        crmOrderDetail.setSalesOrderProductObjs(crmOrderProductObjects);
        Map<String, List<ObjectData>> crmCreateOrderObjectData = JSON.parseObject(JSON.toJSONString(crmOrderDetail), Map.class);

        ActionAddArg actionAddArg = new ActionAddArg();
        actionAddArg.setObjectData(crmOrderObjectData);
        actionAddArg.setDetails(crmCreateOrderObjectData);

        return actionAddArg;
    }
}
