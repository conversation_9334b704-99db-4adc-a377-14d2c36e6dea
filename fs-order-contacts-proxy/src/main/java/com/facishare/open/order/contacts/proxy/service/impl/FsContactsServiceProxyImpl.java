package com.facishare.open.order.contacts.proxy.service.impl;

import com.facishare.open.order.contacts.proxy.api.consts.GlobalValue;
import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.facishare.open.order.contacts.proxy.api.service.FsContactsServiceProxy;
import com.facishare.open.order.contacts.proxy.api.service.FsDepartmentServiceProxy;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service("fsContactsServiceProxy")
public class FsContactsServiceProxyImpl implements FsContactsServiceProxy {
    @Resource
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;
    @Resource
    private FsDepartmentServiceProxy fsDepartmentServiceProxy;

    @Override
    public Result<List<String>> batchStopFsEmp(int ei, String fsEa, String fsDepId,List<String> excludeUserIdList) {
        LogUtils.info("FsContactsServiceProxyImpl.batchStopFsEmp,ei={},fsEa={},fsDepId={},excludeUserIdList={}", ei,fsEa,fsDepId,excludeUserIdList);
        Result<List<ObjectData>> listResult = null;
        if(isParentDep(ei,fsDepId)) {
            //如果fsDepId是父部门，只需要获取直接在父部门下的员工，不包括子部门下的员工
            listResult = fsEmployeeServiceProxy.listByDepId(ei,fsDepId);
            LogUtils.info("FsContactsServiceProxyImpl.batchStopFsEmp,listByDepId,listResult={}", listResult);
        } else {
            listResult = fsEmployeeServiceProxy.listAll(ei,fsDepId);
            LogUtils.info("FsContactsServiceProxyImpl.batchStopFsEmp,listAll,listResult={}", listResult);
        }

        if(listResult.isSuccess()==false)
            return Result.newError(listResult.getCode(),listResult.getMsg());

        List<String> fsUserIdList = listResult.getData().stream()
                .map(ObjectData::getId).collect(Collectors.toList());
        LogUtils.info("FsContactsServiceProxyImpl.batchStopFsEmp,ei={},fsDepId={},fsUserIdList={}",ei,fsDepId,fsUserIdList);
        if(CollectionUtils.isNotEmpty(excludeUserIdList)) {
            fsUserIdList.removeAll(excludeUserIdList);
        }
        LogUtils.info("FsContactsServiceProxyImpl.batchStopFsEmp,fsUserIdList2={}", fsUserIdList);
        if(fsUserIdList.contains("1000")) {
            //纷享企业管理员不能停用
            fsUserIdList.remove("1000");
            //把管理员移到到全公司下
            fsEmployeeServiceProxy.bulkResetMainDepartment(ei+"", Lists.newArrayList("1000"),
                    GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"");
        }

        LogUtils.info("FsContactsServiceProxyImpl.batchStopFsEmp,fsUserIdList3={}", fsUserIdList);
        if(CollectionUtils.isEmpty(fsUserIdList))
            return Result.newSuccess(fsUserIdList);

        Result<Void> result = fsEmployeeServiceProxy.bulkStop(ei + "",
                fsUserIdList);
        LogUtils.info("FsContactsServiceProxyImpl.batchStopFsEmp,fsUserIdList={},result={}",fsUserIdList,result);

        if(result.isSuccess()) {
            return Result.newSuccess(fsUserIdList);
        }
        return Result.newError(result.getCode(),result.getMsg());
    }

    @Override
    public Result<List<String>> batchStopFsDep(int ei, String fsEa, String fsDepId) {
        LogUtils.info("FsContactsServiceProxyImpl.batchStopFsDep,ei={},fsEa={},fsDepId={}", ei,fsEa,fsDepId);
        if(StringUtils.equalsIgnoreCase(GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"",fsDepId)
                || StringUtils.equalsIgnoreCase(GlobalValue.UNALLOCATED_DEPARTMENT_ID+"",fsDepId)) {
            LogUtils.info("FsContactsServiceProxyImpl.batchStopFsDep,纷享全公司和未分配部门，不允许停用"); //ignorei18n
            return Result.newError(-1,"fs root dep cannot stop");
        }
        if(isParentDep(ei,fsDepId)) {
            LogUtils.info("FsContactsServiceProxyImpl.batchStopFsDep,isParentDep,cannot stop,fsDepId={}", fsDepId);
            return Result.newError(-1,"parent dep cannot stop");
        }
        Result<List<ObjectData>> listResult = fsDepartmentServiceProxy.list(ei, fsDepId);
        LogUtils.info("FsContactsServiceProxyImpl.batchStopFsDep,listResult={}", listResult);
        if(listResult.isSuccess()==false) return Result.newError(listResult.getCode(),listResult.getMsg());

        List<String> fsDepIdList = listResult.getData().stream()
                .map(ObjectData::getId).collect(Collectors.toList());
        //不能停用全公司和未分配部门
        fsDepIdList.remove(GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"");
        fsDepIdList.remove(GlobalValue.UNALLOCATED_DEPARTMENT_ID+"");

        LogUtils.info("FsContactsServiceProxyImpl.batchStopFsDep,fsDepIdList={}", fsDepIdList);
        //从子部门到根部门依次排序
        Collections.reverse(fsDepIdList);
        LogUtils.info("FsContactsServiceProxyImpl.batchStopFsDep,reserved,fsDepIdList={}", fsDepIdList);

        List<String> stoppedFsDepIdList = new ArrayList<>();
        for(String depId : fsDepIdList) {
            LogUtils.info("ContactsServiceImpl.batchStopFsDep,depId={}", depId);
            Result<Void> result = fsDepartmentServiceProxy.toggle(ei + "",
                    depId,false);
            LogUtils.info("ContactsServiceImpl.batchStopFsDep,stop dep,result={}", result);
            if(result.isSuccess()) {
                stoppedFsDepIdList.add(depId);
            }
        }
        return Result.newSuccess(stoppedFsDepIdList);
    }

    @Override
    public Result<List<String>> batchResumeFsDep(int ei, String fsEa, List<String> fsDepIdList) {
        LogUtils.info("FsContactsServiceProxyImpl.batchResumeFsDep,ei={},fsEa={},fsDepIdList={}", ei,fsEa,fsDepIdList);
        List<String> resumedDepIdList = new ArrayList<>();
        for(String depId : fsDepIdList) {
            LogUtils.info("ContactsServiceImpl.batchResumeFsDep,depId={}", depId);
            Result<Void> result = fsDepartmentServiceProxy.toggle(ei + "",
                    depId,true);
            LogUtils.info("ContactsServiceImpl.batchResumeFsDep,result={}", result);
            if(result.isSuccess()) {
                resumedDepIdList.add(depId);
            }
        }
        return Result.newSuccess(resumedDepIdList);
    }

    private boolean isParentDep(int ei,String fsDepId) {
        Result<List<ObjectData>> list = fsDepartmentServiceProxy.list(ei, GlobalValue.ALL_COMPANY_DEPARTMENT_ID + "");
        for(ObjectData dep : list.getData()) {
            String dept_parent_path = dep.getString("dept_parent_path");
            if(StringUtils.containsIgnoreCase(dept_parent_path,fsDepId)) {
                return true;
            }
        }
        return false;
    }
}
