<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p" xmlns="http://www.springframework.org/schema/beans"
    xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

  <import resource="classpath*:spring/ei-ea-converter.xml"/>
  <import resource="classpath:crmrest/crmrest.xml"/>
  <import resource="classpath:paasauthrest/paasauthrest.xml"/>
  <import resource="classpath:otherrest/otherrest.xml"/>
  <import resource="classpath:enterpriserelation2/enterpriserelation.xml"/>

  <import resource="classpath:fs-plat-privilege-api-rest-client.xml"/>
  <import resource="classpath:META-INF/fs-spring-dubbo-plugin.xml"/>

  <!--fs-orgainzation-adapter-api -->
  <import resource="classpath:spring/fs-organization-adapter-api-dubbo-rest-client.xml"/>
  <!--默认fs-organization-adapter-provider服务地址，支持client方指定provider指定地址-->
  <import resource="classpath:spring/fs-organization-adapter-api-dubbo-rest-host-config.xml"/>

<!--  <import resource="classpath:fs-organization-cache-no-dubbo.xml"/>-->
  <!--fs-orgainzation-api -->
  <import resource="classpath:spring/fs-organization-api-dubbo-rest-client.xml"/>
  <!--默认fs-organization-provider服务地址，支持client方指定provider指定地址-->
  <import resource="classpath:spring/fs-organization-api-dubbo-rest-client-host-config.xml"/>

  <!-- 引入license-->
  <import resource="classpath:spring/license-client.xml"/>

  <!-- 纷享内部调用组织架构服务 非元数据团队建议使用该配置-->
  <import resource="classpath:spring/fs-organization-api-rest-client.xml"/>

  <import resource="classpath:fs-paas-dao-support.xml"/>


</beans>