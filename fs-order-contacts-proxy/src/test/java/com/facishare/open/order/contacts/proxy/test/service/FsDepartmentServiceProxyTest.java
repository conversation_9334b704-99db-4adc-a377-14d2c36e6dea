package com.facishare.open.order.contacts.proxy.test.service;

import com.facishare.converter.EIEAConverter;
import com.facishare.open.order.contacts.proxy.test.BaseTest;
import com.facishare.open.order.contacts.proxy.api.arg.FsDeptArg;
import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.facishare.open.order.contacts.proxy.api.service.FsDepartmentServiceProxy;
import com.facishare.open.order.contacts.proxy.api.consts.GlobalValue;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.Lists;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

public class FsDepartmentServiceProxyTest extends BaseTest {
    @Resource
    private FsDepartmentServiceProxy fsDepartmentServiceProxy;
    @Resource
    private EIEAConverter eieaConverter;

    @Test
    public void create() {
        FsDeptArg arg = new FsDeptArg();
        arg.setEi("85229");
        arg.setName("销售子公司#$%#@");
        //arg.setCode("abc5");
        arg.setParentId(Lists.newArrayList(GlobalValue.ALL_COMPANY_DEPARTMENT_ID+""));
        arg.setManagerId(Lists.newArrayList("1000"));

        Result<ObjectData> result = fsDepartmentServiceProxy.create(arg);
        System.out.println(result);
    }

    @Test
    public void update() {
        FsDeptArg arg = new FsDeptArg();
        arg.setId("1098");
        arg.setEi("85229");
        arg.setName("销售子公司#$%#@");
        arg.setStatus("1");

        Result<Void> result = fsDepartmentServiceProxy.update(arg);
        System.out.println(result);
    }

    @Test
    public void bulkStop() {
        Result<Void> result = fsDepartmentServiceProxy.bulkStop("85268",Lists.newArrayList("1000","1001"));
        System.out.println(result);
        bulkResume();
    }

    @Test
    public void bulkResume() {
        Result<Void> result = fsDepartmentServiceProxy.bulkResume("85268",Lists.newArrayList("1000","1001"));
        System.out.println(result);
    }

    @Test
    public void toggle() {
        Result<Void> result = fsDepartmentServiceProxy.toggle("85268","1000",true);
        System.out.println(result);
    }

    @Test
    public void list() {
        Result<List<ObjectData>> result = fsDepartmentServiceProxy.list(88102,GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"");
        System.out.println(result);
    }

    @Test
    public void detail() {
        Result<ObjectData> result = fsDepartmentServiceProxy.detail(88142,"1005");
        System.out.println(result);
    }

    @Test
    public void getAllDepartmentDto() {
        Result<List<DepartmentDto>> result = fsDepartmentServiceProxy.getAllDepartmentDto(88142);
        System.out.println(result);
    }
    @Test
    public void getLowDepartmentsDto() {
        Result<List<DepartmentDto>> result = fsDepartmentServiceProxy.getChildrenDepartment(eieaConverter.enterpriseAccountToId("djt6721"), 1000);
        System.out.println(result);
    }

    @Test
    public void getDepartmentDto() {
        Result<DepartmentDto> result = fsDepartmentServiceProxy.getDepartmentDto(88142, 1000);
        System.out.println(result);
    }

}

