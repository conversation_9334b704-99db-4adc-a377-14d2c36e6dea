package com.facishare.open.ding.cloud.manager;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.*;
import com.dingtalk.api.DefaultDingTalkClient;
import com.facishare.open.ding.api.arg.SendTextNoticeArg;
import com.facishare.open.ding.api.enums.CloudDataTypeEnum;
import com.facishare.open.ding.api.enums.DingObjTypeEnum;
import com.facishare.open.ding.api.enums.SourceTypeEnum;
import com.facishare.open.ding.api.model.*;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.result.DingOutUserResult;
import com.facishare.open.ding.api.model.AdminsUser;
import com.facishare.open.ding.api.model.AppParams;
import com.facishare.open.ding.api.model.AuthEnterPriseModel;
import com.facishare.open.ding.api.model.DingUserModel;
import com.facishare.open.ding.api.result.*;
import com.facishare.open.ding.api.service.*;
import com.facishare.open.ding.api.service.cloud.CloudNotificationService;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.api.vo.DingObjSyncVo;
import com.facishare.open.ding.cloud.arg.*;
import com.facishare.open.ding.api.vo.DingTaskVo;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.cloud.constants.Constant;
import com.facishare.open.ding.cloud.utils.DateUtils;
import com.facishare.open.ding.cloud.utils.OkHttp3MonitorUtils4Cloud;
import com.facishare.open.ding.cloud.utils.SignatureUtils;
import com.facishare.open.ding.cloud.utils.XorUtils;
import com.facishare.open.ding.common.model.*;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.result.CreateTodoResult;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.Gson;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import com.jayway.jsonpath.spi.json.JacksonJsonProvider;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.open.ding.common.result.ResultCode.PARAMS_ERROR;

/**
 * <AUTHOR>
 * @Date 2021/4/29 16:45
 * @Version 1.0
 */
@Service
@Slf4j
public class DingManager {

    public static int DING_SUCCESS = 0;
    //时间戳过期
    public static int DING_TIME_OUT = 853002;
    //ticket失效
    public static int TICKET_INVALID = 853005;
    @Autowired
    private SyncBizDataService syncBizDataService;
    @Autowired
    private HttpCloudManager httpCloudManager;
    @Autowired
    private RedisManager redisManager;
    @Autowired
    private DingCorpMappingService dingCorpMappingService;
    @Autowired
    private ObjectMappingService objectMappingService;

    @Autowired
    private DingCorpMappingService corpMappingService;
    @Autowired
    private AppAuthService appAuthService;
    @Autowired
    private DingObjSyncService dingObjSyncService;
    @Autowired
    private DataConvertManager dataConvertManager;
    @Autowired
    private CloudNotificationService cloudNotificationService;
    @Autowired
    private OAConnectorDataManager oaConnectorDataManager;

    private static String getTokenUrl = "https://oapi.dingtalk.com/service/get_corp_token";
    private static String getAuthInfoUrl = "https://oapi.dingtalk.com/auth/scopes";
    private static String DINGTALK_TODO = "https://api.dingtalk.com/v1.0/todo/users/";
    @ReloadableProperty("ding.mid.authorize.url")
    private String MID_URL;//= "https://www.ceshi112.com/dingtalk/cloud/skipMessage";

    //客户端
    private static final String DING_SINGLE_URL = "https://oapi.dingtalk.com/connect/oauth2/sns_authorize?response_type=code&scope=snsapi_auth&state=STATE";
    private static final String DING_PC_SLIDE_URL = "dingtalk://dingtalkclient/page/link?url=%s&pc_slide=true";

    public static Configuration CONFIGURATION = Configuration.defaultConfiguration()
            .addOptions(Option.SUPPRESS_EXCEPTIONS, Option.DEFAULT_PATH_LEAF_TO_NULL)
            .jsonProvider(new JacksonJsonProvider());


    public static DefaultDingTalkClient getClient(String proxyUrl) {
        DefaultDingTalkClient client = new DefaultDingTalkClient(proxyUrl);
        InetSocketAddress inetSocketAddress = new InetSocketAddress(ConfigCenter.PROXY_PORT.get(0), Integer.parseInt(ConfigCenter.PROXY_PORT.get(1)));
        Proxy proxy = new Proxy(Proxy.Type.HTTP, inetSocketAddress);
        client.setProxy(proxy);
        return client;
    }


    //获取accessToken
    public Result<String> getAccessToken(String authCorpId, String suiteId) {
        log.info("DingManager.getAccessToken,authCorpId={},suiteId={}",authCorpId,suiteId);

        String cacheKey = String.format(Constant.DING_CORP_SUITE_TOKEN, authCorpId, suiteId);
        log.info("DingManager.getAccessToken,cacheKey={}",cacheKey);
        Result<String> infoFromRedis = redisManager.getInfoFromRedis(cacheKey);
        log.info("DingManager.getAccessToken,cacheKey={},infoFromRedis={}",cacheKey,infoFromRedis);
        if (ObjectUtils.isNotEmpty(infoFromRedis.getData())) {
            log.info("getaccess token authCorpId:{},data:{}", authCorpId, XorUtils.EncodeByXor(infoFromRedis.getData(), ConfigCenter.XOR_SECRET_KEY));
            return Result.newSuccess(infoFromRedis.getData());
        }
        AppParams appParams = ConfigCenter.APP_PARAMS_MAP.get(suiteId);
        log.info("DingManager.getAccessToken,appParams={}",appParams);
        //安全性扫描，suiteId可能为非法参数
        if(ObjectUtils.isEmpty(appParams)) {
            log.warn("DingManager.getUserByCode,query getAccessToken fail,authCorpId={}.", authCorpId);
            return Result.newError(PARAMS_ERROR);
        }
        String suiteTicket = getSuiteTicket(appParams.getSuiteId());
        log.info("DingManager.getAccessToken,suiteTicket={}",suiteTicket);
        //suiteTicket = ConfigCenter.SUITE_TICKET;
//        suiteTicket = "MyCO2BugZSSBI70KsQ20XosdhR5po8WewKQQbEnetwtxxrlO2x93J8vKN36SDlfjQpVIAz6wObhYVUcbL6wrAX";
//        ConfigCenter.SUITE_KEY = "suitem6hlmpeanmif11kd";
//        ConfigCenter.SUITE_SECRET = "7Q9avRvhdosL--u7JBk-poatrYqH2DKKLQKiS47KCAUQTfkNpl6IMtiplxhghDTu";
        String suiteKey = appParams.getSuiteKey();
        String suiteSecret = appParams.getSuiteSecret();
        Long timeStamp = new Date().getTime();
        Map<String, Object> argMap = Maps.newHashMap();
        String signature = SignatureUtils.signatureValue(timeStamp, suiteTicket, suiteSecret);
        String encodeValue = SignatureUtils.urlEncode(signature, "utf-8");
        String url = "https://oapi.dingtalk.com/service/get_corp_token?signature=" + encodeValue + "&timestamp=" + timeStamp + "&accessKey=" + suiteKey + "&suiteTicket=" + suiteTicket;
        argMap.put("auth_corpid", authCorpId);
        Map<String, Object> refreshUrlResult = httpCloudManager.refreshUrl(url, argMap, createHeader());
        log.info("DingManager.getAccessToken,refreshUrlResult={}", refreshUrlResult);
        String result = refreshUrlResult.get("body").toString();
        log.info("DingManager.getAccessToken,result={}", result);
        Integer errCode = JsonPath.read(result, "$.errcode");
        if (errCode == DING_TIME_OUT) {
            log.info("container request time out refresh");
            Long refreshTime = DateUtils.timeStamp(refreshUrlResult.get("time").toString());
            signature = SignatureUtils.signatureValue(refreshTime, suiteTicket, suiteSecret);
            encodeValue = SignatureUtils.urlEncode(signature, "utf-8");
            url = "https://oapi.dingtalk.com/service/get_corp_token?signature=" + encodeValue + "&timestamp=" + refreshTime + "&accessKey=" + suiteKey + "&suiteTicket=" + suiteTicket;
            refreshUrlResult = httpCloudManager.refreshUrl(url, argMap, createHeader());
            log.info("DingManager.getAccessToken,refreshUrlResult.2={}", refreshUrlResult);
            result = refreshUrlResult.get("body").toString();
            log.info("DingManager.getAccessToken,result.2={}", result);
        } else if(errCode == TICKET_INVALID) {
            log.info("DingManager.getAccessToken.ticket is invalid");
            SendTextNoticeArg arg = new SendTextNoticeArg();
            arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
            List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
            arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
            arg.setMsgTitle("钉钉suite_ticket缓存失效告警");
            String msg = String.format("钉钉suite_ticket缓存失效，请及时关注\n企业账号=%s，账套=%s\n返回结果=%s",authCorpId, suiteId, result);
            arg.setMsg(msg);
            log.info("DingManager.getAccessToken,arg={}", arg);
            cloudNotificationService.sendDingCloudNotice(arg);
        }
        Map<String, String> tokenMap = JSONObject.parseObject(result, new TypeReference<Map<String, String>>() {
        });
        if (tokenMap.get("errcode").equals("0")) {
            //存入redis
            String accessToken = JsonPath.read(result, "$.access_token");
            redisManager.saveAccessToken(cacheKey, accessToken);
            log.info("DingManager.getAccessToken,saveAccessToken,cacheKey={},accessToken={}",cacheKey,accessToken);
            //上报
            oaConnectorDataManager.send(null, null, SourceTypeEnum.DING_CLOUD.getSourceType(),
                    CloudDataTypeEnum.SUITE_TICKET.getDataType(), suiteTicket, accessToken, null, null);
            return Result.newSuccess(accessToken);
        }
        log.info("get accesstoken corpId:{},msg:{}", authCorpId, XorUtils.EncodeByXor(result, ConfigCenter.XOR_SECRET_KEY));
        String resultCode = tokenMap.get("errcode");
        String resultMsg = tokenMap.get("errmsg");
        //上报
        oaConnectorDataManager.send(null, null, SourceTypeEnum.DING_CLOUD.getSourceType(),
                CloudDataTypeEnum.SUITE_TICKET.getDataType(), suiteTicket, null, resultCode, resultMsg);
        return Result.newError(Integer.valueOf(resultCode), resultMsg);
    }


    public static Map<String, String> createHeader() {
        Map<String, String> headers = Maps.newHashMapWithExpectedSize(1);
        headers.put("Content-Type", "application/json");
        return headers;
    }
    //获取suiteTicket

    public String getSuiteTicket(String suiteId) {
        log.info("DingManager.getSuiteTicket,suiteId={}",suiteId);
        Result<String> ticketResult = syncBizDataService.queryByTicket(suiteId);
        log.info("DingManager.getSuiteTicket,suiteId={},ticketResult={}",suiteId,ticketResult);
        if (ObjectUtils.isEmpty(ticketResult.getData())) {
            log.warn("DingManager.getSuiteTicket,query ticket fail");
            return null;
        }
        log.info("DingManager.getSuiteTicket,suiteTicket={}",ticketResult.getData());
        return ticketResult.getData();
    }

    //获取授权范围

    public ScopeVo getAuthInfo(String dingCorpId, String suiteId) {
        Result<String> cacheToken = getAccessToken(dingCorpId, suiteId);
        String url = "https://oapi.dingtalk.com/auth/scopes?access_token=" + cacheToken.getData();
        String result = httpCloudManager.getUrl(url, createHeader());
        Integer errCode = JsonPath.read(result, "$.errcode");
        if (Objects.isNull(errCode) || errCode != DING_SUCCESS) {
            log.warn("query score fail.");
            return null;
        }
        if (errCode == DING_SUCCESS) {
            String authJSon = JSONPath.read(result, "$.auth_org_scopes").toString();
            List<Long> deptList = JSON.parseArray(JSONPath.read(authJSon, "$.authed_dept").toString(), Long.class);
            List<String> userList = JSON.parseArray(JSONPath.read(authJSon, "$.authed_user").toString(), String.class);
            return new ScopeVo(deptList, userList);
        } else {
            log.warn("get auth info failed corpId:{},message:{}", dingCorpId, result);
        }
        return new ScopeVo();
    }

    //根据免登code返回对应的用户信息
    public Result<DingOutUserResult> getUserByCode(String code, String appId, String corpId) {
        AppParams appParams = ConfigCenter.APP_PARAMS_MAP_APP.get(appId);
        log.info("getUserByCode APP_PARAMS_MAP:{},:params{}", ConfigCenter.APP_PARAMS_MAP, appParams);
        DingOutUserResult outUserResult = new DingOutUserResult();
        String suiteId = appParams.getSuiteId();
        String accessToken = getAccessToken(corpId, suiteId).getData();
        String url = "https://oapi.dingtalk.com/user/getuserinfo?access_token=" + accessToken + "&code=" + code;
        Map<String, Object> argMap = Maps.newHashMap();
        argMap.put("code", code);
        String result = httpCloudManager.getUrl(url, createHeader());
        log.info("result:{}", result);
        Map<String, String> userResult = JSONObject.parseObject(result, new TypeReference<Map<String, String>>() {
        });
        Integer errcode = Integer.parseInt(userResult.get("errcode"));
        String errmsg = userResult.get("errmsg");
        if (DING_SUCCESS != errcode) {
            return Result.newError(errcode, errmsg);
        }
        String dingUserId = JSONPath.read(result, "$.userid").toString();
        String userName = JSONPath.read(result, "$.name").toString();
        //查询租户的信息
        Result<List<DingCorpMappingVo>> corpResult = dingCorpMappingService.queryCorpMappingByCorpId(corpId);
        log.info("ding manager query corp:{}", corpResult);
        if (ObjectUtils.isEmpty(corpResult.getData())) {
            return Result.newError(ResultCode.ENTERPRISE_NOT_BIND);
        }
        Integer ei = corpResult.getData().get(0).getEi();
        Result<DingMappingEmployeeResult> empResult = objectMappingService.queryEmpByDingUserId(ei, dingUserId);
        outUserResult.setDingCorpId(corpId);
        outUserResult.setEmployeeName(userName);
        outUserResult.setDingEmployeeId(dingUserId);
        outUserResult.setEi(ei);
        log.info("ding manager query empResult:{}", empResult);
        if (ObjectUtils.isEmpty(empResult.getData())) {
            return Result.newSuccess(outUserResult);
        }
        outUserResult.setFsEmployeeId(empResult.getData().getEmployeeId());
        return Result.newSuccess(outUserResult);
    }


    //获取部门列表信息
    public List<Dept> getListDeptByScope(List<Long> scopeDeptList, String dingCorpId, String suiteId) {
        //组装层次遍历的部门
        log.info("scope deptList:{},corpId:{}", scopeDeptList, dingCorpId);
        if (CollectionUtils.isEmpty(scopeDeptList)) {
            return null;
        }
        List<Dept> allDepts = Lists.newArrayList();
        scopeDeptList.forEach(item -> {
            Dept dept = queryDeptDetail(dingCorpId, item, suiteId);
            allDepts.add(dept);
        });
        List<Long> childList = Lists.newArrayList();
        for (Long parentId : scopeDeptList) {
            List<Dept> depts = queryDingDept(dingCorpId, Lists.newArrayList(parentId), suiteId);
            allDepts.addAll(depts);
            log.info("list scope depts:{}", depts);
            depts.stream().forEach(item -> childList.add(item.getId()));
            while (CollectionUtils.isNotEmpty(childList)) {
                depts = queryDingDept(dingCorpId, childList, suiteId);
                allDepts.addAll(depts);
                childList.clear();
                depts.stream().forEach(item -> childList.add(item.getId()));
            }
        }
        //处理部门同名的情况
        removeDupName(allDepts);
        return allDepts;
    }

    //获取授权范围下的所有子级部门
    public List<Dept> querySimpleDeptByScope(List<Long> scopeDeptList, String dingCorpId, String suiteId) {
        //组装层次遍历的部门
        if (CollectionUtils.isEmpty(scopeDeptList)) {
            return null;
        }
        List<Dept> allDepts = Lists.newArrayList();
        scopeDeptList.forEach(item -> {
            Dept dept = queryDeptDetail(dingCorpId, item, suiteId);
            allDepts.add(dept);
        });
        List<Long> childList = Lists.newArrayList();
        for (Long parentId : scopeDeptList) {
            List<Dept> depts = queryDingDept(dingCorpId, Lists.newArrayList(parentId), suiteId);
            allDepts.addAll(depts);
            depts.stream().forEach(item -> childList.add(item.getId()));
            while (CollectionUtils.isNotEmpty(childList)) {
                depts = queryDingDept(dingCorpId, childList, suiteId);
                allDepts.addAll(depts);
                childList.clear();
                depts.stream().forEach(item -> childList.add(item.getId()));
            }
        }
        //处理部门同名的情况
        removeDupName(allDepts);
        return allDepts;
    }


    public static void removeDupName(List<Dept> deptList) {
        if (CollectionUtils.isEmpty(deptList)) return;
        Set<String> deptNameSet = Sets.newHashSet();
        int postfix = 0;
        for (Dept dept : deptList) {
            if (deptNameSet.contains(dept.getName())) {
                dept.setName(dept.getName() + "__" + postfix++);
            }
            ;
            deptNameSet.add(dept.getName());
        }
    }

    //获取人员信息
    public List<EmployeeDingVo> getDingEmp(String dingCorpId, List<String> dingEmpIds, String suiteId) {
        String cacheToken = getAccessToken(dingCorpId, suiteId).getData();
        String url = "https://oapi.dingtalk.com/topapi/v2/user/get?access_token=" + cacheToken;

        List<EmployeeDingVo> employeeDingVoList = new ArrayList<>();
        for (String dingEmpId : dingEmpIds) {
            Map<String, Object> argMap = Maps.newHashMap();
            argMap.put("userid", dingEmpId);
            argMap.put("language", "zh_CN");
            String result = httpCloudManager.postUrl(url, argMap, createHeader());
            Integer errCode = JsonPath.read(result, "$.errcode");
            if (errCode.equals(DING_SUCCESS)) {
                EmployeeDingVo employeeDingVo = convertEmp(result);
                employeeDingVoList.add(employeeDingVo);
            }
        }
        return employeeDingVoList;
    }

    //获取主管理员
    public List<String> getListAdmins(String dingCorpId, String suiteId) {
        String cacheToken = getAccessToken(dingCorpId, suiteId).getData();
        String url = "https://oapi.dingtalk.com/topapi/user/listadmin?access_token=" + cacheToken;
        Map<String, Object> argMap = Maps.newHashMap();
        String result = httpCloudManager.postUrl(url, argMap, createHeader());
        Integer errCode = JsonPath.read(result, "$.errcode");
        if (errCode.equals(DING_SUCCESS)) {
            JSONObject jsonObject = JSONObject.parseObject(result);
            Object object = jsonObject.get("result");
            List<AdminsUser> adminsUsers = JSONArray.parseArray(object.toString(), AdminsUser.class);
            List<String> userList = adminsUsers.stream().filter(item -> item.getSys_level().equals(1)).map(AdminsUser::getUserid).collect(Collectors.toList());
            return userList;
        }
        return Lists.newArrayList();
    }


    /**
     * 通过unionId获取员工信息，包括手机号码（新版API)
     */

    public Result<EmployeeDingVo> queryUserInfo(String authCode, String suiteId) {
        String url = "https://api.dingtalk.com/v1.0/oauth2/userAccessToken";
        AppParams appParams = ConfigCenter.APP_PARAMS_MAP.get(suiteId);
        log.info("app params:{}", appParams);
        if (ObjectUtils.isEmpty(appParams))
            return null;
        Map<String, Object> argMap = Maps.newHashMap();
        argMap.put("clientId", appParams.getSuiteKey());
        argMap.put("clientSecret", appParams.getSuiteSecret());
        argMap.put("code", authCode);
        argMap.put("grantType", "authorization_code");
        String result = httpCloudManager.postUrl(url, argMap, createHeader());
        Map<String, String> authorizeResult = JSONObject.parseObject(result, new TypeReference<Map<String, String>>() {
        });
        String accessToken = authorizeResult.get("accessToken");
        if (ObjectUtils.isEmpty(accessToken)) {
            String message = authorizeResult.get("message");
            log.info("access querty info:{}", result);
            return Result.newError(ResultCode.GET_PERSON_AUTH_FAIL.getErrorCode(), message);
        }
        log.info("get user info token:{}", accessToken);
        Result<EmployeeDingVo> userByMe = getUserByMe(accessToken);

        return userByMe;
    }

    public Result<EmployeeDingVo> getUserByMe(String accessToken) {
        String getMeUrl = "https://api.dingtalk.com/v1.0/contact/users/me";
        Map<String, String> hearsMap = Maps.newHashMap();
        hearsMap.put("x-acs-dingtalk-access-token", accessToken);
        String personUserInfo = httpCloudManager.getUrl(getMeUrl, hearsMap);
        Map<String, String> personMap = JSONObject.parseObject(personUserInfo, new TypeReference<Map<String, String>>() {
        });
        String openId = personMap.get("openId");

        if (ObjectUtils.isEmpty(openId)) {
            log.info("access getUserByMe info:{}", personUserInfo);
            String message = personMap.get("message");
            return Result.newError(ResultCode.GET_PERSON_AUTH_FAIL.getErrorCode(), message);
        }
        EmployeeDingVo employeeDingVo = new EmployeeDingVo();
        employeeDingVo.setMobile(personMap.get("mobile"));
        employeeDingVo.setName(personMap.get("nick"));
        employeeDingVo.setUnionid(personMap.get("unionId"));
        employeeDingVo.setUserid(personMap.get("openId"));
        employeeDingVo.setAvatarUrl(personMap.get("avatarUrl"));
        log.info("person userInfo:{}", personUserInfo);
        return Result.newSuccess(employeeDingVo);
    }


    private EmployeeDingVo convertEmp(String data) {
        EmployeeDingVo emp = new EmployeeDingVo();
        DocumentContext parse = JsonPath.parse(data, CONFIGURATION);
        Optional.ofNullable(parse.read("$.result.title")).ifPresent(item -> {
            emp.setTitle(item.toString());
        });
        String unionId = JsonPath.read(data, "$.result.unionid").toString();
        Boolean admin = JsonPath.read(data, "$.result.admin");
        String userid = JsonPath.read(data, "$.result.userid").toString();
        String name = JsonPath.read(data, "$.result.name").toString();
        List<Long> deptIds = JSONObject.parseArray(JsonPath.read(data, "$.result.dept_id_list").toString(), Long.class);
        emp.setDepartment(deptIds);
        emp.setName(name);
        emp.setUnionid(unionId);
        emp.setAdmin(admin);
        emp.setUserid(userid);
        Long mainId = deptIds.size() == 0 ? 1L : deptIds.get(0);
        emp.setMainDept(mainId);
        return emp;

    }

    //根据unionId获取userid
    public String getUserIdByUnionId(String corpId, String unionId, String suiteId) {
        String cacheToken = getAccessToken(corpId, suiteId).getData();
        String url = "https://oapi.dingtalk.com/topapi/user/getbyunionid?access_token=" + cacheToken;
        Map<String, Object> argMap = Maps.newHashMap();
        argMap.put("unionid", unionId);
        String result = httpCloudManager.postUrl(url, argMap, createHeader());
        Integer errCode = JsonPath.read(result, "$.errcode");
        if (errCode.equals(DING_SUCCESS)) {
            String userId = JsonPath.read(result, "$.result.userid");
            return userId;
        }
        return null;
    }

    //根据userId获取员工信息
    public EmployeeDingVo getUserByUserId(String corpId, String userId, String suiteId) {
        String cacheToken = getAccessToken(corpId, suiteId).getData();
        String url = "https://oapi.dingtalk.com/topapi/v2/user/get?access_token=" + cacheToken;
        Map<String, Object> argMap = Maps.newHashMap();
        argMap.put("userid", userId);
        String result = httpCloudManager.postUrl(url, argMap, createHeader());
        Integer errCode = JsonPath.read(result, "$.errcode");
        if (errCode.equals(DING_SUCCESS)) {
            EmployeeDingVo employeeDingVo = convertEmp(result);
            return employeeDingVo;
        }
        return null;
    }

    //返回状态
    public Result<EmployeeDingVo> getUserDetailByUserId(String corpId, String userId, String suiteId) {
        String cacheToken = getAccessToken(corpId, suiteId).getData();
        String url = "https://oapi.dingtalk.com/topapi/v2/user/get?access_token=" + cacheToken;
        Map<String, Object> argMap = Maps.newHashMap();
        argMap.put("userid", userId);
        String result = httpCloudManager.postUrl(url, argMap, createHeader());
        Integer errCode = JsonPath.read(result, "$.errcode");
        if (errCode.equals(DING_SUCCESS)) {
            EmployeeDingVo employeeDingVo = convertEmp(result);
            return Result.newSuccess(employeeDingVo);
        } else if (errCode.equals(50002)) {
            return Result.newError(ResultCode.NO_USER_AUTHORITY);
        }
        return Result.newError(ResultCode.GET_DING_EMP_FAILED);
    }

    //获取第三方小程序免登授权码
    public Result<EmployeeDingVo> getPersonalTmpCode(String appId, String tmpAuthCode) {
        AppParams appParams = ConfigCenter.APP_PARAMS_MAP.get(appId);//suitesecret当作appSecret
        long timeStamp = new Date().getTime();
        String signature = SignatureUtils.signatureValue(timeStamp, appParams.getSuiteSecret());
        String authUrl = "https://oapi.dingtalk.com/sns/getuserinfo_bycode?accessKey=".concat(appId).concat("&timestamp=").concat(String.valueOf(timeStamp)).
                concat("&signature=").concat(signature);
        Map<String, String> authCode = Maps.newHashMap();
        authCode.put("tmp_auth_code", tmpAuthCode);
        String result = httpCloudManager.postUrl(authUrl, authCode, Maps.newHashMap());
        Map<String, Object> resultMap = JSONObject.parseObject(result, Map.class);
        Integer errcode = Integer.valueOf(resultMap.get("errcode").toString());
        if (errcode != DING_SUCCESS) {
            return Result.newError(ResultCode.SERVER_BUSY.getErrorCode(), resultMap.get("errmsg").toString());
        }
        EmployeeDingVo employeeDingVo = JSONObject.parseObject((String) resultMap.get("user_info"), new TypeReference<EmployeeDingVo>() {
        });
        return Result.newSuccess(employeeDingVo);
    }


    /**
     * 获取授权企业的信息
     */
    public AuthEnterPriseModel.BizData getEnterPriseData(String corpId, String suiteId) {

        AppParams appParams = ConfigCenter.APP_PARAMS_MAP.get(suiteId);

        Map<String, Object> map = Maps.newHashMap();
        map.put("suite_key", appParams.getSuiteKey());
        map.put("auth_corpid", corpId);
        Long timeStamp = new Date().getTime();
        String suiteSecret = appParams.getSuiteSecret();
        String suiteTicket = getSuiteTicket(suiteId);
        String signature = SignatureUtils.signatureValue(timeStamp, suiteTicket, suiteSecret);
        String encodeValue = SignatureUtils.urlEncode(signature, "utf-8");
        String url = "https://oapi.dingtalk.com/service/get_auth_info?accessKey=" + appParams.getSuiteKey() + "&timestamp=" + timeStamp + "&suiteTicket=" + suiteTicket + "&signature=" + encodeValue;
        Map<String, Object> refreshUrlResult = httpCloudManager.refreshUrl(url, map, createHeader());
        log.info("refreshUrlResult:{}", refreshUrlResult);
        String result = refreshUrlResult.get("body").toString();
        Integer errCode = JsonPath.read(result, "$.errcode");
        if (errCode == DING_TIME_OUT) {
            log.info("container request time out refresh");
            Long refreshTime = DateUtils.timeStamp(refreshUrlResult.get("time").toString());
            signature = SignatureUtils.signatureValue(refreshTime, suiteTicket, suiteSecret);
            encodeValue = SignatureUtils.urlEncode(signature, "utf-8");
            url = "https://oapi.dingtalk.com/service/get_auth_info?accessKey=" + appParams.getSuiteKey() + "&timestamp=" + refreshTime + "&suiteTicket=" + suiteTicket + "&signature=" + encodeValue;
            refreshUrlResult = httpCloudManager.refreshUrl(url, map, createHeader());
            result = refreshUrlResult.get("body").toString();
            errCode = JsonPath.read(result, "$.errcode");
        }

        if (Objects.isNull(errCode) || errCode != DING_SUCCESS) {
            log.warn("getEnterPriseData faile.");
            return null;
        }
        AuthEnterPriseModel.BizData authEnterPriseModel = JSONObject.parseObject(result, new TypeReference<AuthEnterPriseModel.BizData>() {
        });
        return authEnterPriseModel;
    }

    /**
     * 获取企业的员工数量
     */
    public Integer getEmpCount(String dingCorpId, String suiteId) {
        String cacheToken = getAccessToken(dingCorpId, suiteId).getData();
        String url = "https://oapi.dingtalk.com/topapi/user/count?access_token=" + cacheToken;
        Map<String, Object> argMap = Maps.newHashMap();
        argMap.put("only_active", false);
        String result = httpCloudManager.postUrl(url, argMap, createHeader());
        Integer errCode = JsonPath.read(result, "$.errcode");
        if (errCode.equals(DING_SUCCESS)) {
            return JsonPath.read(result, "$.result.count");
        }
        return 10;
    }


    /**
     * 获取通讯录详情列表
     */
    public List<Dept> queryDingDept(String dingCorpId, List<Long> sameLevelIds, String suiteId) {
        List<Dept> deptList = Lists.newArrayList();
        for (Long sameLevelId : sameLevelIds) {
            //获取下一级子部门
            String cacheToken = getAccessToken(dingCorpId, suiteId).getData();
            String url = "https://oapi.dingtalk.com/topapi/v2/department/listsubid?access_token=" + cacheToken;
            Map<String, Object> argMap = Maps.newHashMap();
            argMap.put("dept_id", sameLevelId);
            String result = httpCloudManager.postUrl(url, argMap, createHeader());
            Integer errCode = JsonPath.read(result, "$.errcode");
            if (errCode.equals(DING_SUCCESS)) {
                String array = JSONPath.read(result, "$.result.dept_id_list").toString();
                List<Long> deptIds = JSONArray.parseArray(array, Long.class);
                for (Long deptId : deptIds) {
                    Dept dept = queryDeptDetail(dingCorpId, deptId, suiteId);
                    if (ObjectUtils.isEmpty(dept)) continue;
                    deptList.add(dept);
                }

            }
        }
        return deptList;
    }

    public Dept queryDeptDetail(String corpId, Long deptId, String suiteId) {
        String cacheToken = getAccessToken(corpId, suiteId).getData();
        String url = "https://oapi.dingtalk.com/topapi/v2/department/get?access_token=" + cacheToken;
        Map<String, Object> argMap = Maps.newHashMap();
        argMap.put("dept_id", deptId);
        argMap.put("language", "zh_CN");
        String result = httpCloudManager.postUrl(url, argMap, createHeader());
        Integer errCode = JsonPath.read(result, "$.errcode");
        if (errCode.equals(DING_SUCCESS)) {
            DocumentContext parse = JsonPath.parse(result, CONFIGURATION);
            String deptName = parse.read("$.result.name");
            Dept dept = new Dept();
            Optional.ofNullable(parse.read("$.result.parent_id")).ifPresent(item -> {
                dept.setParentid(Long.valueOf(item.toString()));
            });
            //old:部门负责人，优先钉钉部门的主管 对应 CRM部门的负责人，如果主管没有值群主有值，则取群主
            //new:部门负责人，只取钉钉部门的主管 对应 CRM部门的负责人
            List<String> deptManagerUserIdList = JSONArray.parseArray(JSONArray.toJSONString(parse.read("$.result.dept_manager_userid_list")), String.class);
            String owner = null;
            if(CollectionUtils.isNotEmpty(deptManagerUserIdList)) {
                owner = deptManagerUserIdList.get(0);
            }
//            else {
//                owner = parse.read("$.result.org_dept_owner");
//            }
            dept.setName(deptName);
            dept.setDeptOwner(owner);
            dept.setId(deptId);
            return dept;
        }
        return null;
    }

    //获取上级部门信息
    public List<Dept> queryDeptParents(String dingCorpId, Long deptId, String suiteId) {
        String cacheToken = getAccessToken(dingCorpId, suiteId).getData();
        String url = "https://oapi.dingtalk.com/topapi/v2/department/listparentbydept?access_token=" + cacheToken;
        Map<String, Object> argMap = Maps.newHashMap();
        argMap.put("dept_id", deptId);
        String result = httpCloudManager.postUrl(url, argMap, createHeader());
        Integer errCode = JsonPath.read(result, "$.errcode");
        List<Integer> parentIds = Lists.newArrayList();
        List<Dept> deptList = Lists.newArrayList();
        if (errCode.equals(DING_SUCCESS)) {
            parentIds = JsonPath.read(result, "$.result.parent_id_list[*]");
            //翻转
            Collections.reverse(parentIds);
            for (Integer parentId : parentIds) {
                Dept dept = queryDeptDetail(dingCorpId, Long.valueOf(parentId), suiteId);
                if (ObjectUtils.isEmpty(dept)) continue;
                deptList.add(dept);
            }
        }
        return deptList;
    }


    private static String convert(String managerList) {
        if (StringUtils.isNotEmpty(managerList)) {
            String[] split = managerList.split("\\|");
            return split[0];
        }
        return "";
    }

    /**
     * 获取部门人员的信息
     */
    public List<EmployeeDingVo> queryUserByDept(String corpId, Long deptId, String suiteId) {
        String cacheToken = getAccessToken(corpId, suiteId).getData();
        DingUserModel dingUserModel = new DingUserModel();
        List<EmployeeDingVo> dingDeptUser = Lists.newArrayList();
        Long cursor = 0L;
        do {
            String url = "https://oapi.dingtalk.com/topapi/v2/user/list?access_token=" + cacheToken;
            Map<String, Object> argMap = Maps.newHashMap();
            argMap.put("dept_id", deptId);
            argMap.put("cursor", cursor);
            argMap.put("size", 100L);
            String result = httpCloudManager.postUrl(url, argMap, createHeader());
            dingUserModel = JSONObject.parseObject(JSONPath.read(result, "$.result").toString(), new TypeReference<DingUserModel>() {
            });
            dingUserModel.getList().stream().forEach(item -> {
                EmployeeDingVo user = new EmployeeDingVo();
                user.setUserid(item.getUserId());
                user.setUnionid(item.getUnionid());
                user.setName(item.getName());
                user.setMobile(item.getMobile());
                user.setDepartment(item.getDeptIdList());
                dingDeptUser.add(user);
            });
            cursor = dingUserModel.getNextCursor();
        } while (dingUserModel.getHasMore());
        return dingDeptUser;
    }


    /**
     * 发送卡片消息
     */
    public Result<String> sendCardMessage(Long agentId,
                                          String dingCorpId,
                                          Map<String, Object> dingMessageArg,
                                          String templateId,
                                          Boolean isCardMessage,
                                          String suiteId) {
        String cacheToken = getAccessToken(dingCorpId, suiteId).getData();
        String sendMessageUrl = "https://oapi.dingtalk.com/topapi/message/corpconversation/sendbytemplate?access_token=" + cacheToken;
        Map<String, Object> argMap = Maps.newHashMap();
        argMap.put("agent_id", agentId);
        argMap.put("template_id", templateId);
        Map<String, Object> dataMap = Maps.newHashMap();
        if (isCardMessage) {
            dataMap.put("markdown", dingMessageArg.get("markdown"));
            dataMap.put("ei", dingMessageArg.get("ei").toString());
            dataMap.put("apiname", dingMessageArg.get("apiname"));
            dataMap.put("instanceId", dingMessageArg.get("instanceId"));
            dataMap.put("taskId", dingMessageArg.get("taskId").toString());
            dataMap.put("objectId", dingMessageArg.get("objectId"));
            dataMap.put("url", dingMessageArg.get("url"));
            dataMap.put("bizType", dingMessageArg.get("bizType"));
            argMap.put("data", JSONObject.toJSONString(dataMap));
        } else {
            dataMap.put("message", dingMessageArg.get("message"));
            argMap.put("data", JSONObject.toJSONString(dataMap));

        }
        argMap.put("userid_list", dingMessageArg.get("userIdList"));
        String result = httpCloudManager.postUrl(sendMessageUrl, argMap, createHeader());
        log.info("send cardMessage arg:{},result:{}", argMap, result);
        return Result.newSuccess(result);
    }

    public Result<String> sendCardMessage(SendCardMessageArg arg) {
        return sendCardMessage(arg.getAgentId(),
                arg.getDingCorpId(),
                arg.getDingMessageArg(),
                arg.getTemplateId(),
                arg.getIsCardMessage(),
                arg.getSuiteId());
    }

    /**
     * 支持其他业务应用支持的发送消息
     */
    public Result<String> supportSendMessage(Long agentId, String dingCorpId, Map<String, Object> dingMessageArg, String templateId, String suiteId) {
        String cacheToken = getAccessToken(dingCorpId, suiteId).getData();
        String sendMessageUrl = "https://oapi.dingtalk.com/topapi/message/corpconversation/sendbytemplate?access_token=" + cacheToken;
        Map<String, Object> argMap = Maps.newHashMap();
        argMap.put("agent_id", agentId);
        argMap.put("template_id", templateId);
        argMap.put("data", JSONObject.toJSONString(dingMessageArg));
        argMap.put("userid_list", dingMessageArg.get("userIdList"));
        String result = httpCloudManager.postUrl(sendMessageUrl, argMap, createHeader());
        log.info("supportSendMessage send cardMessage arg:{},result:{}", argMap, result);
        return Result.newSuccess(result);
    }

    /**
     * 获取角色列表
     */
    public Result<String> queryRoleList(String dingCorpId, String suiteId) {
        String cacheToken = getAccessToken(dingCorpId, suiteId).getData();
        String getRole = "https://oapi.dingtalk.com/topapi/role/list?access_token=" + cacheToken;
        Map<String, Object> argMap = Maps.newHashMap();
        argMap.put("size", 100);
        argMap.put("offset", 0);
        String result = httpCloudManager.postUrl(getRole, argMap, createHeader());
        log.info("result");
        return Result.newSuccess(result);
    }

    public CreateTodoResult createTodo(CreateTodoArg createTodoArg) {

        log.info("DingManager.createTodo external todo message arg={}.", createTodoArg);
        CreateTodoResult result = new CreateTodoResult();
        result.setCode(200);
        result.setMessage("发送成功");
        Map<String, Object> dingTalk = Maps.newHashMap();
        dingTalk.put("sourceId", createTodoArg.getSourceId());
        Result<List<DingCorpMappingVo>> mappingResult = corpMappingService.queryByEi(createTodoArg.getEi());
        if(CollectionUtils.isEmpty(mappingResult.getData())) {
            log.info("DingManager.createTodo,ea not bind createTodoArg={}.", createTodoArg);
            return result;
        }
        String dingCorpId = mappingResult.getData().get(0).getDingCorpId();
        Result<List<AppAuthResult>> appAuthResult = appAuthService.conditionAppAuth(dingCorpId, ConfigCenter.APP_CRM_ID, null);
        // 需要判断是否购买了纷享钉钉CRM。其他应用的不发送
        if (CollectionUtils.isEmpty(appAuthResult.getData())) {
            log.info("DingManager.createTodo,enterprise not support send createTodoArg={}.", createTodoArg);
            return result;
        }

        //获取token
        String accessToken = getAccessToken(dingCorpId, ConfigCenter.CRM_SUITE_ID).getData();
        Map<String, String> dingTalkToken = Maps.newHashMap();
        dingTalkToken.put("x-acs-dingtalk-access-token", accessToken);
        if(StringUtils.isEmpty(accessToken)) {
            log.info("DingManager.createTodo,dingTalkToken is null,createTodoArg={}.", createTodoArg);
            return result;
        }

        //标志
        Boolean flag = false;

        //获取人员详细信息
        Result<List<DingMappingEmployeeResult>> employeeInfosResult = objectMappingService.batchQueryMapping(createTodoArg.getEi(), createTodoArg.getReceiverIds());
        if(CollectionUtils.isEmpty(employeeInfosResult.getData())) {
            log.info("DingManager.createTodo,user not bind,createTodoArg={}.", createTodoArg);
            return result;
        }
        List<DingMappingEmployeeResult> employeeInfos = employeeInfosResult.getData();
        int operatorId = employeeInfos.get(0).getEmployeeId();
        String operatorUnionId = employeeInfos.get(0).getDingUnionId();
        List<String> userUnionIds = new LinkedList<>();
        List<Integer> userIds = new LinkedList<>();
        for (int i = 0; i < employeeInfos.size(); i++) {
            userUnionIds.add(employeeInfos.get(i).getDingUnionId());
            userIds.add(employeeInfos.get(i).getEmployeeId());
        }

        //查看代办详情
        String dealTalkUrl = DINGTALK_TODO + operatorUnionId + "/tasks/sources/" + createTodoArg.getSourceId();
        HttpResponseMessage messageResult = OkHttp3MonitorUtils4Cloud.sendOkHttp3Get(dealTalkUrl, dingTalkToken, new HashMap<>());
        JSONObject jsonObject = JSONObject.parseObject(messageResult.getContent());
        log.info("DingManager.createTodo,createTalkUrl={},messageResult={}.", dealTalkUrl, messageResult);
        if(messageResult.getStatusCode() != 200) {
            log.warn("DingManager.createTodo,messageResult is error,arg={}.", createTodoArg);
            return result;
        }

        String talkId = String.valueOf(jsonObject.get("id"));
        if(StringUtils.isEmpty(talkId) || "null".equals(talkId)) {
            //找不到代办，新建代办
            //优先级
            int priority = 20;
            //优化卡片消息
            StringBuilder markdown = new StringBuilder();
            StringBuilder subject = new StringBuilder();
            StringBuilder description = new StringBuilder();
            if(StringUtils.isNotEmpty(createTodoArg.getTitle())) {
                subject.append(createTodoArg.getTitle()).append("-");
            }
            description.append(createTodoArg.getContent()).append("-");
            if (CollectionUtils.isNotEmpty(createTodoArg.getForm())) {
                for (int i = 0; i < createTodoArg.getForm().size(); i++) {
                    markdown.append("【").append(createTodoArg.getForm().get(i).getKey()).append(": ").append(createTodoArg.getForm().get(i).getValue()).append("】");
                    if (i == 1) {
                        subject.append(markdown);
                    }
                }
            }
            description.append(markdown);
            dingTalk.put("subject", subject.toString());
            dingTalk.put("description", description.toString());

            //跳转
            String objectApiName = createTodoArg.getExtraDataMap().get("objectApiName");
            String objectId = createTodoArg.getExtraDataMap().get("objectId");
            String instanceId = createTodoArg.getExtraDataMap().get("workflowInstanceId");
            String directUri = MID_URL + "?ei=" + createTodoArg.getEi() + "&apiname=" + objectApiName
                    + "&objectId=" + objectId + "&apiname=" + objectApiName + "&instanceId=" + instanceId + "&taskId=" + createTodoArg.getGenerateUrlType() + "&bizType=" + createTodoArg.getBizType();
            StringBuilder stringBuilder = new StringBuilder();
            AppParams appParams = ConfigCenter.APP_PARAMS_MAP.get(ConfigCenter.CRM_SUITE_ID);
            String suiteKey = appParams.getSuiteKey();
            stringBuilder.append(DING_SINGLE_URL).append("&appid=").append(suiteKey).append("&redirect_uri=").append(URLEncoder.encode(directUri));
            Map<String, String> detailUrl = Maps.newHashMap();
            detailUrl.put("appUrl", stringBuilder.toString());
            String pcUrl = String.format(DING_PC_SLIDE_URL, URLEncoder.encode(stringBuilder.toString()));
            detailUrl.put("pcUrl", pcUrl);
            dingTalk.put("creatorId", operatorUnionId);
            dingTalk.put("detailUrl", detailUrl);
            //设置代办任务仅出现在处理人当中，创建者的钉钉就不会出现不需要处理的代办
            boolean isOnlyShowExecutor = true;
            dingTalk.put("isOnlyShowExecutor", isOnlyShowExecutor);
            dingTalk.put("priority", priority);
            dingTalk.put("executorIds", userUnionIds);
            dingTalk.put("participantIds", userUnionIds);
            //设置截止时间，钉钉代办可以显示飘数，约定截止时间为当天的23:59:59，过期也可以显示飘数，不影响页面的跳转
            try {
                String endTime = DateFormatUtils.format(new Date(), "yyyy-MM-dd 23:59:59");
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date datetime = sdf.parse(endTime);//将你的日期转换为时间戳
                long time = datetime.getTime();
                dingTalk.put("dueTime", time);
            } catch (ParseException e) {
                e.printStackTrace();
            }
            //调用钉钉代办第三方接口
            Gson gson = new Gson();
            String dingTalkUrl = DINGTALK_TODO + operatorUnionId + "/tasks?operatorId=" + operatorUnionId;
            HttpResponseMessage CreateMessageResult = OkHttp3MonitorUtils4Cloud.sendOkHttp3Post(dingTalkUrl, dingTalkToken, gson.toJson(dingTalk));
            log.info("DingManager.createTodo,create.ea={},dingTalk={},updateToDo={},CreateMessageResult={}.", createTodoArg.getEa(), dingTalk, dingTalkUrl, CreateMessageResult);
            if (CreateMessageResult.getStatusCode() == 200) {
                JSONObject createJsonObject = JSONObject.parseObject(CreateMessageResult.getContent());
                log.warn("ExternalOaTodoServiceImpl.createTodo: 新增代办成功，ea={}.", createTodoArg.getEa());
                for (int i = 0; i < userIds.size(); i++) {
                    DingTaskVo dingTaskVo = new DingTaskVo();
                    dingTaskVo.setEi(createTodoArg.getEi());
                    dingTaskVo.setEmployeeId(userIds.get(i));
                    dingTaskVo.setSourceId(createTodoArg.getSourceId());
                    dingTaskVo.setCreatorEmployeeId(operatorId);
                    dingTaskVo.setTaskId(String.valueOf(createJsonObject.get("id")));
                    int update = objectMappingService.insertSource(dingTaskVo).getData();
                    if (update >= 1) {
                        log.info("DingManager.createTodo: 保存关系映射成功， dingTaskVo={}.", dingTaskVo);
                    }
                }
            } else {
                log.warn("DingManager.createTodo: 新增代办失败，ea={}，messageArg={}，userIds={}.", createTodoArg.getEa(), dingTalk, userIds);
            }
        } else {
            //代办已经存在，更新代办

            String executorIdJson = JSONArray.toJSONString(jsonObject.get("executorIds"));
            // 将json字符串转换为list集合
            List<String> executorIds = CollectionUtils.isNotEmpty(JSONArray.parseArray(executorIdJson, String.class)) ? JSONArray.parseArray(executorIdJson, String.class) : new LinkedList<>();
            executorIds.addAll(userUnionIds);
            String participantIdJson = JSONArray.toJSONString(jsonObject.get("participantIds"));
            // 将json字符串转换为list集合
            List<String> participantIds = CollectionUtils.isNotEmpty(JSONArray.parseArray(participantIdJson, String.class)) ? JSONArray.parseArray(participantIdJson, String.class) : new LinkedList<>();
            participantIds.addAll(userUnionIds);
            dingTalk.put("done", flag);
            dingTalk.put("executorIds", executorIds);
            dingTalk.put("participantIds", participantIds);
            String updateToDo = DINGTALK_TODO + operatorUnionId + "/tasks/" + talkId + "?operatorId=" + operatorUnionId;
            Gson gson = new Gson();
            HttpResponseMessage updateToDoMessageResult = OkHttp3MonitorUtils4Cloud.sendOkHttp3Put(updateToDo, dingTalkToken, gson.toJson(dingTalk));
            log.info("DingManager.createTodo,update.ea={},dingTalk={},updateToDo={},updateToDoMessageResult={}.", createTodoArg.getEa(), dingTalk, updateToDo, updateToDoMessageResult);
            if (updateToDoMessageResult.getStatusCode() == 200) {
                log.warn("DingManager.createTodo: 更新代办成功，creatorEmployeeId={}更新代办成功.", operatorId);
                for (int i = 0; i < userIds.size(); i++) {
                    DingTaskVo dingTaskVo = new DingTaskVo();
                        dingTaskVo.setEi(createTodoArg.getEi());
                        dingTaskVo.setEmployeeId(userIds.get(i));
                        dingTaskVo.setSourceId(createTodoArg.getSourceId());
                        dingTaskVo.setCreatorEmployeeId(operatorId);
                        dingTaskVo.setTaskId(String.valueOf(jsonObject.get("id")));
                        int update = objectMappingService.insertSource(dingTaskVo).getData();
                    if (update >= 1) {
                        log.info("DingManager.createTodo: 保存关系映射成功， dingTaskVo={}.", dingTaskVo);
                    }
                }
            } else {
                log.warn("DingManager.createTodo: 新增代办失败，messageArg={}，userId={}.", dingTalk, userIds);
            }
        }
        return result;
    }

    /**
     * 发送卡片消息
     */
    public Result<String> robotSendCardMessage(String dingCorpId, String suiteId, ChatEventModel chatEventModel) {
        String messageUrl = "https://api.dingtalk.com/v1.0/im/interactiveCards/send";
        String cacheToken = getAccessToken(dingCorpId, suiteId).getData();
        String result = null;
        try {
            Map<String, String> hearMap = Maps.newHashMap();
            hearMap.put("x-acs-dingtalk-access-token", cacheToken);
            RobotCardMessageArg robotCardMessageArg = new RobotCardMessageArg();
            robotCardMessageArg.setCardTemplateId(ConfigCenter.CARD_TEMPLATE_ID);
            robotCardMessageArg.setOpenConversationId(chatEventModel.getOpenConversationId());
            String uuId = UUID.randomUUID().toString().replace("-", "").substring(0, 10);
            robotCardMessageArg.setOutTrackId(dingCorpId + uuId);
            robotCardMessageArg.setChatBotId(ConfigCenter.ROBOT_CODE);
            robotCardMessageArg.setConversationType(1);//群聊
            RobotCardMessageArg.CardData cardData = new RobotCardMessageArg.CardData();
            Map<String, String> paramsMap = Maps.newHashMap();
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // new Date()为获取当前系统时间
            String dateTime = df.format(new Date());
            paramsMap.put("date", dateTime);
            String ret = "dingtalk://dingtalkclient/page/link?dd_mode=push&pc_slide=true&url="
                    + URLEncoder.encode(ConfigCenter.CHAT_REDIRECT_URL, "UTF-8");
            paramsMap.put("url", ret);
            cardData.setCardParamMap(paramsMap);
            robotCardMessageArg.setCardData(cardData);
            result = httpCloudManager.postUrl(messageUrl, robotCardMessageArg, hearMap);
        } catch (UnsupportedEncodingException e) {
            log.info("exception message:{}", e.getMessage());
            e.printStackTrace();
        }
        log.info("result:{}", result);
        return Result.newSuccess();
    }

    public Result<String> commonConvertObj(String dingCorpId, String suiteId, Long lastSyncTime, String cursor, Long pageSize, String dingApiName) {
        switch (dingApiName) {
            case "crm_customer":
                return getDingObjList(dingCorpId, suiteId, lastSyncTime, cursor, pageSize, dingApiName);
            case "crm_contact":
                return getDingObjList(dingCorpId, suiteId, lastSyncTime, cursor, pageSize, dingApiName);
            case "crm_customer_personal":
                return getDingObjListNewVersion2(dingCorpId, suiteId, lastSyncTime, cursor, pageSize, dingApiName);
            default:
                return null;
        }
    }


    /**
     * 获取钉钉crm历史数据,支持客户，联系人
     */

    public Result<String> getDingObjList(String dingCorpId, String suiteId, Long lastSyncTime, String cursor, Long pageSize, String dingApiName) {
        String cacheToken = getAccessToken(dingCorpId, suiteId).getData();
        String preObjUrl = getObjUrl(dingApiName);
        String dingCustomerUrl = preObjUrl.concat("?access_token=").concat(cacheToken);
        QueryDingObjArg queryDingObjArg = new QueryDingObjArg();
        queryDingObjArg.setCursor(cursor);
        queryDingObjArg.setPage_size(pageSize);
        QueryDingObjArg.QueryDslArg queryDslArg = new QueryDingObjArg.QueryDslArg();
        queryDslArg.setLogicType("ADD");
        QueryDingObjArg.FilterArg filterArg = new QueryDingObjArg.FilterArg();
        filterArg.setFieldId("gmt_create");
        filterArg.setFilterType("BETWEEN");
        filterArg.setValue(Lists.newArrayList(lastSyncTime, System.currentTimeMillis()));
        queryDslArg.setQueryObjectList(Lists.newArrayList(filterArg));
        Map<String, Object> queryMap = Maps.newHashMap();
        queryMap.put("queryGroupList", Lists.newArrayList(queryDslArg));
        queryMap.put("order", "asc");
        queryMap.put("orderFieldId", "gmt_create");
        queryDingObjArg.setQuery_dsl(JSONObject.toJSONString(queryMap));
        String result = httpCloudManager.postUrl(dingCustomerUrl, queryDingObjArg, createHeader());
        log.info("getDingObjList result:{}", result);
        return Result.newSuccess(result);
    }

    /**
     * 支持新版查询对象的数据，个人客户
     *
     * @param dingCorpId
     * @param suiteId
     * @param lastSyncTime
     * @param cursor
     * @param pageSize
     * @param dingApiName
     * @return
     */
    public Result<String> getDingObjListNewVersion2(String dingCorpId, String suiteId, Long lastSyncTime, String cursor, Long pageSize, String dingApiName) {
        String cacheToken = getAccessToken(dingCorpId, suiteId).getData();
        Map<String, String> hearToken = Maps.newHashMap();
        hearToken.put("x-acs-dingtalk-access-token", cacheToken);
        NewQueryDingObjArg queryDingObjArg = new NewQueryDingObjArg();
        NewQueryDingObjArg.QueryDslArg queryDslArg = new NewQueryDingObjArg.QueryDslArg();
        queryDslArg.setLogicType("ADD");
        NewQueryDingObjArg.FilterArg filterArg = new NewQueryDingObjArg.FilterArg();
        filterArg.setFieldId("gmt_create");
        filterArg.setFilterType("BETWEEN");
        filterArg.setValue(Lists.newArrayList(lastSyncTime, System.currentTimeMillis()));
        queryDslArg.setQueryObjectList(Lists.newArrayList(filterArg));
        Map<String, Object> queryMap = Maps.newHashMap();
        queryMap.put("queryGroupList", Lists.newArrayList(queryDslArg));
        queryMap.put("order", "asc");
        queryMap.put("orderFieldId", "gmt_create");
        String queryResult = JSONObject.toJSONString(queryMap);
        String dingCustomerUrl = "https://api.dingtalk.com//v1.0/crm/personalCustomers?nextToken=" + cursor + "&maxResults=" + pageSize + "&queryDsl=" + queryResult;
        String result = httpCloudManager.getUrl(dingCustomerUrl, hearToken);
        log.info("getDingObjListNewVersion2 result:{}", result);
        return Result.newSuccess(result);
    }


    private String getObjUrl(String dingApiName) {
        switch (dingApiName) {
            case "crm_customer":
                return "https://oapi.dingtalk.com/topapi/crm/objectdata/customer/query";
            case "crm_contact":
                return "https://oapi.dingtalk.com/topapi/crm/objectdata/contact/query";
            case "crm_customer_personal":
                return "https://api.dingtalk.com//v1.0/crm/personalCustomers/batchQuery";
            default:
                return "";
        }
    }


    /**
     * 获取客户的元数据字段
     */
    public String getCustomerDescribe(String dingCorpId, String suiteId) {
        String cacheToken = getAccessToken(dingCorpId, suiteId).getData();
        String getCustomerUrl = "https://oapi.dingtalk.com/topapi/crm/objectmeta/customer/describe?access_token=" + cacheToken;
        String result = httpCloudManager.postUrl(getCustomerUrl, Maps.newHashMap(), createHeader());
        log.info("get customer describe result:{}", result);
        return result;
    }


    /**
     * 获取联系人的元数据字段
     */
    public String getContactDescribe(String dingCorpId, String suiteId) {
        String cacheToken = getAccessToken(dingCorpId, suiteId).getData();
        String getCustomerUrl = "https://oapi.dingtalk.com/topapi/crm/objectmeta/contact/describe?access_token=" + cacheToken;
        String result = httpCloudManager.postUrl(getCustomerUrl, Maps.newHashMap(), createHeader());
        log.info("get customer describe result:{}", result);
        return result;
    }

    /**
     * 根据id获取客户的数据
     */
    public String queryCustomerDataById(String dingCorpId, String suiteId, String dingDataId) {
        String cacheToken = getAccessToken(dingCorpId, suiteId).getData();
        String url = "https://oapi.dingtalk.com/topapi/crm/objectdata/customer/list?access_token=" + cacheToken;
        Map<String, String> dataMap = Maps.newHashMap();
        dataMap.put("data_id_list", dingDataId);
        String result = httpCloudManager.postUrl(url, dataMap, createHeader());
        return result;
    }

    /**
     * 根据id获取联系人的数据
     */
    public String queryContactDataById(String dingCorpId, String suiteId, String dingDataId) {
        String cacheToken = getAccessToken(dingCorpId, suiteId).getData();
        String url = "https://oapi.dingtalk.com/topapi/crm/objectdata/contact/list?access_token=" + cacheToken;
        Map<String, String> dataMap = Maps.newHashMap();
        dataMap.put("data_id_list", dingDataId);
        String result = httpCloudManager.postUrl(url, dataMap, createHeader());
        return result;
    }

    /**
     * 创建客户
     */
    public String createDingCustomer(String dingCorpId, String suiteId, String data, Integer ei) {
        String cacheToken = getAccessToken(dingCorpId, suiteId).getData();
        String url = "https://oapi.dingtalk.com/topapi/crm/objectdata/customer/create?access_token=" + cacheToken;
        SyncCreateCustomerArg syncCreateCustomerArg = JSONObject.parseObject(data, new TypeReference<SyncCreateCustomerArg>() {
        });
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("creator_userid", syncCreateCustomerArg.getCreator_user_id());
        Result<DingMappingEmployeeResult> dingMappingEmployeeResultResult = objectMappingService.queryEmpByDingUserId(ei, syncCreateCustomerArg.getCreator_user_id());
        String nickName = dingMappingEmployeeResultResult.getData().getDingEmployeeName();
        dataMap.put("data", data);
        dataMap.put("creator_nick", nickName);
        Map<String, Object> ownerMap = Maps.newHashMap();
        ownerMap.put("owner_userids", Lists.newArrayList(syncCreateCustomerArg.getCreator_user_id()));
        ownerMap.put("participant_userids", Lists.newArrayList(syncCreateCustomerArg.getCreator_user_id()));
        dataMap.put("permission", ownerMap);
        String result = httpCloudManager.postUrl(url, dataMap, createHeader());
        //TODO 创建成功后，落库
        return null;

    }

    //创建联系人
    public String createDingConcat(String dingCorpId, String suiteId, String data, Integer ei) {
        String cacheToken = getAccessToken(dingCorpId, suiteId).getData();

        String url = "https://oapi.dingtalk.com/topapi/crm/objectdata/contact/create?access_token=" + cacheToken;
        SyncCreateCustomerArg syncCreateCustomerArg = JSONObject.parseObject(data, new TypeReference<SyncCreateCustomerArg>() {
        });
        Result<DingMappingEmployeeResult> dingMappingEmployeeResultResult = objectMappingService.queryEmpByDingUserId(ei, syncCreateCustomerArg.getCreator_user_id());
        String nickName = dingMappingEmployeeResultResult.getData().getDingEmployeeName();

        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("creator_userid", syncCreateCustomerArg.getCreator_user_id());
        dataMap.put("creator_nick", nickName);
        dataMap.put("data", data);
        Map<String, Object> ownerMap = Maps.newHashMap();
        ownerMap.put("owner_userids", Lists.newArrayList(syncCreateCustomerArg.getCreator_user_id()));
        ownerMap.put("participant_userids", Lists.newArrayList(syncCreateCustomerArg.getCreator_user_id()));
        dataMap.put("permission", ownerMap);
        String result = httpCloudManager.postUrl(url, dataMap, createHeader());
        //TODO 创建成功后，落库
        return null;

    }


    //创建个人客户
    public Result<String> createPersonalCustomer(String dingCorpId, String suiteId, Map<String, Object> dataMap, Integer ei) {
        String cacheToken = getAccessToken(dingCorpId, suiteId).getData();

        String url = "https://api.dingtalk.com/v1.0/crm/personalCustomers?access_token=" + cacheToken;

        Result<DingMappingEmployeeResult> dingMappingEmployeeResultResult = objectMappingService.queryEmpByDingUserId(ei, dataMap.get("creator_userid").toString());
        String nickName="";
        if (dingMappingEmployeeResultResult.getData()!=null){
            nickName = dingMappingEmployeeResultResult.getData().getDingEmployeeName();
        }

        //前置判断一下，
        if (ObjectUtils.isEmpty(dataMap.get("customer_phone"))) {
            Object replacePhone = dataMap.get("tel");
            dataMap.put("customer_phone", replacePhone);//可能只有tel有数据
        }
        //综合customer_name
        String complexCustomerName = convertCustomerName(dataMap);
        dataMap.put("customer_name",complexCustomerName);
        Map<String, Object> objectMap = dataConvertManager.convertData2Ding(dataMap, DingObjTypeEnum.CRM_PERSONAL_CONTACT_TYPE.getDingObjApiName());
        objectMap.put("customer_follow_up_status", "{\"extendValue\":\"{\\\"extension\\\":{\\\"editFreeze\\\":true},\\\"label\\\":\\\"新获取\\\",\\\"key\\\":\\\"option_new_acquisition\\\"}\",\"value\":\"新获取\"}");

        Map<String, Object> personalCustomerMap = Maps.newHashMap();
        personalCustomerMap.put("creatorUserId", dataMap.get("creator_userid"));
        personalCustomerMap.put("creator_nick", nickName);
        personalCustomerMap.put("data", objectMap);

        Map<String, Object> ownerMap = Maps.newHashMap();
        ownerMap.put("ownerStaffIds", Lists.newArrayList(dataMap.get("creator_userid")));
        ownerMap.put("participantStaffIds", Lists.newArrayList(dataMap.get("creator_userid")));
        personalCustomerMap.put("permission", ownerMap);
        Map<String, String> hearMap = Maps.newHashMap();
        hearMap.put("x-acs-dingtalk-access-token", cacheToken);
        String data = JSONObject.toJSONString(personalCustomerMap);
        String result = httpCloudManager.postUrl(url, data, hearMap);
        Map<String, Object> resultMap = JSONObject.parseObject(result, new TypeReference<Map>() {
        });
        if (!ObjectUtils.isEmpty(resultMap.get("instanceId"))) {
            DingObjSyncVo dingObjSyncVo = new DingObjSyncVo();
            dingObjSyncVo.setCrmApiName("LeadsObj");
            dingObjSyncVo.setDingApiName("crm_customer_personal");
            dingObjSyncVo.setCrmObjId("");
            dingObjSyncVo.setDingCorpId(dingCorpId);
            dingObjSyncVo.setTenantId(ei);
            dingObjSyncVo.setDingObjId(resultMap.get("instanceId").toString());
            dingObjSyncService.insertObjSync(dingObjSyncVo);
            return Result.newSuccess(resultMap.get("instanceId").toString());
        }
        //TODO 创建成功后，落库
        //{
        //    "code": "InvalidAuthentication",
        //    "requestid": "3D249C0F-33AF-7D92-B0B1-C0AAE80F5967",
        //    "message": "不合法的access_token"
        //}
        return Result.newError(ResultCode.SERVER_BUSY.getErrorCode(), resultMap.get("message").toString());

    }


    private String convertCustomerName(Map<String, Object> dataMap) {
        StringBuilder complexCustomerName = new StringBuilder();
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(dataMap.get("customer_name"))) {
            complexCustomerName.append(dataMap.get("customer_name").toString());
        }
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(dataMap.get("company"))) {
            complexCustomerName.append("-").append(dataMap.get("company").toString());
        }
        if (org.apache.commons.lang3.ObjectUtils.isNotEmpty(dataMap.get("account_name"))) {
            complexCustomerName.append("-").append(dataMap.get("account_name").toString());
        }
        if (StringUtils.isEmpty(complexCustomerName.toString())) {
            complexCustomerName .append("未知") ;
        }
        //去掉首尾-；
        String finalCustomerName = complexCustomerName.toString().startsWith("-") ? complexCustomerName.substring(1) : complexCustomerName.toString();
        return finalCustomerName;
    }


}
